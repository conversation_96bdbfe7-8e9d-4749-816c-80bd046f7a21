import realApiService from './realApiService';

/**
 * Team service for managing team members and related operations
 */

// Get team members for an organization
export const getTeamMembers = async (organizationId, filters = {}) => {
  try {
    console.log('Fetching team members for organization:', organizationId);
    
    // Use the organization members endpoint
    const response = await realApiService.api.get(`/organizations/${organizationId}/members`, {
      params: {
        page: filters.page || 1,
        limit: filters.limit || 50,
        search: filters.search,
        role: filters.role
      }
    });

    if (response.data && response.data.success) {
      const members = response.data.data || [];
      
      // Transform the data to match frontend expectations
      const transformedMembers = members.map(member => ({
        id: member.user?.id || member.user_id,
        name: `${member.user?.first_name || ''} ${member.user?.last_name || ''}`.trim() || 
              member.user?.email?.split('@')[0] || 'User',
        email: member.user?.email || '',
        role: member.role || 'member',
        status: member.status || 'active',
        avatar: member.user?.avatar_url || member.user?.avatar || '/assets/images/avatar.jpg',
        lastActivity: member.last_active_at ? new Date(member.last_active_at) : new Date(),
        joinedDate: member.joined_at ? new Date(member.joined_at) : new Date(),
        department: member.department || '',
        currentTask: member.current_task || '',
        tasksCompleted: member.tasks_completed || 0,
        tasksAssigned: member.tasks_assigned || 0
      }));

      console.log('Transformed team members:', transformedMembers);
      return transformedMembers;
    }

    console.warn('No team members data received');
    return [];
  } catch (error) {
    console.error('Failed to fetch team members:', error);
    
    // Return empty array instead of mock data
    return [];
  }
};

// Get member activity
export const getMemberActivity = async (organizationId, userId) => {
  try {
    const response = await realApiService.api.get(`/teams/${organizationId}/members/${userId}/activity`);
    
    if (response.data && response.data.success) {
      return response.data.data || [];
    }
    
    return [];
  } catch (error) {
    console.error('Failed to fetch member activity:', error);
    return [];
  }
};

// Invite team member
export const inviteTeamMember = async (organizationId, inviteData) => {
  try {
    console.log('Sending invite request:', { organizationId, inviteData });

    // Try the invite endpoint
    const response = await realApiService.api.post(`/organizations/${organizationId}/invite`, inviteData);

    console.log('Invite response:', response);

    if (response.data && response.data.success) {
      console.log('Invite successful:', response.data);
      return response.data;
    }

    throw new Error(response.data?.message || 'Failed to invite member');
  } catch (error) {
    console.error('Failed to invite team member:', error);

    // If the endpoint doesn't exist, try alternative endpoints
    if (error.response?.status === 404) {
      console.log('Trying alternative invite endpoint...');
      try {
        const altResponse = await realApiService.api.post(`/teams/${organizationId}/invite`, inviteData);
        console.log('Alternative invite response:', altResponse);
        return altResponse.data;
      } catch (altError) {
        console.error('Alternative invite also failed:', altError);
      }
    }

    throw error;
  }
};

// Update member role
export const updateMemberRole = async (organizationId, userId, roleData) => {
  try {
    const response = await realApiService.api.put(`/organizations/${organizationId}/members/${userId}/role`, roleData);
    
    if (response.data && response.data.success) {
      return response.data;
    }
    
    throw new Error(response.data?.message || 'Failed to update member role');
  } catch (error) {
    console.error('Failed to update member role:', error);
    throw error;
  }
};

// Remove team member
export const removeMember = async (organizationId, userId) => {
  try {
    const response = await realApiService.api.delete(`/organizations/${organizationId}/members/${userId}`);
    
    if (response.data && response.data.success) {
      return response.data;
    }
    
    throw new Error(response.data?.message || 'Failed to remove member');
  } catch (error) {
    console.error('Failed to remove member:', error);
    throw error;
  }
};

// Bulk member actions
export const bulkMemberAction = async (organizationId, action, memberIds, additionalData = {}) => {
  try {
    const response = await realApiService.api.post(`/teams/${organizationId}/members/bulk-action`, {
      action,
      member_ids: memberIds,
      ...additionalData
    });
    
    if (response.data && response.data.success) {
      return response.data;
    }
    
    throw new Error(response.data?.message || 'Failed to perform bulk action');
  } catch (error) {
    console.error('Failed to perform bulk action:', error);
    throw error;
  }
};

// Get team invitations
export const getTeamInvitations = async (organizationId) => {
  try {
    const response = await realApiService.api.get(`/organizations/${organizationId}/invitations`);
    
    if (response.data && response.data.success) {
      return response.data.data || [];
    }
    
    return [];
  } catch (error) {
    console.error('Failed to fetch team invitations:', error);
    return [];
  }
};

// Cancel invitation
export const cancelInvitation = async (organizationId, inviteId) => {
  try {
    const response = await realApiService.api.delete(`/organizations/${organizationId}/invitations/${inviteId}`);
    
    if (response.data && response.data.success) {
      return response.data;
    }
    
    throw new Error(response.data?.message || 'Failed to cancel invitation');
  } catch (error) {
    console.error('Failed to cancel invitation:', error);
    throw error;
  }
};

// Resend invitation
export const resendInvitation = async (organizationId, inviteId) => {
  try {
    const response = await realApiService.api.post(`/organizations/${organizationId}/invitations/${inviteId}/resend`);
    
    if (response.data && response.data.success) {
      return response.data;
    }
    
    throw new Error(response.data?.message || 'Failed to resend invitation');
  } catch (error) {
    console.error('Failed to resend invitation:', error);
    throw error;
  }
};

// Default export
const teamService = {
  getTeamMembers,
  getMemberActivity,
  inviteTeamMember,
  updateMemberRole,
  removeMember,
  bulkMemberAction,
  getTeamInvitations,
  cancelInvitation,
  resendInvitation
};

export default teamService;
