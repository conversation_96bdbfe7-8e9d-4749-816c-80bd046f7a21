{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\role-based-dashboard\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\nimport teamService from '../../utils/teamService';\nimport notificationService from '../../utils/notificationService';\nimport CreateProjectModal from '../../components/modals/CreateProjectModal';\nimport CreateOrganizationModal from '../../components/modals/CreateOrganizationModal';\nimport InviteMemberModal from '../../components/modals/InviteMemberModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleBasedDashboard = () => {\n  _s();\n  var _organizations$0$orga, _currentUser$email, _organizations$0$orga2, _currentUser$email2;\n  const location = useLocation();\n  const [userRole, setUserRole] = useState('member'); // Default role\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showWelcome, setShowWelcome] = useState(false);\n  const [welcomeMessage, setWelcomeMessage] = useState('');\n  const [showCreateProject, setShowCreateProject] = useState(false);\n  const [showCreateOrganization, setShowCreateOrganization] = useState(false);\n  const [showInviteMember, setShowInviteMember] = useState(false);\n\n  // Real team members data\n  const [teamMembers, setTeamMembers] = useState([]);\n\n  // Real notifications data\n  const [notifications, setNotifications] = useState([]);\n  const [notificationsLoading, setNotificationsLoading] = useState(true);\n\n  // Load dashboard data from backend\n  useEffect(() => {\n    const loadDashboardData = async () => {\n      try {\n        var _location$state, _location$state2;\n        setLoading(true);\n\n        // Check for welcome message from navigation state\n        if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message && ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.type) === 'success') {\n          setWelcomeMessage(location.state.message);\n          setShowWelcome(true);\n          // Clear the state to prevent showing on refresh\n          window.history.replaceState({}, document.title);\n        }\n\n        // Get current user and role\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || authService.getUserRole());\n          setOrganizations(userResult.data.organizations || []);\n        } else {\n          // If no user, set fallback data instead of redirecting\n          console.warn('No user data available, using fallback');\n          setCurrentUser({\n            id: 'fallback-user',\n            firstName: 'User',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'member'\n          });\n          setUserRole('member');\n          setOrganizations([{\n            id: 'fallback-org',\n            name: 'Default Organization',\n            domain: 'example.com'\n          }]);\n        }\n\n        // Get dashboard stats\n        const statsResult = await authService.getDashboardStats();\n        if (statsResult.data) {\n          setDashboardData(statsResult.data);\n        }\n\n        // Get projects\n        const organizationId = authService.getOrganizationId();\n        if (organizationId) {\n          const projectsResult = await apiService.projects.getAll(organizationId);\n          setProjects(projectsResult || []);\n\n          // Get team members\n          try {\n            const teamMembersResult = await teamService.getTeamMembers(organizationId);\n            setTeamMembers(teamMembersResult || []);\n          } catch (teamError) {\n            console.error('Failed to load team members:', teamError);\n            setTeamMembers([]); // Clear team members on error\n          }\n\n          // Get notifications and check for first-time user\n          try {\n            var _userResult$data$orga, _userResult$data$orga2;\n            setNotificationsLoading(true);\n            const notificationsResult = await notificationService.getNotifications({\n              limit: 10\n            });\n\n            // Handle the new response structure\n            if (notificationsResult && notificationsResult.data && Array.isArray(notificationsResult.data)) {\n              setNotifications(notificationsResult.data);\n            } else if (Array.isArray(notificationsResult)) {\n              setNotifications(notificationsResult);\n            } else {\n              setNotifications([]);\n            }\n\n            // Check if this is a first-time user and create welcome notification\n            const isFirstTime = await notificationService.checkFirstTimeUser();\n            if (isFirstTime && (_userResult$data$orga = userResult.data.organizations) !== null && _userResult$data$orga !== void 0 && (_userResult$data$orga2 = _userResult$data$orga[0]) !== null && _userResult$data$orga2 !== void 0 && _userResult$data$orga2.name) {\n              await notificationService.createWelcomeNotification(userResult.data.user.id, userResult.data.organizations[0].name);\n\n              // Reload notifications to include the welcome notification\n              const updatedNotifications = await notificationService.getNotifications({\n                limit: 10\n              });\n              if (updatedNotifications && updatedNotifications.data && Array.isArray(updatedNotifications.data)) {\n                setNotifications(updatedNotifications.data);\n              } else if (Array.isArray(updatedNotifications)) {\n                setNotifications(updatedNotifications);\n              } else {\n                setNotifications([]);\n              }\n            }\n          } catch (notificationError) {\n            console.error('Failed to load notifications:', notificationError);\n            setNotifications([]); // Clear notifications on error\n          } finally {\n            setNotificationsLoading(false);\n          }\n        }\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load dashboard data:', err);\n\n        // Don't set error state that would prevent the page from working\n        // Instead, set fallback data and let the page render\n        if (!currentUser) {\n          setCurrentUser({\n            id: 'fallback-user',\n            firstName: 'User',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'member'\n          });\n          setUserRole('member');\n        }\n        if (organizations.length === 0) {\n          setOrganizations([{\n            id: 'fallback-org',\n            name: 'Default Organization',\n            domain: 'example.com'\n          }]);\n        }\n\n        // Set empty arrays for other data\n        setProjects([]);\n        setTeamMembers([]);\n        setNotifications([]);\n        setNotificationsLoading(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadDashboardData();\n  }, [location.state]);\n\n  // Project creation handler\n  const handleCreateProject = async projectData => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n      console.log('Creating project with data:', projectData);\n      console.log('Organization ID:', organizationId);\n      const newProject = await apiService.projects.create(organizationId, projectData);\n      console.log('Project creation response:', newProject);\n\n      // Refresh projects list\n      console.log('Refreshing projects list...');\n      const projectsResult = await apiService.projects.getAll(organizationId);\n      console.log('Updated projects list:', projectsResult);\n      setProjects(projectsResult || []);\n\n      // Refresh dashboard stats\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n      console.log('Project created successfully:', newProject);\n    } catch (error) {\n      var _error$response;\n      console.error('Failed to create project:', error);\n      console.error('Error details:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n      throw error;\n    }\n  };\n\n  // Quick action handlers\n  const handleOpenCreateProject = () => {\n    setShowCreateProject(true);\n  };\n  const handleCloseCreateProject = () => {\n    setShowCreateProject(false);\n  };\n\n  // Organization creation handlers\n  const handleCreateOrganization = async (organizationData, logoFile) => {\n    try {\n      const result = await apiService.organizations.create(organizationData, logoFile);\n      if (result.error) {\n        throw new Error(result.error);\n      }\n\n      // Show success message\n      setWelcomeMessage(result.message || `Organization \"${organizationData.name}\" created successfully!`);\n      setShowWelcome(true);\n\n      // Refresh organizations list if needed\n      // You might want to update the current organization context here\n      console.log('Organization created successfully:', result.data);\n      return result.data;\n    } catch (error) {\n      console.error('Failed to create organization:', error);\n      throw error;\n    }\n  };\n  const handleOpenCreateOrganization = () => {\n    setShowCreateOrganization(true);\n  };\n  const handleCloseCreateOrganization = () => {\n    setShowCreateOrganization(false);\n  };\n  const handleManageUsers = () => {\n    // Navigate to team members page\n    window.location.href = '/team-members';\n  };\n  const handleInviteMembers = () => {\n    setShowInviteMember(true);\n  };\n  const handleCloseInviteMember = () => {\n    setShowInviteMember(false);\n  };\n  const handleSendInvitation = async inviteData => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n\n      // Call the backend API to send invitation\n      const response = await fetch(`http://localhost:3001/api/v1/organizations/${organizationId}/invite`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getAccessToken()}`\n        },\n        body: JSON.stringify({\n          email: inviteData.email,\n          role: inviteData.role\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to send invitation');\n      }\n      const result = await response.json();\n      console.log('Invitation sent successfully:', result);\n\n      // Refresh dashboard stats to show updated member count\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n    } catch (error) {\n      console.error('Failed to send invitation:', error);\n      throw error;\n    }\n  };\n\n  // Show loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600\",\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-red-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-lg mb-2\",\n          children: \"Failed to load dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600 text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Real projects data is loaded from API in useEffect\n\n  // Real activities and tasks data will be loaded from API when available\n\n  // Role-based KPI data with real data integration\n  const getKPIData = () => {\n    var _realData$total_organ, _realData$total_proje, _realData$total_membe, _realData$recent_acti, _realData$recent_acti2, _realData$total_proje2, _realData$total_membe2, _realData$total_organ2, _realData$recent_acti3, _realData$recent_acti4, _realData$total_proje3, _realData$total_organ3, _realData$total_membe3, _realData$recent_acti5, _realData$recent_acti6, _realData$total_proje4, _realData$total_organ4, _realData$total_membe4, _realData$recent_acti7, _realData$recent_acti8;\n    const realData = dashboardData || {};\n    switch (userRole) {\n      case 'owner':\n        return [{\n          title: \"Organizations\",\n          value: ((_realData$total_organ = realData.total_organizations) === null || _realData$total_organ === void 0 ? void 0 : _realData$total_organ.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Active Projects\",\n          value: ((_realData$total_proje = realData.total_projects) === null || _realData$total_proje === void 0 ? void 0 : _realData$total_proje.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe = realData.total_members) === null || _realData$total_membe === void 0 ? void 0 : _realData$total_membe.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti = realData.recent_activity) === null || _realData$recent_acti === void 0 ? void 0 : (_realData$recent_acti2 = _realData$recent_acti.length) === null || _realData$recent_acti2 === void 0 ? void 0 : _realData$recent_acti2.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'admin':\n        return [{\n          title: \"Active Projects\",\n          value: ((_realData$total_proje2 = realData.total_projects) === null || _realData$total_proje2 === void 0 ? void 0 : _realData$total_proje2.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe2 = realData.total_members) === null || _realData$total_membe2 === void 0 ? void 0 : _realData$total_membe2.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"success\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ2 = realData.total_organizations) === null || _realData$total_organ2 === void 0 ? void 0 : _realData$total_organ2.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti3 = realData.recent_activity) === null || _realData$recent_acti3 === void 0 ? void 0 : (_realData$recent_acti4 = _realData$recent_acti3.length) === null || _realData$recent_acti4 === void 0 ? void 0 : _realData$recent_acti4.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'member':\n        return [{\n          title: \"My Projects\",\n          value: ((_realData$total_proje3 = realData.total_projects) === null || _realData$total_proje3 === void 0 ? void 0 : _realData$total_proje3.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ3 = realData.total_organizations) === null || _realData$total_organ3 === void 0 ? void 0 : _realData$total_organ3.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe3 = realData.total_members) === null || _realData$total_membe3 === void 0 ? void 0 : _realData$total_membe3.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti5 = realData.recent_activity) === null || _realData$recent_acti5 === void 0 ? void 0 : (_realData$recent_acti6 = _realData$recent_acti5.length) === null || _realData$recent_acti6 === void 0 ? void 0 : _realData$recent_acti6.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'viewer':\n        return [{\n          title: \"Projects Viewed\",\n          value: ((_realData$total_proje4 = realData.total_projects) === null || _realData$total_proje4 === void 0 ? void 0 : _realData$total_proje4.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Eye\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ4 = realData.total_organizations) === null || _realData$total_organ4 === void 0 ? void 0 : _realData$total_organ4.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"accent\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe4 = realData.total_members) === null || _realData$total_membe4 === void 0 ? void 0 : _realData$total_membe4.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"success\"\n        }, {\n          title: \"Activity Items\",\n          value: ((_realData$recent_acti7 = realData.recent_activity) === null || _realData$recent_acti7 === void 0 ? void 0 : (_realData$recent_acti8 = _realData$recent_acti7.length) === null || _realData$recent_acti8 === void 0 ? void 0 : _realData$recent_acti8.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"BarChart3\",\n          color: \"warning\"\n        }];\n      default:\n        return [{\n          title: \"Projects\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Members\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Activity\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n    }\n  };\n\n  // Filter projects based on search, filter values, and role-based access\n  const filteredProjects = projects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) || (project.description || '').toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' || (project.status || 'active').toLowerCase() === filterValue.toLowerCase();\n\n    // Role-based project access control\n    const hasProjectAccess = () => {\n      if (!(currentUser !== null && currentUser !== void 0 && currentUser.role)) return true; // Default access if role not set\n\n      const userRole = currentUser.role.toLowerCase();\n\n      // Viewers should only see projects they are specifically invited to\n      if (userRole === 'viewer') {\n        // For now, viewers can see projects from their organization\n        // In a real implementation, this would check project-specific invitations\n        return project.organization_id === currentUser.organization_id;\n      }\n\n      // Members, admins, and owners can see all projects in their organization\n      return true;\n    };\n    return matchesSearch && matchesFilter && hasProjectAccess();\n  });\n\n  // Get current organization data\n  const currentOrganization = organizations.length > 0 ? {\n    name: ((_organizations$0$orga = organizations[0].organization) === null || _organizations$0$orga === void 0 ? void 0 : _organizations$0$orga.name) || 'Your Organization',\n    domain: (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$email = currentUser.email) === null || _currentUser$email === void 0 ? void 0 : _currentUser$email.split('@')[1]) || 'company.com',\n    logo: ((_organizations$0$orga2 = organizations[0].organization) === null || _organizations$0$orga2 === void 0 ? void 0 : _organizations$0$orga2.logo_url) || '/assets/images/org-logo.png'\n  } : {\n    name: 'Your Organization',\n    domain: 'company.com',\n    logo: '/assets/images/org-logo.png'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || ((_currentUser$email2 = currentUser.email) === null || _currentUser$email2 === void 0 ? void 0 : _currentUser$email2.split('@')[0]) || 'User',\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), showWelcome && welcomeMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-green-50 to-blue-50 border-l-4 border-green-400 p-6 mt-16 mx-4 rounded-lg shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6 text-green-400\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3 flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-green-800\",\n            children: \"Welcome to Agno WorkSphere!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-sm text-green-700\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: welcomeMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowWelcome(false),\n              className: \"bg-green-100 hover:bg-green-200 text-green-800 px-4 py-2 rounded-md text-sm font-medium transition-colors\",\n              children: \"Got it, thanks!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `glass-effect border-b border-white/20 p-4 ${showWelcome ? 'mt-4' : 'mt-16'}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-slate-700\",\n              children: currentUser ? `Welcome, ${currentUser.firstName}!` : 'Loading...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-sm text-slate-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-3 py-1 bg-blue-100 text-blue-800 rounded-full font-medium capitalize\",\n              children: userRole\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this), currentOrganization && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-slate-500\",\n              children: [\"\\u2022 \", currentOrganization.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 text-sm text-slate-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Organizations: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_organizations) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Projects: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_projects) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Members: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_members) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\",\n        children: getKPIData().map((kpi, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in\",\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: /*#__PURE__*/_jsxDEV(KPICard, {\n            title: kpi.title,\n            value: kpi.value,\n            change: kpi.change,\n            changeType: kpi.changeType,\n            icon: kpi.icon,\n            color: kpi.color\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2 space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-semibold text-slate-800 tracking-tight\",\n                  children: userRole === 'Viewer' ? 'Available Projects' : 'My Projects'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-600 mt-1\",\n                  children: \"Manage and track your active projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/project-overview-analytics\",\n                className: \"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\",\n                children: [\"View Analytics\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5l7 7-7 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-fade-in\",\n                style: {\n                  animationDelay: `${index * 0.1}s`\n                },\n                children: /*#__PURE__*/_jsxDEV(ProjectCard, {\n                  project: project,\n                  userRole: userRole\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this)\n              }, project.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), filteredProjects.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-slate-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-600 text-lg\",\n                children: \"No projects match your current filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-500 text-sm mt-1\",\n                children: \"Try adjusting your search or filter criteria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(QuickActions, {\n            userRole: userRole,\n            onCreateProject: handleOpenCreateProject,\n            onCreateOrganization: handleOpenCreateOrganization,\n            onManageUsers: handleManageUsers,\n            onInviteMembers: handleInviteMembers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(ActivityFeed, {\n            activities: [],\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NotificationPanel, {\n            notifications: notifications,\n            userRole: userRole,\n            loading: notificationsLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(TaskSummary, {\n          tasks: [],\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TeamOverview, {\n          teamMembers: teamMembers,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateProjectModal, {\n      isOpen: showCreateProject,\n      onClose: handleCloseCreateProject,\n      onCreateProject: handleCreateProject,\n      organizationId: authService.getOrganizationId(),\n      organizationName: (currentOrganization === null || currentOrganization === void 0 ? void 0 : currentOrganization.name) || 'Your Organization'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateOrganizationModal, {\n      isOpen: showCreateOrganization,\n      onClose: handleCloseCreateOrganization,\n      onCreateOrganization: handleCreateOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InviteMemberModal, {\n      isOpen: showInviteMember,\n      onClose: handleCloseInviteMember,\n      onInviteMember: handleSendInvitation,\n      organizationId: authService.getOrganizationId(),\n      organizationName: (currentOrganization === null || currentOrganization === void 0 ? void 0 : currentOrganization.name) || 'Your Organization'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 431,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleBasedDashboard, \"zPbfSgVcjhVJ3+YwZ5kixwgwQ5s=\", false, function () {\n  return [useLocation];\n});\n_c = RoleBasedDashboard;\nexport default RoleBasedDashboard;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "RoleBasedHeader", "KPICard", "ProjectCard", "ActivityFeed", "QuickActions", "TaskSummary", "TeamOverview", "NotificationPanel", "authService", "apiService", "teamService", "notificationService", "CreateProjectModal", "CreateOrganizationModal", "InviteMemberModal", "jsxDEV", "_jsxDEV", "RoleBasedDashboard", "_s", "_organizations$0$orga", "_currentUser$email", "_organizations$0$orga2", "_currentUser$email2", "location", "userRole", "setUserRole", "searchValue", "setSearchValue", "filterValue", "setFilterValue", "dashboardData", "setDashboardData", "projects", "setProjects", "currentUser", "setCurrentUser", "organizations", "setOrganizations", "loading", "setLoading", "error", "setError", "showWelcome", "setShowWelcome", "welcomeMessage", "setWelcomeMessage", "showCreateProject", "setShowCreateProject", "showCreateOrganization", "setShowCreateOrganization", "showInviteMember", "setShowInviteMember", "teamMembers", "setTeamMembers", "notifications", "setNotifications", "notificationsLoading", "setNotificationsLoading", "loadDashboardData", "_location$state", "_location$state2", "state", "message", "type", "window", "history", "replaceState", "document", "title", "userResult", "getCurrentUser", "data", "user", "role", "getUserRole", "console", "warn", "id", "firstName", "lastName", "email", "name", "domain", "statsResult", "getDashboardStats", "organizationId", "getOrganizationId", "projectsResult", "getAll", "teamMembersResult", "getTeamMembers", "teamError", "_userResult$data$orga", "_userResult$data$orga2", "notificationsResult", "getNotifications", "limit", "Array", "isArray", "isFirstTime", "checkFirstTimeUser", "createWelcomeNotification", "updatedNotifications", "notificationError", "err", "length", "handleCreateProject", "projectData", "Error", "log", "newProject", "create", "_error$response", "response", "handleOpenCreateProject", "handleCloseCreateProject", "handleCreateOrganization", "organizationData", "logoFile", "result", "handleOpenCreateOrganization", "handleCloseCreateOrganization", "handleManageUsers", "href", "handleInviteMembers", "handleCloseInviteMember", "handleSendInvitation", "inviteData", "fetch", "method", "headers", "getAccessToken", "body", "JSON", "stringify", "ok", "errorData", "json", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "reload", "getKPIData", "_realData$total_organ", "_realData$total_proje", "_realData$total_membe", "_realData$recent_acti", "_realData$recent_acti2", "_realData$total_proje2", "_realData$total_membe2", "_realData$total_organ2", "_realData$recent_acti3", "_realData$recent_acti4", "_realData$total_proje3", "_realData$total_organ3", "_realData$total_membe3", "_realData$recent_acti5", "_realData$recent_acti6", "_realData$total_proje4", "_realData$total_organ4", "_realData$total_membe4", "_realData$recent_acti7", "_realData$recent_acti8", "realData", "value", "total_organizations", "toString", "change", "changeType", "icon", "color", "total_projects", "total_members", "recent_activity", "filteredProjects", "filter", "project", "matchesSearch", "toLowerCase", "includes", "description", "matchesFilter", "status", "hasProjectAccess", "organization_id", "currentOrganization", "organization", "split", "logo", "logo_url", "trim", "avatar", "map", "kpi", "index", "style", "animationDelay", "to", "onCreateProject", "onCreateOrganization", "onManageUsers", "onInviteMembers", "activities", "tasks", "isOpen", "onClose", "organizationName", "onInviteMember", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/role-based-dashboard/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\nimport teamService from '../../utils/teamService';\nimport notificationService from '../../utils/notificationService';\nimport CreateProjectModal from '../../components/modals/CreateProjectModal';\nimport CreateOrganizationModal from '../../components/modals/CreateOrganizationModal';\nimport InviteMemberModal from '../../components/modals/InviteMemberModal';\n\nconst RoleBasedDashboard = () => {\n  const location = useLocation();\n  const [userRole, setUserRole] = useState('member'); // Default role\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showWelcome, setShowWelcome] = useState(false);\n  const [welcomeMessage, setWelcomeMessage] = useState('');\n  const [showCreateProject, setShowCreateProject] = useState(false);\n  const [showCreateOrganization, setShowCreateOrganization] = useState(false);\n  const [showInviteMember, setShowInviteMember] = useState(false);\n\n  // Real team members data\n  const [teamMembers, setTeamMembers] = useState([]);\n\n  // Real notifications data\n  const [notifications, setNotifications] = useState([]);\n  const [notificationsLoading, setNotificationsLoading] = useState(true);\n\n  // Load dashboard data from backend\n  useEffect(() => {\n    const loadDashboardData = async () => {\n      try {\n        setLoading(true);\n\n        // Check for welcome message from navigation state\n        if (location.state?.message && location.state?.type === 'success') {\n          setWelcomeMessage(location.state.message);\n          setShowWelcome(true);\n          // Clear the state to prevent showing on refresh\n          window.history.replaceState({}, document.title);\n        }\n\n        // Get current user and role\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || authService.getUserRole());\n          setOrganizations(userResult.data.organizations || []);\n        } else {\n          // If no user, set fallback data instead of redirecting\n          console.warn('No user data available, using fallback');\n          setCurrentUser({\n            id: 'fallback-user',\n            firstName: 'User',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'member'\n          });\n          setUserRole('member');\n          setOrganizations([{\n            id: 'fallback-org',\n            name: 'Default Organization',\n            domain: 'example.com'\n          }]);\n        }\n\n        // Get dashboard stats\n        const statsResult = await authService.getDashboardStats();\n        if (statsResult.data) {\n          setDashboardData(statsResult.data);\n        }\n\n        // Get projects\n        const organizationId = authService.getOrganizationId();\n        if (organizationId) {\n          const projectsResult = await apiService.projects.getAll(organizationId);\n          setProjects(projectsResult || []);\n\n          // Get team members\n          try {\n            const teamMembersResult = await teamService.getTeamMembers(organizationId);\n            setTeamMembers(teamMembersResult || []);\n          } catch (teamError) {\n            console.error('Failed to load team members:', teamError);\n            setTeamMembers([]); // Clear team members on error\n          }\n\n          // Get notifications and check for first-time user\n          try {\n            setNotificationsLoading(true);\n            const notificationsResult = await notificationService.getNotifications({ limit: 10 });\n\n            // Handle the new response structure\n            if (notificationsResult && notificationsResult.data && Array.isArray(notificationsResult.data)) {\n              setNotifications(notificationsResult.data);\n            } else if (Array.isArray(notificationsResult)) {\n              setNotifications(notificationsResult);\n            } else {\n              setNotifications([]);\n            }\n\n            // Check if this is a first-time user and create welcome notification\n            const isFirstTime = await notificationService.checkFirstTimeUser();\n            if (isFirstTime && userResult.data.organizations?.[0]?.name) {\n              await notificationService.createWelcomeNotification(\n                userResult.data.user.id,\n                userResult.data.organizations[0].name\n              );\n\n              // Reload notifications to include the welcome notification\n              const updatedNotifications = await notificationService.getNotifications({ limit: 10 });\n              if (updatedNotifications && updatedNotifications.data && Array.isArray(updatedNotifications.data)) {\n                setNotifications(updatedNotifications.data);\n              } else if (Array.isArray(updatedNotifications)) {\n                setNotifications(updatedNotifications);\n              } else {\n                setNotifications([]);\n              }\n            }\n          } catch (notificationError) {\n            console.error('Failed to load notifications:', notificationError);\n            setNotifications([]); // Clear notifications on error\n          } finally {\n            setNotificationsLoading(false);\n          }\n        }\n\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load dashboard data:', err);\n\n        // Don't set error state that would prevent the page from working\n        // Instead, set fallback data and let the page render\n        if (!currentUser) {\n          setCurrentUser({\n            id: 'fallback-user',\n            firstName: 'User',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'member'\n          });\n          setUserRole('member');\n        }\n\n        if (organizations.length === 0) {\n          setOrganizations([{\n            id: 'fallback-org',\n            name: 'Default Organization',\n            domain: 'example.com'\n          }]);\n        }\n\n        // Set empty arrays for other data\n        setProjects([]);\n        setTeamMembers([]);\n        setNotifications([]);\n        setNotificationsLoading(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadDashboardData();\n  }, [location.state]);\n\n  // Project creation handler\n  const handleCreateProject = async (projectData) => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n\n      console.log('Creating project with data:', projectData);\n      console.log('Organization ID:', organizationId);\n\n      const newProject = await apiService.projects.create(organizationId, projectData);\n      console.log('Project creation response:', newProject);\n\n      // Refresh projects list\n      console.log('Refreshing projects list...');\n      const projectsResult = await apiService.projects.getAll(organizationId);\n      console.log('Updated projects list:', projectsResult);\n      setProjects(projectsResult || []);\n\n      // Refresh dashboard stats\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n\n      console.log('Project created successfully:', newProject);\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      console.error('Error details:', error.response?.data || error.message);\n      throw error;\n    }\n  };\n\n  // Quick action handlers\n  const handleOpenCreateProject = () => {\n    setShowCreateProject(true);\n  };\n\n  const handleCloseCreateProject = () => {\n    setShowCreateProject(false);\n  };\n\n  // Organization creation handlers\n  const handleCreateOrganization = async (organizationData, logoFile) => {\n    try {\n      const result = await apiService.organizations.create(organizationData, logoFile);\n\n      if (result.error) {\n        throw new Error(result.error);\n      }\n\n      // Show success message\n      setWelcomeMessage(result.message || `Organization \"${organizationData.name}\" created successfully!`);\n      setShowWelcome(true);\n\n      // Refresh organizations list if needed\n      // You might want to update the current organization context here\n      console.log('Organization created successfully:', result.data);\n\n      return result.data;\n    } catch (error) {\n      console.error('Failed to create organization:', error);\n      throw error;\n    }\n  };\n\n  const handleOpenCreateOrganization = () => {\n    setShowCreateOrganization(true);\n  };\n\n  const handleCloseCreateOrganization = () => {\n    setShowCreateOrganization(false);\n  };\n\n  const handleManageUsers = () => {\n    // Navigate to team members page\n    window.location.href = '/team-members';\n  };\n\n  const handleInviteMembers = () => {\n    setShowInviteMember(true);\n  };\n\n  const handleCloseInviteMember = () => {\n    setShowInviteMember(false);\n  };\n\n  const handleSendInvitation = async (inviteData) => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n\n      // Call the backend API to send invitation\n      const response = await fetch(`http://localhost:3001/api/v1/organizations/${organizationId}/invite`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getAccessToken()}`\n        },\n        body: JSON.stringify({\n          email: inviteData.email,\n          role: inviteData.role\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to send invitation');\n      }\n\n      const result = await response.json();\n      console.log('Invitation sent successfully:', result);\n\n      // Refresh dashboard stats to show updated member count\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n\n    } catch (error) {\n      console.error('Failed to send invitation:', error);\n      throw error;\n    }\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-slate-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <p className=\"text-red-600 text-lg mb-2\">Failed to load dashboard</p>\n          <p className=\"text-slate-600 text-sm\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Real projects data is loaded from API in useEffect\n\n  // Real activities and tasks data will be loaded from API when available\n\n  // Role-based KPI data with real data integration\n  const getKPIData = () => {\n    const realData = dashboardData || {};\n\n    switch (userRole) {\n      case 'owner':\n        return [\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Active Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'admin':\n        return [\n          { title: \"Active Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"success\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'member':\n        return [\n          { title: \"My Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'viewer':\n        return [\n          { title: \"Projects Viewed\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Eye\", color: \"primary\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"accent\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"success\" },\n          { title: \"Activity Items\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"BarChart3\", color: \"warning\" }\n        ];\n      default:\n        return [\n          { title: \"Projects\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Organizations\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Members\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Activity\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n    }\n  };\n\n  // Filter projects based on search, filter values, and role-based access\n  const filteredProjects = projects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) ||\n                         (project.description || '').toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' ||\n                         (project.status || 'active').toLowerCase() === filterValue.toLowerCase();\n\n    // Role-based project access control\n    const hasProjectAccess = () => {\n      if (!currentUser?.role) return true; // Default access if role not set\n\n      const userRole = currentUser.role.toLowerCase();\n\n      // Viewers should only see projects they are specifically invited to\n      if (userRole === 'viewer') {\n        // For now, viewers can see projects from their organization\n        // In a real implementation, this would check project-specific invitations\n        return project.organization_id === currentUser.organization_id;\n      }\n\n      // Members, admins, and owners can see all projects in their organization\n      return true;\n    };\n\n    return matchesSearch && matchesFilter && hasProjectAccess();\n  });\n\n\n\n  // Get current organization data\n  const currentOrganization = organizations.length > 0 ? {\n    name: organizations[0].organization?.name || 'Your Organization',\n    domain: currentUser?.email?.split('@')[1] || 'company.com',\n    logo: organizations[0].organization?.logo_url || '/assets/images/org-logo.png'\n  } : {\n    name: 'Your Organization',\n    domain: 'company.com',\n    logo: '/assets/images/org-logo.png'\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\">\n      {/* Role-Based Header */}\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || currentUser.email?.split('@')[0] || 'User',\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Welcome Message for New Users */}\n      {showWelcome && welcomeMessage && (\n        <div className=\"bg-gradient-to-r from-green-50 to-blue-50 border-l-4 border-green-400 p-6 mt-16 mx-4 rounded-lg shadow-sm\">\n          <div className=\"max-w-7xl mx-auto flex items-start\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-6 w-6 text-green-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div className=\"ml-3 flex-1\">\n              <h3 className=\"text-lg font-medium text-green-800\">Welcome to Agno WorkSphere!</h3>\n              <div className=\"mt-2 text-sm text-green-700\">\n                <p>{welcomeMessage}</p>\n              </div>\n              <div className=\"mt-4\">\n                <button\n                  onClick={() => setShowWelcome(false)}\n                  className=\"bg-green-100 hover:bg-green-200 text-green-800 px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  Got it, thanks!\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* User Info Bar */}\n      <div className={`glass-effect border-b border-white/20 p-4 ${showWelcome ? 'mt-4' : 'mt-16'}`}>\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm font-medium text-slate-700\">\n                {currentUser ? `Welcome, ${currentUser.firstName}!` : 'Loading...'}\n              </span>\n            </div>\n            <div className=\"flex items-center gap-2 text-sm text-slate-600\">\n              <span className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full font-medium capitalize\">\n                {userRole}\n              </span>\n              {currentOrganization && (\n                <span className=\"text-slate-500\">\n                  • {currentOrganization.name}\n                </span>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center gap-3 text-sm text-slate-600\">\n            <span>Organizations: {dashboardData?.total_organizations || 0}</span>\n            <span>•</span>\n            <span>Projects: {dashboardData?.total_projects || 0}</span>\n            <span>•</span>\n            <span>Members: {dashboardData?.total_members || 0}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Dashboard content - header functionality moved to RoleBasedHeader */}\n\n      {/* Main Dashboard Content */}\n      <div className=\"max-w-7xl mx-auto p-8\">\n        {/* Enhanced KPI Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\">\n          {getKPIData().map((kpi, index) => (\n            <div key={index} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n              <KPICard\n                title={kpi.title}\n                value={kpi.value}\n                change={kpi.change}\n                changeType={kpi.changeType}\n                icon={kpi.icon}\n                color={kpi.color}\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content Grid with improved spacing */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\">\n          {/* Left Column - Projects and Quick Actions */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Projects Section with enhanced header */}\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h2 className=\"text-2xl font-semibold text-slate-800 tracking-tight\">\n                    {userRole === 'Viewer' ? 'Available Projects' : 'My Projects'}\n                  </h2>\n                  <p className=\"text-slate-600 mt-1\">Manage and track your active projects</p>\n                </div>\n                <Link \n                  to=\"/project-overview-analytics\"\n                  className=\"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\"\n                >\n                  View Analytics\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n              \n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n                {filteredProjects.map((project, index) => (\n                  <div key={project.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                    <ProjectCard\n                      project={project}\n                      userRole={userRole}\n                    />\n                  </div>\n                ))}\n              </div>\n              \n              {filteredProjects.length === 0 && (\n                <div className=\"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\">\n                  <div className=\"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <svg className=\"w-8 h-8 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-slate-600 text-lg\">No projects match your current filters</p>\n                  <p className=\"text-slate-500 text-sm mt-1\">Try adjusting your search or filter criteria</p>\n                </div>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <QuickActions\n              userRole={userRole}\n              onCreateProject={handleOpenCreateProject}\n              onCreateOrganization={handleOpenCreateOrganization}\n              onManageUsers={handleManageUsers}\n              onInviteMembers={handleInviteMembers}\n            />\n          </div>\n\n          {/* Right Column - Activity Feed and Notifications */}\n          <div className=\"space-y-8\">\n            <ActivityFeed activities={[]} userRole={userRole} />\n            <NotificationPanel notifications={notifications} userRole={userRole} loading={notificationsLoading} />\n          </div>\n        </div>\n\n        {/* Bottom Section - Tasks and Team with improved layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          <TaskSummary tasks={[]} userRole={userRole} />\n          <TeamOverview teamMembers={teamMembers} userRole={userRole} />\n        </div>\n      </div>\n\n      {/* Create Project Modal */}\n      <CreateProjectModal\n        isOpen={showCreateProject}\n        onClose={handleCloseCreateProject}\n        onCreateProject={handleCreateProject}\n        organizationId={authService.getOrganizationId()}\n        organizationName={currentOrganization?.name || 'Your Organization'}\n      />\n\n      {/* Create Organization Modal */}\n      <CreateOrganizationModal\n        isOpen={showCreateOrganization}\n        onClose={handleCloseCreateOrganization}\n        onCreateOrganization={handleCreateOrganization}\n      />\n\n      {/* Invite Member Modal */}\n      <InviteMemberModal\n        isOpen={showInviteMember}\n        onClose={handleCloseInviteMember}\n        onInviteMember={handleSendInvitation}\n        organizationId={authService.getOrganizationId()}\n        organizationName={currentOrganization?.name || 'Your Organization'}\n      />\n    </div>\n  );\n};\n\nexport default RoleBasedDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,kBAAkB,MAAM,4CAA4C;AAC3E,OAAOC,uBAAuB,MAAM,iDAAiD;AACrF,OAAOC,iBAAiB,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,mBAAA;EAC/B,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6D,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QAAA,IAAAC,eAAA,EAAAC,gBAAA;QACFrB,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,IAAI,CAAAoB,eAAA,GAAApC,QAAQ,CAACsC,KAAK,cAAAF,eAAA,eAAdA,eAAA,CAAgBG,OAAO,IAAI,EAAAF,gBAAA,GAAArC,QAAQ,CAACsC,KAAK,cAAAD,gBAAA,uBAAdA,gBAAA,CAAgBG,IAAI,MAAK,SAAS,EAAE;UACjElB,iBAAiB,CAACtB,QAAQ,CAACsC,KAAK,CAACC,OAAO,CAAC;UACzCnB,cAAc,CAAC,IAAI,CAAC;UACpB;UACAqB,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,CAAC;QACjD;;QAEA;QACA,MAAMC,UAAU,GAAG,MAAM7D,WAAW,CAAC8D,cAAc,CAAC,CAAC;QACrD,IAAID,UAAU,CAACE,IAAI,CAACC,IAAI,EAAE;UACxBrC,cAAc,CAACkC,UAAU,CAACE,IAAI,CAACC,IAAI,CAAC;UACpC/C,WAAW,CAAC4C,UAAU,CAACE,IAAI,CAACC,IAAI,CAACC,IAAI,IAAIjE,WAAW,CAACkE,WAAW,CAAC,CAAC,CAAC;UACnErC,gBAAgB,CAACgC,UAAU,CAACE,IAAI,CAACnC,aAAa,IAAI,EAAE,CAAC;QACvD,CAAC,MAAM;UACL;UACAuC,OAAO,CAACC,IAAI,CAAC,wCAAwC,CAAC;UACtDzC,cAAc,CAAC;YACb0C,EAAE,EAAE,eAAe;YACnBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE,kBAAkB;YACzBP,IAAI,EAAE;UACR,CAAC,CAAC;UACFhD,WAAW,CAAC,QAAQ,CAAC;UACrBY,gBAAgB,CAAC,CAAC;YAChBwC,EAAE,EAAE,cAAc;YAClBI,IAAI,EAAE,sBAAsB;YAC5BC,MAAM,EAAE;UACV,CAAC,CAAC,CAAC;QACL;;QAEA;QACA,MAAMC,WAAW,GAAG,MAAM3E,WAAW,CAAC4E,iBAAiB,CAAC,CAAC;QACzD,IAAID,WAAW,CAACZ,IAAI,EAAE;UACpBxC,gBAAgB,CAACoD,WAAW,CAACZ,IAAI,CAAC;QACpC;;QAEA;QACA,MAAMc,cAAc,GAAG7E,WAAW,CAAC8E,iBAAiB,CAAC,CAAC;QACtD,IAAID,cAAc,EAAE;UAClB,MAAME,cAAc,GAAG,MAAM9E,UAAU,CAACuB,QAAQ,CAACwD,MAAM,CAACH,cAAc,CAAC;UACvEpD,WAAW,CAACsD,cAAc,IAAI,EAAE,CAAC;;UAEjC;UACA,IAAI;YACF,MAAME,iBAAiB,GAAG,MAAM/E,WAAW,CAACgF,cAAc,CAACL,cAAc,CAAC;YAC1EhC,cAAc,CAACoC,iBAAiB,IAAI,EAAE,CAAC;UACzC,CAAC,CAAC,OAAOE,SAAS,EAAE;YAClBhB,OAAO,CAACnC,KAAK,CAAC,8BAA8B,EAAEmD,SAAS,CAAC;YACxDtC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;UACtB;;UAEA;UACA,IAAI;YAAA,IAAAuC,qBAAA,EAAAC,sBAAA;YACFpC,uBAAuB,CAAC,IAAI,CAAC;YAC7B,MAAMqC,mBAAmB,GAAG,MAAMnF,mBAAmB,CAACoF,gBAAgB,CAAC;cAAEC,KAAK,EAAE;YAAG,CAAC,CAAC;;YAErF;YACA,IAAIF,mBAAmB,IAAIA,mBAAmB,CAACvB,IAAI,IAAI0B,KAAK,CAACC,OAAO,CAACJ,mBAAmB,CAACvB,IAAI,CAAC,EAAE;cAC9FhB,gBAAgB,CAACuC,mBAAmB,CAACvB,IAAI,CAAC;YAC5C,CAAC,MAAM,IAAI0B,KAAK,CAACC,OAAO,CAACJ,mBAAmB,CAAC,EAAE;cAC7CvC,gBAAgB,CAACuC,mBAAmB,CAAC;YACvC,CAAC,MAAM;cACLvC,gBAAgB,CAAC,EAAE,CAAC;YACtB;;YAEA;YACA,MAAM4C,WAAW,GAAG,MAAMxF,mBAAmB,CAACyF,kBAAkB,CAAC,CAAC;YAClE,IAAID,WAAW,KAAAP,qBAAA,GAAIvB,UAAU,CAACE,IAAI,CAACnC,aAAa,cAAAwD,qBAAA,gBAAAC,sBAAA,GAA7BD,qBAAA,CAAgC,CAAC,CAAC,cAAAC,sBAAA,eAAlCA,sBAAA,CAAoCZ,IAAI,EAAE;cAC3D,MAAMtE,mBAAmB,CAAC0F,yBAAyB,CACjDhC,UAAU,CAACE,IAAI,CAACC,IAAI,CAACK,EAAE,EACvBR,UAAU,CAACE,IAAI,CAACnC,aAAa,CAAC,CAAC,CAAC,CAAC6C,IACnC,CAAC;;cAED;cACA,MAAMqB,oBAAoB,GAAG,MAAM3F,mBAAmB,CAACoF,gBAAgB,CAAC;gBAAEC,KAAK,EAAE;cAAG,CAAC,CAAC;cACtF,IAAIM,oBAAoB,IAAIA,oBAAoB,CAAC/B,IAAI,IAAI0B,KAAK,CAACC,OAAO,CAACI,oBAAoB,CAAC/B,IAAI,CAAC,EAAE;gBACjGhB,gBAAgB,CAAC+C,oBAAoB,CAAC/B,IAAI,CAAC;cAC7C,CAAC,MAAM,IAAI0B,KAAK,CAACC,OAAO,CAACI,oBAAoB,CAAC,EAAE;gBAC9C/C,gBAAgB,CAAC+C,oBAAoB,CAAC;cACxC,CAAC,MAAM;gBACL/C,gBAAgB,CAAC,EAAE,CAAC;cACtB;YACF;UACF,CAAC,CAAC,OAAOgD,iBAAiB,EAAE;YAC1B5B,OAAO,CAACnC,KAAK,CAAC,+BAA+B,EAAE+D,iBAAiB,CAAC;YACjEhD,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;UACxB,CAAC,SAAS;YACRE,uBAAuB,CAAC,KAAK,CAAC;UAChC;QACF;QAEAhB,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAO+D,GAAG,EAAE;QACZ7B,OAAO,CAACnC,KAAK,CAAC,gCAAgC,EAAEgE,GAAG,CAAC;;QAEpD;QACA;QACA,IAAI,CAACtE,WAAW,EAAE;UAChBC,cAAc,CAAC;YACb0C,EAAE,EAAE,eAAe;YACnBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE,kBAAkB;YACzBP,IAAI,EAAE;UACR,CAAC,CAAC;UACFhD,WAAW,CAAC,QAAQ,CAAC;QACvB;QAEA,IAAIW,aAAa,CAACqE,MAAM,KAAK,CAAC,EAAE;UAC9BpE,gBAAgB,CAAC,CAAC;YAChBwC,EAAE,EAAE,cAAc;YAClBI,IAAI,EAAE,sBAAsB;YAC5BC,MAAM,EAAE;UACV,CAAC,CAAC,CAAC;QACL;;QAEA;QACAjD,WAAW,CAAC,EAAE,CAAC;QACfoB,cAAc,CAAC,EAAE,CAAC;QAClBE,gBAAgB,CAAC,EAAE,CAAC;QACpBE,uBAAuB,CAAC,KAAK,CAAC;MAChC,CAAC,SAAS;QACRlB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDmB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACnC,QAAQ,CAACsC,KAAK,CAAC,CAAC;;EAEpB;EACA,MAAM6C,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD,IAAI;MACF,MAAMtB,cAAc,GAAG7E,WAAW,CAAC8E,iBAAiB,CAAC,CAAC;MACtD,IAAI,CAACD,cAAc,EAAE;QACnB,MAAM,IAAIuB,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MAEAjC,OAAO,CAACkC,GAAG,CAAC,6BAA6B,EAAEF,WAAW,CAAC;MACvDhC,OAAO,CAACkC,GAAG,CAAC,kBAAkB,EAAExB,cAAc,CAAC;MAE/C,MAAMyB,UAAU,GAAG,MAAMrG,UAAU,CAACuB,QAAQ,CAAC+E,MAAM,CAAC1B,cAAc,EAAEsB,WAAW,CAAC;MAChFhC,OAAO,CAACkC,GAAG,CAAC,4BAA4B,EAAEC,UAAU,CAAC;;MAErD;MACAnC,OAAO,CAACkC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,MAAMtB,cAAc,GAAG,MAAM9E,UAAU,CAACuB,QAAQ,CAACwD,MAAM,CAACH,cAAc,CAAC;MACvEV,OAAO,CAACkC,GAAG,CAAC,wBAAwB,EAAEtB,cAAc,CAAC;MACrDtD,WAAW,CAACsD,cAAc,IAAI,EAAE,CAAC;;MAEjC;MACA,MAAMJ,WAAW,GAAG,MAAM3E,WAAW,CAAC4E,iBAAiB,CAAC,CAAC;MACzD,IAAID,WAAW,CAACZ,IAAI,EAAE;QACpBxC,gBAAgB,CAACoD,WAAW,CAACZ,IAAI,CAAC;MACpC;MAEAI,OAAO,CAACkC,GAAG,CAAC,+BAA+B,EAAEC,UAAU,CAAC;IAC1D,CAAC,CAAC,OAAOtE,KAAK,EAAE;MAAA,IAAAwE,eAAA;MACdrC,OAAO,CAACnC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDmC,OAAO,CAACnC,KAAK,CAAC,gBAAgB,EAAE,EAAAwE,eAAA,GAAAxE,KAAK,CAACyE,QAAQ,cAAAD,eAAA,uBAAdA,eAAA,CAAgBzC,IAAI,KAAI/B,KAAK,CAACsB,OAAO,CAAC;MACtE,MAAMtB,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM0E,uBAAuB,GAAGA,CAAA,KAAM;IACpCnE,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMoE,wBAAwB,GAAGA,CAAA,KAAM;IACrCpE,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMqE,wBAAwB,GAAG,MAAAA,CAAOC,gBAAgB,EAAEC,QAAQ,KAAK;IACrE,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM9G,UAAU,CAAC2B,aAAa,CAAC2E,MAAM,CAACM,gBAAgB,EAAEC,QAAQ,CAAC;MAEhF,IAAIC,MAAM,CAAC/E,KAAK,EAAE;QAChB,MAAM,IAAIoE,KAAK,CAACW,MAAM,CAAC/E,KAAK,CAAC;MAC/B;;MAEA;MACAK,iBAAiB,CAAC0E,MAAM,CAACzD,OAAO,IAAI,iBAAiBuD,gBAAgB,CAACpC,IAAI,yBAAyB,CAAC;MACpGtC,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA;MACAgC,OAAO,CAACkC,GAAG,CAAC,oCAAoC,EAAEU,MAAM,CAAChD,IAAI,CAAC;MAE9D,OAAOgD,MAAM,CAAChD,IAAI;IACpB,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMgF,4BAA4B,GAAGA,CAAA,KAAM;IACzCvE,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMwE,6BAA6B,GAAGA,CAAA,KAAM;IAC1CxE,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,MAAMyE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA1D,MAAM,CAACzC,QAAQ,CAACoG,IAAI,GAAG,eAAe;EACxC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCzE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM0E,uBAAuB,GAAGA,CAAA,KAAM;IACpC1E,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM2E,oBAAoB,GAAG,MAAOC,UAAU,IAAK;IACjD,IAAI;MACF,MAAM1C,cAAc,GAAG7E,WAAW,CAAC8E,iBAAiB,CAAC,CAAC;MACtD,IAAI,CAACD,cAAc,EAAE;QACnB,MAAM,IAAIuB,KAAK,CAAC,uBAAuB,CAAC;MAC1C;;MAEA;MACA,MAAMK,QAAQ,GAAG,MAAMe,KAAK,CAAC,8CAA8C3C,cAAc,SAAS,EAAE;QAClG4C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAU1H,WAAW,CAAC2H,cAAc,CAAC,CAAC;QACzD,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBtD,KAAK,EAAE+C,UAAU,CAAC/C,KAAK;UACvBP,IAAI,EAAEsD,UAAU,CAACtD;QACnB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACwC,QAAQ,CAACsB,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;QACvC,MAAM,IAAI7B,KAAK,CAAC4B,SAAS,CAAC1E,OAAO,IAAI,2BAA2B,CAAC;MACnE;MAEA,MAAMyD,MAAM,GAAG,MAAMN,QAAQ,CAACwB,IAAI,CAAC,CAAC;MACpC9D,OAAO,CAACkC,GAAG,CAAC,+BAA+B,EAAEU,MAAM,CAAC;;MAEpD;MACA,MAAMpC,WAAW,GAAG,MAAM3E,WAAW,CAAC4E,iBAAiB,CAAC,CAAC;MACzD,IAAID,WAAW,CAACZ,IAAI,EAAE;QACpBxC,gBAAgB,CAACoD,WAAW,CAACZ,IAAI,CAAC;MACpC;IAEF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,IAAIF,OAAO,EAAE;IACX,oBACEtB,OAAA;MAAK0H,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3H3H,OAAA;QAAK0H,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3H,OAAA;UAAK0H,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG/H,OAAA;UAAG0H,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIvG,KAAK,EAAE;IACT,oBACExB,OAAA;MAAK0H,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3H3H,OAAA;QAAK0H,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3H,OAAA;UAAK0H,SAAS,EAAC,iFAAiF;UAAAC,QAAA,eAC9F3H,OAAA;YAAK0H,SAAS,EAAC,sBAAsB;YAACM,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAP,QAAA,eACzF3H,OAAA;cAAMmI,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAmD;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/H,OAAA;UAAG0H,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrE/H,OAAA;UAAG0H,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAEnG;QAAK;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjD/H,OAAA;UACEuI,OAAO,EAAEA,CAAA,KAAMvF,MAAM,CAACzC,QAAQ,CAACiI,MAAM,CAAC,CAAE;UACxCd,SAAS,EAAC,sFAAsF;UAAAC,QAAA,EACjG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;;EAEA;;EAEA;EACA,MAAMU,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACvB,MAAMC,QAAQ,GAAGhJ,aAAa,IAAI,CAAC,CAAC;IAEpC,QAAQN,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,CACL;UAAE4C,KAAK,EAAE,eAAe;UAAE2G,KAAK,EAAE,EAAArB,qBAAA,GAAAoB,QAAQ,CAACE,mBAAmB,cAAAtB,qBAAA,uBAA5BA,qBAAA,CAA8BuB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC3J;UAAEjH,KAAK,EAAE,iBAAiB;UAAE2G,KAAK,EAAE,EAAApB,qBAAA,GAAAmB,QAAQ,CAACQ,cAAc,cAAA3B,qBAAA,uBAAvBA,qBAAA,CAAyBsB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC1J;UAAEjH,KAAK,EAAE,cAAc;UAAE2G,KAAK,EAAE,EAAAnB,qBAAA,GAAAkB,QAAQ,CAACS,aAAa,cAAA3B,qBAAA,uBAAtBA,qBAAA,CAAwBqB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAChJ;UAAEjH,KAAK,EAAE,iBAAiB;UAAE2G,KAAK,EAAE,EAAAlB,qBAAA,GAAAiB,QAAQ,CAACU,eAAe,cAAA3B,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BpD,MAAM,cAAAqD,sBAAA,uBAAhCA,sBAAA,CAAkCmB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAEjH,KAAK,EAAE,iBAAiB;UAAE2G,KAAK,EAAE,EAAAhB,sBAAA,GAAAe,QAAQ,CAACQ,cAAc,cAAAvB,sBAAA,uBAAvBA,sBAAA,CAAyBkB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC1J;UAAEjH,KAAK,EAAE,cAAc;UAAE2G,KAAK,EAAE,EAAAf,sBAAA,GAAAc,QAAQ,CAACS,aAAa,cAAAvB,sBAAA,uBAAtBA,sBAAA,CAAwBiB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjJ;UAAEjH,KAAK,EAAE,eAAe;UAAE2G,KAAK,EAAE,EAAAd,sBAAA,GAAAa,QAAQ,CAACE,mBAAmB,cAAAf,sBAAA,uBAA5BA,sBAAA,CAA8BgB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC1J;UAAEjH,KAAK,EAAE,iBAAiB;UAAE2G,KAAK,EAAE,EAAAb,sBAAA,GAAAY,QAAQ,CAACU,eAAe,cAAAtB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BzD,MAAM,cAAA0D,sBAAA,uBAAhCA,sBAAA,CAAkCc,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEjH,KAAK,EAAE,aAAa;UAAE2G,KAAK,EAAE,EAAAX,sBAAA,GAAAU,QAAQ,CAACQ,cAAc,cAAAlB,sBAAA,uBAAvBA,sBAAA,CAAyBa,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACtJ;UAAEjH,KAAK,EAAE,eAAe;UAAE2G,KAAK,EAAE,EAAAV,sBAAA,GAAAS,QAAQ,CAACE,mBAAmB,cAAAX,sBAAA,uBAA5BA,sBAAA,CAA8BY,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC3J;UAAEjH,KAAK,EAAE,cAAc;UAAE2G,KAAK,EAAE,EAAAT,sBAAA,GAAAQ,QAAQ,CAACS,aAAa,cAAAjB,sBAAA,uBAAtBA,sBAAA,CAAwBW,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAChJ;UAAEjH,KAAK,EAAE,iBAAiB;UAAE2G,KAAK,EAAE,EAAAR,sBAAA,GAAAO,QAAQ,CAACU,eAAe,cAAAjB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0B9D,MAAM,cAAA+D,sBAAA,uBAAhCA,sBAAA,CAAkCS,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEjH,KAAK,EAAE,iBAAiB;UAAE2G,KAAK,EAAE,EAAAN,sBAAA,GAAAK,QAAQ,CAACQ,cAAc,cAAAb,sBAAA,uBAAvBA,sBAAA,CAAyBQ,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAC,EACnJ;UAAEjH,KAAK,EAAE,eAAe;UAAE2G,KAAK,EAAE,EAAAL,sBAAA,GAAAI,QAAQ,CAACE,mBAAmB,cAAAN,sBAAA,uBAA5BA,sBAAA,CAA8BO,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC1J;UAAEjH,KAAK,EAAE,cAAc;UAAE2G,KAAK,EAAE,EAAAJ,sBAAA,GAAAG,QAAQ,CAACS,aAAa,cAAAZ,sBAAA,uBAAtBA,sBAAA,CAAwBM,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjJ;UAAEjH,KAAK,EAAE,gBAAgB;UAAE2G,KAAK,EAAE,EAAAH,sBAAA,GAAAE,QAAQ,CAACU,eAAe,cAAAZ,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BnE,MAAM,cAAAoE,sBAAA,uBAAhCA,sBAAA,CAAkCI,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH;QACE,OAAO,CACL;UAAEjH,KAAK,EAAE,UAAU;UAAE2G,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC5G;UAAEjH,KAAK,EAAE,eAAe;UAAE2G,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/G;UAAEjH,KAAK,EAAE,SAAS;UAAE2G,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EACrG;UAAEjH,KAAK,EAAE,UAAU;UAAE2G,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAC3G;IACL;EACF,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGzJ,QAAQ,CAAC0J,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,aAAa,GAAGD,OAAO,CAAC1G,IAAI,CAAC4G,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpK,WAAW,CAACmK,WAAW,CAAC,CAAC,CAAC,IAC/D,CAACF,OAAO,CAACI,WAAW,IAAI,EAAE,EAAEF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpK,WAAW,CAACmK,WAAW,CAAC,CAAC,CAAC;IAClG,MAAMG,aAAa,GAAGpK,WAAW,KAAK,KAAK,IACtB,CAAC+J,OAAO,CAACM,MAAM,IAAI,QAAQ,EAAEJ,WAAW,CAAC,CAAC,KAAKjK,WAAW,CAACiK,WAAW,CAAC,CAAC;;IAE7F;IACA,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAI,EAAChK,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEuC,IAAI,GAAE,OAAO,IAAI,CAAC,CAAC;;MAErC,MAAMjD,QAAQ,GAAGU,WAAW,CAACuC,IAAI,CAACoH,WAAW,CAAC,CAAC;;MAE/C;MACA,IAAIrK,QAAQ,KAAK,QAAQ,EAAE;QACzB;QACA;QACA,OAAOmK,OAAO,CAACQ,eAAe,KAAKjK,WAAW,CAACiK,eAAe;MAChE;;MAEA;MACA,OAAO,IAAI;IACb,CAAC;IAED,OAAOP,aAAa,IAAII,aAAa,IAAIE,gBAAgB,CAAC,CAAC;EAC7D,CAAC,CAAC;;EAIF;EACA,MAAME,mBAAmB,GAAGhK,aAAa,CAACqE,MAAM,GAAG,CAAC,GAAG;IACrDxB,IAAI,EAAE,EAAA9D,qBAAA,GAAAiB,aAAa,CAAC,CAAC,CAAC,CAACiK,YAAY,cAAAlL,qBAAA,uBAA7BA,qBAAA,CAA+B8D,IAAI,KAAI,mBAAmB;IAChEC,MAAM,EAAE,CAAAhD,WAAW,aAAXA,WAAW,wBAAAd,kBAAA,GAAXc,WAAW,CAAE8C,KAAK,cAAA5D,kBAAA,uBAAlBA,kBAAA,CAAoBkL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,aAAa;IAC1DC,IAAI,EAAE,EAAAlL,sBAAA,GAAAe,aAAa,CAAC,CAAC,CAAC,CAACiK,YAAY,cAAAhL,sBAAA,uBAA7BA,sBAAA,CAA+BmL,QAAQ,KAAI;EACnD,CAAC,GAAG;IACFvH,IAAI,EAAE,mBAAmB;IACzBC,MAAM,EAAE,aAAa;IACrBqH,IAAI,EAAE;EACR,CAAC;EAED,oBACEvL,OAAA;IAAK0H,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAE1F3H,OAAA,CAAChB,eAAe;MACdwB,QAAQ,EAAEA,QAAQ,CAACqK,WAAW,CAAC,CAAE;MACjC3J,WAAW,EAAEA,WAAW,GAAG;QACzB+C,IAAI,EAAE,GAAG/C,WAAW,CAAC4C,SAAS,IAAI,EAAE,IAAI5C,WAAW,CAAC6C,QAAQ,IAAI,EAAE,EAAE,CAAC0H,IAAI,CAAC,CAAC,MAAAnL,mBAAA,GAAIY,WAAW,CAAC8C,KAAK,cAAA1D,mBAAA,uBAAjBA,mBAAA,CAAmBgL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,MAAM;QACzHtH,KAAK,EAAE9C,WAAW,CAAC8C,KAAK;QACxB0H,MAAM,EAAExK,WAAW,CAACwK,MAAM,IAAI,2BAA2B;QACzDjI,IAAI,EAAEjD;MACR,CAAC,GAAG;QACFyD,IAAI,EAAE,YAAY;QAClBD,KAAK,EAAE,EAAE;QACT0H,MAAM,EAAE,2BAA2B;QACnCjI,IAAI,EAAEjD;MACR,CAAE;MACF4K,mBAAmB,EAAEA;IAAoB;MAAAxD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,EAGDrG,WAAW,IAAIE,cAAc,iBAC5B5B,OAAA;MAAK0H,SAAS,EAAC,2GAA2G;MAAAC,QAAA,eACxH3H,OAAA;QAAK0H,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjD3H,OAAA;UAAK0H,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B3H,OAAA;YAAK0H,SAAS,EAAC,wBAAwB;YAACM,IAAI,EAAC,MAAM;YAACE,OAAO,EAAC,WAAW;YAACD,MAAM,EAAC,cAAc;YAAAN,QAAA,eAC3F3H,OAAA;cAAMmI,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA+C;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B3H,OAAA;YAAI0H,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF/H,OAAA;YAAK0H,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C3H,OAAA;cAAA2H,QAAA,EAAI/F;YAAc;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACN/H,OAAA;YAAK0H,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB3H,OAAA;cACEuI,OAAO,EAAEA,CAAA,KAAM5G,cAAc,CAAC,KAAK,CAAE;cACrC+F,SAAS,EAAC,2GAA2G;cAAAC,QAAA,EACtH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD/H,OAAA;MAAK0H,SAAS,EAAE,6CAA6ChG,WAAW,GAAG,MAAM,GAAG,OAAO,EAAG;MAAAiG,QAAA,eAC5F3H,OAAA;QAAK0H,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClE3H,OAAA;UAAK0H,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC3H,OAAA;YAAK0H,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC3H,OAAA;cAAK0H,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvE/H,OAAA;cAAM0H,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EACjDzG,WAAW,GAAG,YAAYA,WAAW,CAAC4C,SAAS,GAAG,GAAG;YAAY;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN/H,OAAA;YAAK0H,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D3H,OAAA;cAAM0H,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EACtFnH;YAAQ;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACNqD,mBAAmB,iBAClBpL,OAAA;cAAM0H,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAC,SAC7B,EAACyD,mBAAmB,CAACnH,IAAI;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7D3H,OAAA;YAAA2H,QAAA,GAAM,iBAAe,EAAC,CAAA7G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkJ,mBAAmB,KAAI,CAAC;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrE/H,OAAA;YAAA2H,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACd/H,OAAA;YAAA2H,QAAA,GAAM,YAAU,EAAC,CAAA7G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwJ,cAAc,KAAI,CAAC;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3D/H,OAAA;YAAA2H,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACd/H,OAAA;YAAA2H,QAAA,GAAM,WAAS,EAAC,CAAA7G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyJ,aAAa,KAAI,CAAC;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAKN/H,OAAA;MAAK0H,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAEpC3H,OAAA;QAAK0H,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEc,UAAU,CAAC,CAAC,CAACkD,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC3B7L,OAAA;UAAiB0H,SAAS,EAAC,iBAAiB;UAACoE,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAI,CAAE;UAAAlE,QAAA,eACxF3H,OAAA,CAACf,OAAO;YACNmE,KAAK,EAAEwI,GAAG,CAACxI,KAAM;YACjB2G,KAAK,EAAE6B,GAAG,CAAC7B,KAAM;YACjBG,MAAM,EAAE0B,GAAG,CAAC1B,MAAO;YACnBC,UAAU,EAAEyB,GAAG,CAACzB,UAAW;YAC3BC,IAAI,EAAEwB,GAAG,CAACxB,IAAK;YACfC,KAAK,EAAEuB,GAAG,CAACvB;UAAM;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GARM8D,KAAK;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN/H,OAAA;QAAK0H,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1D3H,OAAA;UAAK0H,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtC3H,OAAA;YAAK0H,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3H,OAAA;cAAK0H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD3H,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAI0H,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EACjEnH,QAAQ,KAAK,QAAQ,GAAG,oBAAoB,GAAG;gBAAa;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACL/H,OAAA;kBAAG0H,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACN/H,OAAA,CAAClB,IAAI;gBACHkN,EAAE,EAAC,6BAA6B;gBAChCtE,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,GAC3K,gBAEC,eAAA3H,OAAA;kBAAK0H,SAAS,EAAC,SAAS;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC5E3H,OAAA;oBAAMmI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAc;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN/H,OAAA;cAAK0H,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD8C,gBAAgB,CAACkB,GAAG,CAAC,CAAChB,OAAO,EAAEkB,KAAK,kBACnC7L,OAAA;gBAAsB0H,SAAS,EAAC,iBAAiB;gBAACoE,KAAK,EAAE;kBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;gBAAI,CAAE;gBAAAlE,QAAA,eAC7F3H,OAAA,CAACd,WAAW;kBACVyL,OAAO,EAAEA,OAAQ;kBACjBnK,QAAQ,EAAEA;gBAAS;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC,GAJM4C,OAAO,CAAC9G,EAAE;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEL0C,gBAAgB,CAAChF,MAAM,KAAK,CAAC,iBAC5BzF,OAAA;cAAK0H,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAChG3H,OAAA;gBAAK0H,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChG3H,OAAA;kBAAK0H,SAAS,EAAC,wBAAwB;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC3F3H,OAAA;oBAAMmI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAsH;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/H,OAAA;gBAAG0H,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChF/H,OAAA;gBAAG0H,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN/H,OAAA,CAACZ,YAAY;YACXoB,QAAQ,EAAEA,QAAS;YACnByL,eAAe,EAAE/F,uBAAwB;YACzCgG,oBAAoB,EAAE1F,4BAA6B;YACnD2F,aAAa,EAAEzF,iBAAkB;YACjC0F,eAAe,EAAExF;UAAoB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/H,OAAA;UAAK0H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3H,OAAA,CAACb,YAAY;YAACkN,UAAU,EAAE,EAAG;YAAC7L,QAAQ,EAAEA;UAAS;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD/H,OAAA,CAACT,iBAAiB;YAAC+C,aAAa,EAAEA,aAAc;YAAC9B,QAAQ,EAAEA,QAAS;YAACc,OAAO,EAAEkB;UAAqB;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/H,OAAA;QAAK0H,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD3H,OAAA,CAACX,WAAW;UAACiN,KAAK,EAAE,EAAG;UAAC9L,QAAQ,EAAEA;QAAS;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C/H,OAAA,CAACV,YAAY;UAAC8C,WAAW,EAAEA,WAAY;UAAC5B,QAAQ,EAAEA;QAAS;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/H,OAAA,CAACJ,kBAAkB;MACjB2M,MAAM,EAAEzK,iBAAkB;MAC1B0K,OAAO,EAAErG,wBAAyB;MAClC8F,eAAe,EAAEvG,mBAAoB;MACrCrB,cAAc,EAAE7E,WAAW,CAAC8E,iBAAiB,CAAC,CAAE;MAChDmI,gBAAgB,EAAE,CAAArB,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEnH,IAAI,KAAI;IAAoB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC,eAGF/H,OAAA,CAACH,uBAAuB;MACtB0M,MAAM,EAAEvK,sBAAuB;MAC/BwK,OAAO,EAAE/F,6BAA8B;MACvCyF,oBAAoB,EAAE9F;IAAyB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eAGF/H,OAAA,CAACF,iBAAiB;MAChByM,MAAM,EAAErK,gBAAiB;MACzBsK,OAAO,EAAE3F,uBAAwB;MACjC6F,cAAc,EAAE5F,oBAAqB;MACrCzC,cAAc,EAAE7E,WAAW,CAAC8E,iBAAiB,CAAC,CAAE;MAChDmI,gBAAgB,EAAE,CAAArB,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEnH,IAAI,KAAI;IAAoB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC7H,EAAA,CA9lBID,kBAAkB;EAAA,QACLlB,WAAW;AAAA;AAAA4N,EAAA,GADxB1M,kBAAkB;AAgmBxB,eAAeA,kBAAkB;AAAC,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}