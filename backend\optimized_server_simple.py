#!/usr/bin/env python3
"""
Simplified Optimized Server for Agno WorkSphere
Implements performance optimizations without complex imports
"""

import asyncio
import asyncpg
import uvicorn
import time
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr

# Global connection pool
db_pool = None
cache = {}  # Simple in-memory cache

# Pydantic models
class UserRegistration(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    organization_name: Optional[str] = None
    organization_slug: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

# Security
security = HTTPBearer()

# Create FastAPI app
app = FastAPI(
    title="Agno WorkSphere Optimized API",
    description="Optimized API with connection pooling and caching",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Performance monitoring middleware
@app.middleware("http")
async def performance_middleware(request: Request, call_next):
    """Monitor API performance"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # Log slow requests
    if process_time > 0.5:  # Log requests taking more than 500ms
        print(f"⚠️ Slow request: {request.method} {request.url.path} took {process_time:.3f}s")
    
    return response

# Database connection pool functions
async def init_db_pool():
    """Initialize database connection pool"""
    global db_pool
    try:
        db_pool = await asyncpg.create_pool(
            "postgresql://postgres:admin@localhost:5432/agno_worksphere",
            min_size=5,
            max_size=20,
            max_queries=50000,
            max_inactive_connection_lifetime=300,
            timeout=30,
            command_timeout=60
        )
        print(f"✅ Database pool initialized with {db_pool.get_size()} connections")
    except Exception as e:
        print(f"❌ Failed to initialize database pool: {e}")
        raise

async def close_db_pool():
    """Close database connection pool"""
    global db_pool
    if db_pool:
        await db_pool.close()
        print("✅ Database pool closed")

async def execute_query(query: str, *args):
    """Execute query using pool"""
    async with db_pool.acquire() as conn:
        return await conn.execute(query, *args)

async def fetch_all(query: str, *args):
    """Fetch all rows using pool"""
    async with db_pool.acquire() as conn:
        return await conn.fetch(query, *args)

async def fetch_one(query: str, *args):
    """Fetch one row using pool"""
    async with db_pool.acquire() as conn:
        return await conn.fetchrow(query, *args)

async def fetch_value(query: str, *args):
    """Fetch single value using pool"""
    async with db_pool.acquire() as conn:
        return await conn.fetchval(query, *args)

# Cache functions
def get_cache(key: str):
    """Get from cache"""
    if key in cache:
        data, expiry = cache[key]
        if time.time() < expiry:
            return data
        else:
            del cache[key]
    return None

def set_cache(key: str, value: Any, ttl: int = 300):
    """Set cache with TTL"""
    cache[key] = (value, time.time() + ttl)

def clear_cache_pattern(pattern: str):
    """Clear cache keys matching pattern"""
    keys_to_delete = [k for k in cache.keys() if pattern in k]
    for key in keys_to_delete:
        del cache[key]

# Utility functions
def generate_token(user_id: str) -> str:
    """Generate JWT-like token"""
    import base64
    token_data = {
        "user_id": user_id,
        "exp": (datetime.utcnow() + timedelta(hours=24)).isoformat()
    }
    return base64.b64encode(json.dumps(token_data).encode()).decode()

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Verify and extract user ID from token"""
    try:
        import base64
        token_data = json.loads(base64.b64decode(credentials.credentials).decode())
        return token_data["user_id"]
    except:
        raise HTTPException(status_code=401, detail="Invalid token")

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize on startup"""
    print("🚀 Starting Optimized Agno WorkSphere Server...")
    await init_db_pool()
    print("✅ Server startup complete")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    print("🔄 Shutting down server...")
    await close_db_pool()
    cache.clear()
    print("✅ Server shutdown complete")

# API Endpoints
@app.get("/health")
async def health_check():
    """Optimized health check"""
    pool_stats = {
        "size": db_pool.get_size() if db_pool else 0,
        "min_size": db_pool.get_min_size() if db_pool else 0,
        "max_size": db_pool.get_max_size() if db_pool else 0,
        "idle_size": db_pool.get_idle_size() if db_pool else 0,
        "status": "active" if db_pool else "inactive"
    }
    
    cache_stats = {
        "total_keys": len(cache),
        "cache_type": "in_memory"
    }
    
    return {
        "status": "success",
        "data": {
            "status": "healthy",
            "version": "2.0.0",
            "environment": "development",
            "timestamp": datetime.utcnow().isoformat(),
            "database_pool": pool_stats,
            "cache": cache_stats
        }
    }

@app.post("/api/v1/auth/register")
async def register_user(user_data: UserRegistration):
    """Optimized user registration"""
    try:
        # Check cache first
        cache_key = f"user_email:{user_data.email}"
        existing_user = get_cache(cache_key)
        
        if not existing_user:
            existing_user = await fetch_one(
                "SELECT id, email FROM users WHERE email = $1", 
                user_data.email
            )
            if existing_user:
                set_cache(cache_key, dict(existing_user), ttl=300)
        
        if existing_user:
            raise HTTPException(status_code=409, detail="User already exists")
        
        # Create user and organization
        user_id = str(uuid.uuid4())
        org_id = str(uuid.uuid4())
        
        # Use transaction
        async with db_pool.acquire() as conn:
            async with conn.transaction():
                # Create user
                await conn.execute("""
                    INSERT INTO users (id, email, password_hash, first_name, last_name, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
                """, user_id, user_data.email, "hashed_password", user_data.first_name, user_data.last_name)
                
                # Create organization if provided
                if user_data.organization_name:
                    org_slug = user_data.organization_slug or user_data.organization_name.lower().replace(" ", "-")
                    await conn.execute("""
                        INSERT INTO organizations (id, name, slug, allowed_domains, created_at, updated_at)
                        VALUES ($1, $2, $3, $4, NOW(), NOW())
                    """, org_id, user_data.organization_name, org_slug, ['agnoshin.com', 'agno.com'])
                    
                    # Add user as owner
                    await conn.execute("""
                        INSERT INTO organization_members (user_id, organization_id, role, created_at, updated_at)
                        VALUES ($1, $2, 'owner', NOW(), NOW())
                    """, user_id, org_id)
        
        # Generate token
        access_token = generate_token(user_id)
        
        # Cache user data
        user_cache_data = {
            "id": user_id,
            "email": user_data.email,
            "first_name": user_data.first_name,
            "last_name": user_data.last_name
        }
        set_cache(f"user:{user_id}", user_cache_data, ttl=600)
        set_cache(f"user_email:{user_data.email}", user_cache_data, ttl=600)
        
        return {
            "status": "success",
            "data": {
                "user": user_cache_data,
                "tokens": {
                    "access_token": access_token,
                    "token_type": "bearer"
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Registration error: {e}")
        raise HTTPException(status_code=500, detail="Registration failed")

@app.post("/api/v1/auth/login")
async def login_user(login_data: UserLogin):
    """Optimized user login"""
    try:
        # Check cache first
        cache_key = f"user_email:{login_data.email}"
        user = get_cache(cache_key)
        
        if not user:
            user = await fetch_one(
                "SELECT id, email, first_name, last_name FROM users WHERE email = $1",
                login_data.email
            )
            if user:
                user_dict = dict(user)
                set_cache(f"user:{user_dict['id']}", user_dict, ttl=600)
                set_cache(cache_key, user_dict, ttl=600)
                user = user_dict
        
        if not user:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Generate token
        user_id = user["id"]
        access_token = generate_token(user_id)
        
        return {
            "status": "success",
            "data": {
                "user": user,
                "tokens": {
                    "access_token": access_token,
                    "token_type": "bearer"
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")

@app.get("/api/v1/users/profile")
async def get_user_profile(user_id: str = Depends(verify_token)):
    """Get user profile with caching"""
    try:
        # Check cache first
        cache_key = f"user_profile:{user_id}"
        profile = get_cache(cache_key)
        
        if not profile:
            user = await fetch_one(
                "SELECT id, email, first_name, last_name, created_at FROM users WHERE id = $1",
                user_id
            )
            
            if not user:
                raise HTTPException(status_code=404, detail="User not found")
            
            # Get organizations
            organizations = await fetch_all("""
                SELECT o.id, o.name, o.slug, om.role
                FROM organizations o
                JOIN organization_members om ON o.id = om.organization_id
                WHERE om.user_id = $1
            """, user_id)
            
            profile = dict(user)
            profile["organizations"] = [dict(org) for org in organizations]
            
            # Cache for 5 minutes
            set_cache(cache_key, profile, ttl=300)
        
        return {
            "status": "success",
            "data": profile
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Profile error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get profile")

@app.get("/api/v1/organizations")
async def get_organizations(user_id: str = Depends(verify_token)):
    """Get user organizations with caching"""
    try:
        cache_key = f"user_orgs:{user_id}"
        organizations = get_cache(cache_key)
        
        if not organizations:
            organizations = await fetch_all("""
                SELECT o.id, o.name, o.slug, o.allowed_domains, om.role
                FROM organizations o
                JOIN organization_members om ON o.id = om.organization_id
                WHERE om.user_id = $1
            """, user_id)
            organizations = [dict(org) for org in organizations]
            set_cache(cache_key, organizations, ttl=300)
        
        return {
            "status": "success",
            "data": organizations
        }
        
    except Exception as e:
        print(f"Organizations error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get organizations")

@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats(user_id: str = Depends(verify_token)):
    """Get dashboard statistics with caching"""
    try:
        cache_key = f"dashboard_stats:{user_id}"
        stats = get_cache(cache_key)
        
        if not stats:
            # Get user organizations
            user_orgs = await fetch_all("""
                SELECT organization_id FROM organization_members WHERE user_id = $1
            """, user_id)
            
            if not user_orgs:
                stats = {
                    "total_projects": 0,
                    "total_boards": 0,
                    "total_cards": 0,
                    "organizations": 0
                }
            else:
                org_ids = [org["organization_id"] for org in user_orgs]
                
                # Get statistics efficiently
                stats = {}
                stats["total_projects"] = await fetch_value(
                    "SELECT COUNT(*) FROM projects WHERE organization_id = ANY($1)",
                    org_ids
                )
                stats["total_boards"] = await fetch_value("""
                    SELECT COUNT(*) FROM boards b
                    JOIN projects p ON b.project_id = p.id
                    WHERE p.organization_id = ANY($1)
                """, org_ids)
                stats["total_cards"] = await fetch_value("""
                    SELECT COUNT(*) FROM cards c
                    JOIN board_columns bc ON c.column_id = bc.id
                    JOIN boards b ON bc.board_id = b.id
                    JOIN projects p ON b.project_id = p.id
                    WHERE p.organization_id = ANY($1)
                """, org_ids)
                stats["organizations"] = len(org_ids)
            
            # Cache for 3 minutes
            set_cache(cache_key, stats, ttl=180)
        
        return {
            "status": "success",
            "data": stats
        }
        
    except Exception as e:
        print(f"Dashboard stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard stats")

@app.get("/api/v1/projects")
async def get_projects(user_id: str = Depends(verify_token)):
    """Get user projects with caching"""
    try:
        cache_key = f"user_projects:{user_id}"
        projects = get_cache(cache_key)
        
        if not projects:
            # Get user organizations
            user_orgs = await fetch_all("""
                SELECT organization_id FROM organization_members WHERE user_id = $1
            """, user_id)
            
            if not user_orgs:
                projects = []
            else:
                org_ids = [org["organization_id"] for org in user_orgs]
                projects = await fetch_all("""
                    SELECT id, name, description, organization_id, created_at
                    FROM projects WHERE organization_id = ANY($1)
                """, org_ids)
                projects = [dict(project) for project in projects]
            
            set_cache(cache_key, projects, ttl=300)
        
        return {
            "status": "success",
            "data": projects
        }
        
    except Exception as e:
        print(f"Projects error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get projects")

@app.get("/api/v1/boards")
async def get_boards(user_id: str = Depends(verify_token)):
    """Get user boards with caching"""
    try:
        cache_key = f"user_boards:{user_id}"
        boards = get_cache(cache_key)
        
        if not boards:
            # Get user organizations
            user_orgs = await fetch_all("""
                SELECT organization_id FROM organization_members WHERE user_id = $1
            """, user_id)
            
            if not user_orgs:
                boards = []
            else:
                org_ids = [org["organization_id"] for org in user_orgs]
                boards = await fetch_all("""
                    SELECT b.id, b.name, b.description, b.project_id, p.name as project_name
                    FROM boards b
                    JOIN projects p ON b.project_id = p.id
                    WHERE p.organization_id = ANY($1)
                """, org_ids)
                boards = [dict(board) for board in boards]
            
            set_cache(cache_key, boards, ttl=300)
        
        return {
            "status": "success",
            "data": boards
        }
        
    except Exception as e:
        print(f"Boards error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get boards")

if __name__ == "__main__":
    print("🚀 Starting Optimized Agno WorkSphere Server...")
    uvicorn.run(
        "optimized_server_simple:app",
        host="0.0.0.0",
        port=3001,
        reload=False,
        workers=1,
        log_level="info"
    )
