{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\analytics\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Analytics = () => {\n  _s();\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [analyticsData, setAnalyticsData] = useState({\n    userActivity: {},\n    organizationPerformance: {},\n    projectStats: {},\n    usageAnalytics: {}\n  });\n\n  // Time period filter\n  const [timePeriod, setTimePeriod] = useState('30d');\n  const timePeriods = [{\n    value: '7d',\n    label: 'Last 7 days'\n  }, {\n    value: '30d',\n    label: 'Last 30 days'\n  }, {\n    value: '90d',\n    label: 'Last 3 months'\n  }, {\n    value: '1y',\n    label: 'Last year'\n  }];\n  useEffect(() => {\n    loadUserData();\n    loadAnalyticsData();\n  }, [timePeriod]);\n  const loadUserData = async () => {\n    try {\n      const user = await authService.getCurrentUser();\n      const role = await authService.getUserRole();\n      const organization = await authService.getCurrentOrganization();\n      setCurrentUser(user);\n      setUserRole(role);\n      setCurrentOrganization(organization);\n    } catch (error) {\n      console.error('Failed to load user data:', error);\n    }\n  };\n  const loadAnalyticsData = async () => {\n    try {\n      setLoading(true);\n\n      // Load analytics data from API\n      const [userActivity, orgPerformance, projectStats, usageData] = await Promise.all([apiService.getUserActivityAnalytics(timePeriod), apiService.getOrganizationPerformance(timePeriod), apiService.getProjectStatistics(timePeriod), apiService.getUsageAnalytics(timePeriod)]);\n      setAnalyticsData({\n        userActivity: userActivity || generateMockUserActivity(),\n        organizationPerformance: orgPerformance || generateMockOrgPerformance(),\n        projectStats: projectStats || generateMockProjectStats(),\n        usageAnalytics: usageData || generateMockUsageAnalytics()\n      });\n    } catch (error) {\n      console.error('Failed to load analytics data:', error);\n      // Use mock data as fallback\n      setAnalyticsData({\n        userActivity: generateMockUserActivity(),\n        organizationPerformance: generateMockOrgPerformance(),\n        projectStats: generateMockProjectStats(),\n        usageAnalytics: generateMockUsageAnalytics()\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mock data generators for demonstration\n  const generateMockUserActivity = () => ({\n    totalUsers: 24,\n    activeUsers: 18,\n    newUsers: 3,\n    userGrowth: 12.5,\n    dailyActiveUsers: [{\n      date: '2024-01-01',\n      users: 15\n    }, {\n      date: '2024-01-02',\n      users: 18\n    }, {\n      date: '2024-01-03',\n      users: 16\n    }, {\n      date: '2024-01-04',\n      users: 20\n    }, {\n      date: '2024-01-05',\n      users: 22\n    }, {\n      date: '2024-01-06',\n      users: 19\n    }, {\n      date: '2024-01-07',\n      users: 24\n    }],\n    topUsers: [{\n      name: 'John Doe',\n      email: '<EMAIL>',\n      activity: 95\n    }, {\n      name: 'Jane Smith',\n      email: '<EMAIL>',\n      activity: 87\n    }, {\n      name: 'Bob Johnson',\n      email: '<EMAIL>',\n      activity: 76\n    }]\n  });\n  const generateMockOrgPerformance = () => ({\n    totalProjects: 12,\n    completedProjects: 8,\n    activeProjects: 4,\n    completionRate: 66.7,\n    averageProjectDuration: 45,\n    teamProductivity: 78,\n    performanceMetrics: [{\n      metric: 'Task Completion Rate',\n      value: 85,\n      trend: 'up'\n    }, {\n      metric: 'Team Collaboration',\n      value: 92,\n      trend: 'up'\n    }, {\n      metric: 'Project Delivery',\n      value: 78,\n      trend: 'down'\n    }, {\n      metric: 'Resource Utilization',\n      value: 88,\n      trend: 'up'\n    }]\n  });\n  const generateMockProjectStats = () => ({\n    totalTasks: 156,\n    completedTasks: 124,\n    inProgressTasks: 24,\n    overdueTasks: 8,\n    taskCompletionRate: 79.5,\n    averageTaskDuration: 3.2,\n    projectsByStatus: [{\n      status: 'Completed',\n      count: 8,\n      percentage: 66.7\n    }, {\n      status: 'In Progress',\n      count: 3,\n      percentage: 25\n    }, {\n      status: 'Planning',\n      count: 1,\n      percentage: 8.3\n    }]\n  });\n  const generateMockUsageAnalytics = () => ({\n    totalSessions: 342,\n    averageSessionDuration: 28,\n    pageViews: 1456,\n    bounceRate: 23,\n    mostUsedFeatures: [{\n      feature: 'Kanban Board',\n      usage: 89\n    }, {\n      feature: 'Team Chat',\n      usage: 76\n    }, {\n      feature: 'File Sharing',\n      usage: 65\n    }, {\n      feature: 'Time Tracking',\n      usage: 54\n    }],\n    deviceBreakdown: [{\n      device: 'Desktop',\n      percentage: 68\n    }, {\n      device: 'Mobile',\n      percentage: 24\n    }, {\n      device: 'Tablet',\n      percentage: 8\n    }]\n  });\n\n  // Check if user has access to analytics\n  if (userRole !== 'owner' && userRole !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser,\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"pt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Lock\",\n                size: 32,\n                className: \"mx-auto mb-4 text-text-secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-text-primary mb-2\",\n                children: \"Access Restricted\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary mb-4\",\n                children: \"Analytics are only available to Owner and Admin users.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => window.history.back(),\n                children: \"Go Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName} ${currentUser.lastName}`,\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"pt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-text-primary\",\n              children: \"Analytics Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary mt-2\",\n              children: \"Comprehensive insights into your organization's performance and usage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              value: timePeriod,\n              onChange: e => setTimePeriod(e.target.value),\n              className: \"px-4 py-2 border border-border rounded-lg bg-card text-text-primary focus:outline-none focus:ring-2 focus:ring-primary\",\n              children: timePeriods.map(period => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: period.value,\n                children: period.label\n              }, period.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              iconName: \"Download\",\n              children: \"Export Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary\",\n              children: \"Loading analytics data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card rounded-lg border border-border p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-text-secondary\",\n                    children: \"Total Users\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-text-primary\",\n                    children: analyticsData.userActivity.totalUsers\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Users\",\n                    size: 24,\n                    className: \"text-primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"TrendingUp\",\n                  size: 16,\n                  className: \"text-success mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-success\",\n                  children: [\"+\", analyticsData.userActivity.userGrowth, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-text-secondary ml-2\",\n                  children: \"vs last period\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card rounded-lg border border-border p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-text-secondary\",\n                    children: \"Active Projects\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-text-primary\",\n                    children: analyticsData.organizationPerformance.activeProjects\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"FolderOpen\",\n                    size: 24,\n                    className: \"text-accent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"TrendingUp\",\n                  size: 16,\n                  className: \"text-success mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-success\",\n                  children: \"+2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-text-secondary ml-2\",\n                  children: \"new this month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card rounded-lg border border-border p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-text-secondary\",\n                    children: \"Completion Rate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-text-primary\",\n                    children: [analyticsData.projectStats.taskCompletionRate, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"CheckCircle\",\n                    size: 24,\n                    className: \"text-success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"TrendingUp\",\n                  size: 16,\n                  className: \"text-success mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-success\",\n                  children: \"+5.2%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-text-secondary ml-2\",\n                  children: \"improvement\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card rounded-lg border border-border p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-text-secondary\",\n                    children: \"Team Productivity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-text-primary\",\n                    children: [analyticsData.organizationPerformance.teamProductivity, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Zap\",\n                    size: 24,\n                    className: \"text-warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"TrendingUp\",\n                  size: 16,\n                  className: \"text-success mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-success\",\n                  children: \"+3.1%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-text-secondary ml-2\",\n                  children: \"this week\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card rounded-lg border border-border p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-text-primary\",\n                  children: \"User Activity Trends\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"BarChart3\",\n                  size: 20,\n                  className: \"text-text-secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: analyticsData.userActivity.dailyActiveUsers.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-text-secondary\",\n                    children: new Date(day.date).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-32 bg-muted rounded-full h-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-primary h-2 rounded-full\",\n                        style: {\n                          width: `${day.users / 24 * 100}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 324,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-text-primary w-8\",\n                      children: day.users\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card rounded-lg border border-border p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-text-primary\",\n                  children: \"Performance Metrics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"Target\",\n                  size: 20,\n                  className: \"text-text-secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: analyticsData.organizationPerformance.performanceMetrics.map((metric, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-text-secondary\",\n                    children: metric.metric\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-24 bg-muted rounded-full h-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-primary h-2 rounded-full\",\n                        style: {\n                          width: `${metric.value}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-text-primary w-8\",\n                      children: [metric.value, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Icon, {\n                      name: metric.trend === 'up' ? 'TrendingUp' : 'TrendingDown',\n                      size: 16,\n                      className: metric.trend === 'up' ? 'text-success' : 'text-destructive'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"qjhDKmG2xEYUtUz6nNISFvzJSuY=\");\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "RoleBasedHeader", "Breadcrumb", "Icon", "<PERSON><PERSON>", "authService", "apiService", "jsxDEV", "_jsxDEV", "Analytics", "_s", "currentUser", "setCurrentUser", "userRole", "setUserRole", "currentOrganization", "setCurrentOrganization", "loading", "setLoading", "analyticsData", "setAnalyticsData", "userActivity", "organizationPerformance", "projectStats", "usageAnalytics", "timePeriod", "setTimePeriod", "timePeriods", "value", "label", "loadUserData", "loadAnalyticsData", "user", "getCurrentUser", "role", "getUserRole", "organization", "getCurrentOrganization", "error", "console", "orgPerformance", "usageData", "Promise", "all", "getUserActivityAnalytics", "getOrganizationPerformance", "getProjectStatistics", "getUsageAnalytics", "generateMockUserActivity", "generateMockOrgPerformance", "generateMockProjectStats", "generateMockUsageAnalytics", "totalUsers", "activeUsers", "newUsers", "userGrowth", "dailyActiveUsers", "date", "users", "topUsers", "name", "email", "activity", "totalProjects", "completedProjects", "activeProjects", "completionRate", "averageProjectDuration", "teamProductivity", "performanceMetrics", "metric", "trend", "totalTasks", "completedTasks", "inProgressTasks", "overdueTasks", "taskCompletionRate", "averageTaskDuration", "projectsByStatus", "status", "count", "percentage", "totalSessions", "averageSessionDuration", "pageViews", "bounceRate", "mostUsedFeatures", "feature", "usage", "deviceBreakdown", "device", "className", "children", "toLowerCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "window", "history", "back", "firstName", "lastName", "avatar", "onChange", "e", "target", "map", "period", "variant", "iconName", "day", "index", "Date", "toLocaleDateString", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/analytics/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\n\nconst Analytics = () => {\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [analyticsData, setAnalyticsData] = useState({\n    userActivity: {},\n    organizationPerformance: {},\n    projectStats: {},\n    usageAnalytics: {}\n  });\n\n  // Time period filter\n  const [timePeriod, setTimePeriod] = useState('30d');\n  const timePeriods = [\n    { value: '7d', label: 'Last 7 days' },\n    { value: '30d', label: 'Last 30 days' },\n    { value: '90d', label: 'Last 3 months' },\n    { value: '1y', label: 'Last year' }\n  ];\n\n  useEffect(() => {\n    loadUserData();\n    loadAnalyticsData();\n  }, [timePeriod]);\n\n  const loadUserData = async () => {\n    try {\n      const user = await authService.getCurrentUser();\n      const role = await authService.getUserRole();\n      const organization = await authService.getCurrentOrganization();\n      \n      setCurrentUser(user);\n      setUserRole(role);\n      setCurrentOrganization(organization);\n    } catch (error) {\n      console.error('Failed to load user data:', error);\n    }\n  };\n\n  const loadAnalyticsData = async () => {\n    try {\n      setLoading(true);\n      \n      // Load analytics data from API\n      const [userActivity, orgPerformance, projectStats, usageData] = await Promise.all([\n        apiService.getUserActivityAnalytics(timePeriod),\n        apiService.getOrganizationPerformance(timePeriod),\n        apiService.getProjectStatistics(timePeriod),\n        apiService.getUsageAnalytics(timePeriod)\n      ]);\n\n      setAnalyticsData({\n        userActivity: userActivity || generateMockUserActivity(),\n        organizationPerformance: orgPerformance || generateMockOrgPerformance(),\n        projectStats: projectStats || generateMockProjectStats(),\n        usageAnalytics: usageData || generateMockUsageAnalytics()\n      });\n    } catch (error) {\n      console.error('Failed to load analytics data:', error);\n      // Use mock data as fallback\n      setAnalyticsData({\n        userActivity: generateMockUserActivity(),\n        organizationPerformance: generateMockOrgPerformance(),\n        projectStats: generateMockProjectStats(),\n        usageAnalytics: generateMockUsageAnalytics()\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mock data generators for demonstration\n  const generateMockUserActivity = () => ({\n    totalUsers: 24,\n    activeUsers: 18,\n    newUsers: 3,\n    userGrowth: 12.5,\n    dailyActiveUsers: [\n      { date: '2024-01-01', users: 15 },\n      { date: '2024-01-02', users: 18 },\n      { date: '2024-01-03', users: 16 },\n      { date: '2024-01-04', users: 20 },\n      { date: '2024-01-05', users: 22 },\n      { date: '2024-01-06', users: 19 },\n      { date: '2024-01-07', users: 24 }\n    ],\n    topUsers: [\n      { name: 'John Doe', email: '<EMAIL>', activity: 95 },\n      { name: 'Jane Smith', email: '<EMAIL>', activity: 87 },\n      { name: 'Bob Johnson', email: '<EMAIL>', activity: 76 }\n    ]\n  });\n\n  const generateMockOrgPerformance = () => ({\n    totalProjects: 12,\n    completedProjects: 8,\n    activeProjects: 4,\n    completionRate: 66.7,\n    averageProjectDuration: 45,\n    teamProductivity: 78,\n    performanceMetrics: [\n      { metric: 'Task Completion Rate', value: 85, trend: 'up' },\n      { metric: 'Team Collaboration', value: 92, trend: 'up' },\n      { metric: 'Project Delivery', value: 78, trend: 'down' },\n      { metric: 'Resource Utilization', value: 88, trend: 'up' }\n    ]\n  });\n\n  const generateMockProjectStats = () => ({\n    totalTasks: 156,\n    completedTasks: 124,\n    inProgressTasks: 24,\n    overdueTasks: 8,\n    taskCompletionRate: 79.5,\n    averageTaskDuration: 3.2,\n    projectsByStatus: [\n      { status: 'Completed', count: 8, percentage: 66.7 },\n      { status: 'In Progress', count: 3, percentage: 25 },\n      { status: 'Planning', count: 1, percentage: 8.3 }\n    ]\n  });\n\n  const generateMockUsageAnalytics = () => ({\n    totalSessions: 342,\n    averageSessionDuration: 28,\n    pageViews: 1456,\n    bounceRate: 23,\n    mostUsedFeatures: [\n      { feature: 'Kanban Board', usage: 89 },\n      { feature: 'Team Chat', usage: 76 },\n      { feature: 'File Sharing', usage: 65 },\n      { feature: 'Time Tracking', usage: 54 }\n    ],\n    deviceBreakdown: [\n      { device: 'Desktop', percentage: 68 },\n      { device: 'Mobile', percentage: 24 },\n      { device: 'Tablet', percentage: 8 }\n    ]\n  });\n\n  // Check if user has access to analytics\n  if (userRole !== 'owner' && userRole !== 'admin') {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser}\n          currentOrganization={currentOrganization}\n        />\n        <main className=\"pt-16\">\n          <div className=\"max-w-7xl mx-auto p-6\">\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <Icon name=\"Lock\" size={32} className=\"mx-auto mb-4 text-text-secondary\" />\n                <h3 className=\"text-lg font-medium text-text-primary mb-2\">Access Restricted</h3>\n                <p className=\"text-text-secondary mb-4\">Analytics are only available to Owner and Admin users.</p>\n                <Button onClick={() => window.history.back()}>\n                  Go Back\n                </Button>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      <main className=\"pt-16\">\n        <div className=\"max-w-7xl mx-auto p-6\">\n          <Breadcrumb />\n          \n          {/* Page Header */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-text-primary\">Analytics Dashboard</h1>\n              <p className=\"text-text-secondary mt-2\">\n                Comprehensive insights into your organization's performance and usage\n              </p>\n            </div>\n            \n            {/* Time Period Filter */}\n            <div className=\"flex items-center space-x-4\">\n              <select\n                value={timePeriod}\n                onChange={(e) => setTimePeriod(e.target.value)}\n                className=\"px-4 py-2 border border-border rounded-lg bg-card text-text-primary focus:outline-none focus:ring-2 focus:ring-primary\"\n              >\n                {timePeriods.map((period) => (\n                  <option key={period.value} value={period.value}>\n                    {period.label}\n                  </option>\n                ))}\n              </select>\n              \n              <Button variant=\"outline\" iconName=\"Download\">\n                Export Report\n              </Button>\n            </div>\n          </div>\n\n          {loading ? (\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n                <p className=\"text-text-secondary\">Loading analytics data...</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"space-y-8\">\n              {/* Key Metrics Overview */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                <div className=\"bg-card rounded-lg border border-border p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-text-secondary\">Total Users</p>\n                      <p className=\"text-2xl font-bold text-text-primary\">{analyticsData.userActivity.totalUsers}</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center\">\n                      <Icon name=\"Users\" size={24} className=\"text-primary\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center\">\n                    <Icon name=\"TrendingUp\" size={16} className=\"text-success mr-1\" />\n                    <span className=\"text-sm text-success\">+{analyticsData.userActivity.userGrowth}%</span>\n                    <span className=\"text-sm text-text-secondary ml-2\">vs last period</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-card rounded-lg border border-border p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-text-secondary\">Active Projects</p>\n                      <p className=\"text-2xl font-bold text-text-primary\">{analyticsData.organizationPerformance.activeProjects}</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center\">\n                      <Icon name=\"FolderOpen\" size={24} className=\"text-accent\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center\">\n                    <Icon name=\"TrendingUp\" size={16} className=\"text-success mr-1\" />\n                    <span className=\"text-sm text-success\">+2</span>\n                    <span className=\"text-sm text-text-secondary ml-2\">new this month</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-card rounded-lg border border-border p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-text-secondary\">Completion Rate</p>\n                      <p className=\"text-2xl font-bold text-text-primary\">{analyticsData.projectStats.taskCompletionRate}%</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center\">\n                      <Icon name=\"CheckCircle\" size={24} className=\"text-success\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center\">\n                    <Icon name=\"TrendingUp\" size={16} className=\"text-success mr-1\" />\n                    <span className=\"text-sm text-success\">+5.2%</span>\n                    <span className=\"text-sm text-text-secondary ml-2\">improvement</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-card rounded-lg border border-border p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-text-secondary\">Team Productivity</p>\n                      <p className=\"text-2xl font-bold text-text-primary\">{analyticsData.organizationPerformance.teamProductivity}%</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center\">\n                      <Icon name=\"Zap\" size={24} className=\"text-warning\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center\">\n                    <Icon name=\"TrendingUp\" size={16} className=\"text-success mr-1\" />\n                    <span className=\"text-sm text-success\">+3.1%</span>\n                    <span className=\"text-sm text-text-secondary ml-2\">this week</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Charts and Detailed Analytics */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n                {/* User Activity Chart */}\n                <div className=\"bg-card rounded-lg border border-border p-6\">\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <h3 className=\"text-lg font-semibold text-text-primary\">User Activity Trends</h3>\n                    <Icon name=\"BarChart3\" size={20} className=\"text-text-secondary\" />\n                  </div>\n                  \n                  <div className=\"space-y-4\">\n                    {analyticsData.userActivity.dailyActiveUsers.map((day, index) => (\n                      <div key={index} className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-text-secondary\">{new Date(day.date).toLocaleDateString()}</span>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"w-32 bg-muted rounded-full h-2\">\n                            <div \n                              className=\"bg-primary h-2 rounded-full\" \n                              style={{ width: `${(day.users / 24) * 100}%` }}\n                            ></div>\n                          </div>\n                          <span className=\"text-sm font-medium text-text-primary w-8\">{day.users}</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Performance Metrics */}\n                <div className=\"bg-card rounded-lg border border-border p-6\">\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <h3 className=\"text-lg font-semibold text-text-primary\">Performance Metrics</h3>\n                    <Icon name=\"Target\" size={20} className=\"text-text-secondary\" />\n                  </div>\n                  \n                  <div className=\"space-y-4\">\n                    {analyticsData.organizationPerformance.performanceMetrics.map((metric, index) => (\n                      <div key={index} className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-text-secondary\">{metric.metric}</span>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"w-24 bg-muted rounded-full h-2\">\n                            <div \n                              className=\"bg-primary h-2 rounded-full\" \n                              style={{ width: `${metric.value}%` }}\n                            ></div>\n                          </div>\n                          <span className=\"text-sm font-medium text-text-primary w-8\">{metric.value}%</span>\n                          <Icon \n                            name={metric.trend === 'up' ? 'TrendingUp' : 'TrendingDown'} \n                            size={16} \n                            className={metric.trend === 'up' ? 'text-success' : 'text-destructive'} \n                          />\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Analytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,UAAU,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACgB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC;IACjDsB,YAAY,EAAE,CAAC,CAAC;IAChBC,uBAAuB,EAAE,CAAC,CAAC;IAC3BC,YAAY,EAAE,CAAC,CAAC;IAChBC,cAAc,EAAE,CAAC;EACnB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM4B,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAc,CAAC,EACrC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACxC;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAY,CAAC,CACpC;EAED7B,SAAS,CAAC,MAAM;IACd8B,YAAY,CAAC,CAAC;IACdC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;EAEhB,MAAMK,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAME,IAAI,GAAG,MAAM3B,WAAW,CAAC4B,cAAc,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAG,MAAM7B,WAAW,CAAC8B,WAAW,CAAC,CAAC;MAC5C,MAAMC,YAAY,GAAG,MAAM/B,WAAW,CAACgC,sBAAsB,CAAC,CAAC;MAE/DzB,cAAc,CAACoB,IAAI,CAAC;MACpBlB,WAAW,CAACoB,IAAI,CAAC;MACjBlB,sBAAsB,CAACoB,YAAY,CAAC;IACtC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMP,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACG,YAAY,EAAEmB,cAAc,EAAEjB,YAAY,EAAEkB,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChFrC,UAAU,CAACsC,wBAAwB,CAACnB,UAAU,CAAC,EAC/CnB,UAAU,CAACuC,0BAA0B,CAACpB,UAAU,CAAC,EACjDnB,UAAU,CAACwC,oBAAoB,CAACrB,UAAU,CAAC,EAC3CnB,UAAU,CAACyC,iBAAiB,CAACtB,UAAU,CAAC,CACzC,CAAC;MAEFL,gBAAgB,CAAC;QACfC,YAAY,EAAEA,YAAY,IAAI2B,wBAAwB,CAAC,CAAC;QACxD1B,uBAAuB,EAAEkB,cAAc,IAAIS,0BAA0B,CAAC,CAAC;QACvE1B,YAAY,EAAEA,YAAY,IAAI2B,wBAAwB,CAAC,CAAC;QACxD1B,cAAc,EAAEiB,SAAS,IAAIU,0BAA0B,CAAC;MAC1D,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACAlB,gBAAgB,CAAC;QACfC,YAAY,EAAE2B,wBAAwB,CAAC,CAAC;QACxC1B,uBAAuB,EAAE2B,0BAA0B,CAAC,CAAC;QACrD1B,YAAY,EAAE2B,wBAAwB,CAAC,CAAC;QACxC1B,cAAc,EAAE2B,0BAA0B,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8B,wBAAwB,GAAGA,CAAA,MAAO;IACtCI,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,CAChB;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAG,CAAC,EACjC;MAAED,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAG,CAAC,EACjC;MAAED,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAG,CAAC,EACjC;MAAED,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAG,CAAC,EACjC;MAAED,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAG,CAAC,EACjC;MAAED,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAG,CAAC,EACjC;MAAED,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAG,CAAC,CAClC;IACDC,QAAQ,EAAE,CACR;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,eAAe;MAAEC,QAAQ,EAAE;IAAG,CAAC,EAC1D;MAAEF,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,eAAe;MAAEC,QAAQ,EAAE;IAAG,CAAC,EAC5D;MAAEF,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,cAAc;MAAEC,QAAQ,EAAE;IAAG,CAAC;EAEhE,CAAC,CAAC;EAEF,MAAMb,0BAA0B,GAAGA,CAAA,MAAO;IACxCc,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,IAAI;IACpBC,sBAAsB,EAAE,EAAE;IAC1BC,gBAAgB,EAAE,EAAE;IACpBC,kBAAkB,EAAE,CAClB;MAAEC,MAAM,EAAE,sBAAsB;MAAE1C,KAAK,EAAE,EAAE;MAAE2C,KAAK,EAAE;IAAK,CAAC,EAC1D;MAAED,MAAM,EAAE,oBAAoB;MAAE1C,KAAK,EAAE,EAAE;MAAE2C,KAAK,EAAE;IAAK,CAAC,EACxD;MAAED,MAAM,EAAE,kBAAkB;MAAE1C,KAAK,EAAE,EAAE;MAAE2C,KAAK,EAAE;IAAO,CAAC,EACxD;MAAED,MAAM,EAAE,sBAAsB;MAAE1C,KAAK,EAAE,EAAE;MAAE2C,KAAK,EAAE;IAAK,CAAC;EAE9D,CAAC,CAAC;EAEF,MAAMrB,wBAAwB,GAAGA,CAAA,MAAO;IACtCsB,UAAU,EAAE,GAAG;IACfC,cAAc,EAAE,GAAG;IACnBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,IAAI;IACxBC,mBAAmB,EAAE,GAAG;IACxBC,gBAAgB,EAAE,CAChB;MAAEC,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAK,CAAC,EACnD;MAAEF,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAG,CAAC,EACnD;MAAEF,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAI,CAAC;EAErD,CAAC,CAAC;EAEF,MAAM9B,0BAA0B,GAAGA,CAAA,MAAO;IACxC+B,aAAa,EAAE,GAAG;IAClBC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,CAChB;MAAEC,OAAO,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAG,CAAC,EACtC;MAAED,OAAO,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAG,CAAC,EACnC;MAAED,OAAO,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAG,CAAC,EACtC;MAAED,OAAO,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAG,CAAC,CACxC;IACDC,eAAe,EAAE,CACf;MAAEC,MAAM,EAAE,SAAS;MAAET,UAAU,EAAE;IAAG,CAAC,EACrC;MAAES,MAAM,EAAE,QAAQ;MAAET,UAAU,EAAE;IAAG,CAAC,EACpC;MAAES,MAAM,EAAE,QAAQ;MAAET,UAAU,EAAE;IAAE,CAAC;EAEvC,CAAC,CAAC;;EAEF;EACA,IAAIpE,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,OAAO,EAAE;IAChD,oBACEL,OAAA;MAAKmF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCpF,OAAA,CAACP,eAAe;QACdY,QAAQ,EAAEA,QAAQ,CAACgF,WAAW,CAAC,CAAE;QACjClF,WAAW,EAAEA,WAAY;QACzBI,mBAAmB,EAAEA;MAAoB;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACFzF,OAAA;QAAMmF,SAAS,EAAC,OAAO;QAAAC,QAAA,eACrBpF,OAAA;UAAKmF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCpF,OAAA;YAAKmF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDpF,OAAA;cAAKmF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpF,OAAA,CAACL,IAAI;gBAACyD,IAAI,EAAC,MAAM;gBAACsC,IAAI,EAAE,EAAG;gBAACP,SAAS,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3EzF,OAAA;gBAAImF,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFzF,OAAA;gBAAGmF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAsD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClGzF,OAAA,CAACJ,MAAM;gBAAC+F,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;gBAAAV,QAAA,EAAC;cAE9C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACEzF,OAAA;IAAKmF,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzCpF,OAAA,CAACP,eAAe;MACdY,QAAQ,EAAEA,QAAQ,CAACgF,WAAW,CAAC,CAAE;MACjClF,WAAW,EAAEA,WAAW,GAAG;QACzBiD,IAAI,EAAE,GAAGjD,WAAW,CAAC4F,SAAS,IAAI5F,WAAW,CAAC6F,QAAQ,EAAE;QACxD3C,KAAK,EAAElD,WAAW,CAACkD,KAAK;QACxB4C,MAAM,EAAE9F,WAAW,CAAC8F,MAAM,IAAI,2BAA2B;QACzDvE,IAAI,EAAErB;MACR,CAAC,GAAG;QACF+C,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,EAAE;QACT4C,MAAM,EAAE,2BAA2B;QACnCvE,IAAI,EAAErB;MACR,CAAE;MACFE,mBAAmB,EAAEA;IAAoB;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAEFzF,OAAA;MAAMmF,SAAS,EAAC,OAAO;MAAAC,QAAA,eACrBpF,OAAA;QAAKmF,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCpF,OAAA,CAACN,UAAU;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGdzF,OAAA;UAAKmF,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDpF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAImF,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EzF,OAAA;cAAGmF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAExC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNzF,OAAA;YAAKmF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CpF,OAAA;cACEoB,KAAK,EAAEH,UAAW;cAClBiF,QAAQ,EAAGC,CAAC,IAAKjF,aAAa,CAACiF,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAE;cAC/C+D,SAAS,EAAC,wHAAwH;cAAAC,QAAA,EAEjIjE,WAAW,CAACkF,GAAG,CAAEC,MAAM,iBACtBtG,OAAA;gBAA2BoB,KAAK,EAAEkF,MAAM,CAAClF,KAAM;gBAAAgE,QAAA,EAC5CkB,MAAM,CAACjF;cAAK,GADFiF,MAAM,CAAClF,KAAK;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAETzF,OAAA,CAACJ,MAAM;cAAC2G,OAAO,EAAC,SAAS;cAACC,QAAQ,EAAC,UAAU;cAAApB,QAAA,EAAC;YAE9C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELhF,OAAO,gBACNT,OAAA;UAAKmF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDpF,OAAA;YAAKmF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BpF,OAAA;cAAKmF,SAAS,EAAC;YAA0E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChGzF,OAAA;cAAGmF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENzF,OAAA;UAAKmF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBpF,OAAA;YAAKmF,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEpF,OAAA;cAAKmF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DpF,OAAA;gBAAKmF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAGmF,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACtEzF,OAAA;oBAAGmF,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAEzE,aAAa,CAACE,YAAY,CAAC+B;kBAAU;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACNzF,OAAA;kBAAKmF,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFpF,OAAA,CAACL,IAAI;oBAACyD,IAAI,EAAC,OAAO;oBAACsC,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzF,OAAA;gBAAKmF,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpF,OAAA,CAACL,IAAI;kBAACyD,IAAI,EAAC,YAAY;kBAACsC,IAAI,EAAE,EAAG;kBAACP,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEzF,OAAA;kBAAMmF,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAC,GAAC,EAACzE,aAAa,CAACE,YAAY,CAACkC,UAAU,EAAC,GAAC;gBAAA;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvFzF,OAAA;kBAAMmF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzF,OAAA;cAAKmF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DpF,OAAA;gBAAKmF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAGmF,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1EzF,OAAA;oBAAGmF,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAEzE,aAAa,CAACG,uBAAuB,CAAC2C;kBAAc;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3G,CAAC,eACNzF,OAAA;kBAAKmF,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,eACjFpF,OAAA,CAACL,IAAI;oBAACyD,IAAI,EAAC,YAAY;oBAACsC,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzF,OAAA;gBAAKmF,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpF,OAAA,CAACL,IAAI;kBAACyD,IAAI,EAAC,YAAY;kBAACsC,IAAI,EAAE,EAAG;kBAACP,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEzF,OAAA;kBAAMmF,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDzF,OAAA;kBAAMmF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzF,OAAA;cAAKmF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DpF,OAAA;gBAAKmF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAGmF,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1EzF,OAAA;oBAAGmF,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,GAAEzE,aAAa,CAACI,YAAY,CAACqD,kBAAkB,EAAC,GAAC;kBAAA;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,eACNzF,OAAA;kBAAKmF,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFpF,OAAA,CAACL,IAAI;oBAACyD,IAAI,EAAC,aAAa;oBAACsC,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzF,OAAA;gBAAKmF,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpF,OAAA,CAACL,IAAI;kBAACyD,IAAI,EAAC,YAAY;kBAACsC,IAAI,EAAE,EAAG;kBAACP,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEzF,OAAA;kBAAMmF,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDzF,OAAA;kBAAMmF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzF,OAAA;cAAKmF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DpF,OAAA;gBAAKmF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAGmF,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5EzF,OAAA;oBAAGmF,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,GAAEzE,aAAa,CAACG,uBAAuB,CAAC8C,gBAAgB,EAAC,GAAC;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9G,CAAC,eACNzF,OAAA;kBAAKmF,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFpF,OAAA,CAACL,IAAI;oBAACyD,IAAI,EAAC,KAAK;oBAACsC,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzF,OAAA;gBAAKmF,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpF,OAAA,CAACL,IAAI;kBAACyD,IAAI,EAAC,YAAY;kBAACsC,IAAI,EAAE,EAAG;kBAACP,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEzF,OAAA;kBAAMmF,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDzF,OAAA;kBAAMmF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzF,OAAA;YAAKmF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpDpF,OAAA;cAAKmF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DpF,OAAA;gBAAKmF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDpF,OAAA;kBAAImF,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjFzF,OAAA,CAACL,IAAI;kBAACyD,IAAI,EAAC,WAAW;kBAACsC,IAAI,EAAE,EAAG;kBAACP,SAAS,EAAC;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eAENzF,OAAA;gBAAKmF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBzE,aAAa,CAACE,YAAY,CAACmC,gBAAgB,CAACqD,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAC1D1G,OAAA;kBAAiBmF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAC5DpF,OAAA;oBAAMmF,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAE,IAAIuB,IAAI,CAACF,GAAG,CAACxD,IAAI,CAAC,CAAC2D,kBAAkB,CAAC;kBAAC;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9FzF,OAAA;oBAAKmF,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CpF,OAAA;sBAAKmF,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,eAC7CpF,OAAA;wBACEmF,SAAS,EAAC,6BAA6B;wBACvC0B,KAAK,EAAE;0BAAEC,KAAK,EAAE,GAAIL,GAAG,CAACvD,KAAK,GAAG,EAAE,GAAI,GAAG;wBAAI;sBAAE;wBAAAoC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNzF,OAAA;sBAAMmF,SAAS,EAAC,2CAA2C;sBAAAC,QAAA,EAAEqB,GAAG,CAACvD;oBAAK;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA,GAVEiB,KAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNzF,OAAA;cAAKmF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DpF,OAAA;gBAAKmF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDpF,OAAA;kBAAImF,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChFzF,OAAA,CAACL,IAAI;kBAACyD,IAAI,EAAC,QAAQ;kBAACsC,IAAI,EAAE,EAAG;kBAACP,SAAS,EAAC;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eAENzF,OAAA;gBAAKmF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBzE,aAAa,CAACG,uBAAuB,CAAC+C,kBAAkB,CAACwC,GAAG,CAAC,CAACvC,MAAM,EAAE4C,KAAK,kBAC1E1G,OAAA;kBAAiBmF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAC5DpF,OAAA;oBAAMmF,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEtB,MAAM,CAACA;kBAAM;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpEzF,OAAA;oBAAKmF,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CpF,OAAA;sBAAKmF,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,eAC7CpF,OAAA;wBACEmF,SAAS,EAAC,6BAA6B;wBACvC0B,KAAK,EAAE;0BAAEC,KAAK,EAAE,GAAGhD,MAAM,CAAC1C,KAAK;wBAAI;sBAAE;wBAAAkE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNzF,OAAA;sBAAMmF,SAAS,EAAC,2CAA2C;sBAAAC,QAAA,GAAEtB,MAAM,CAAC1C,KAAK,EAAC,GAAC;oBAAA;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClFzF,OAAA,CAACL,IAAI;sBACHyD,IAAI,EAAEU,MAAM,CAACC,KAAK,KAAK,IAAI,GAAG,YAAY,GAAG,cAAe;sBAC5D2B,IAAI,EAAE,EAAG;sBACTP,SAAS,EAAErB,MAAM,CAACC,KAAK,KAAK,IAAI,GAAG,cAAc,GAAG;oBAAmB;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAfEiB,KAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgBV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACvF,EAAA,CA3WID,SAAS;AAAA8G,EAAA,GAAT9G,SAAS;AA6Wf,eAAeA,SAAS;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}