#!/usr/bin/env python3
"""
Optimized Enhanced Server for Agno WorkSphere
Implements connection pooling, caching, and performance optimizations
"""

import asyncio
import uvicorn
import time
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr

# Import our optimized components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from database.connection_pool import db_pool, init_database, close_database, fetch_all, fetch_one, fetch_value
from cache.redis_cache import cache_manager, init_cache, close_cache, cache_result

# Pydantic models for request/response
class UserRegistration(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    organization_name: Optional[str] = None
    organization_slug: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class InviteUser(BaseModel):
    email: EmailStr
    role: str = "member"

# Security
security = HTTPBearer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    print("🚀 Starting Optimized Agno WorkSphere Server...")
    await init_database()
    await init_cache()
    print("✅ Database pool and cache initialized")
    
    yield
    
    # Shutdown
    print("🔄 Shutting down server...")
    await close_database()
    await close_cache()
    print("✅ Database pool and cache closed")

# Create FastAPI app with lifespan
app = FastAPI(
    title="Agno WorkSphere API",
    description="Optimized API for Agno WorkSphere with connection pooling and caching",
    version="2.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Performance monitoring middleware
@app.middleware("http")
async def performance_middleware(request: Request, call_next):
    """Monitor API performance"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # Log slow requests
    if process_time > 1.0:  # Log requests taking more than 1 second
        print(f"⚠️ Slow request: {request.method} {request.url.path} took {process_time:.2f}s")
    
    return response

# Utility functions
def generate_token(user_id: str) -> str:
    """Generate JWT-like token (simplified for demo)"""
    import base64
    token_data = {
        "user_id": user_id,
        "exp": (datetime.utcnow() + timedelta(hours=24)).isoformat()
    }
    return base64.b64encode(json.dumps(token_data).encode()).decode()

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Verify and extract user ID from token"""
    try:
        import base64
        token_data = json.loads(base64.b64decode(credentials.credentials).decode())
        return token_data["user_id"]
    except:
        raise HTTPException(status_code=401, detail="Invalid token")

# API Endpoints

@app.get("/health")
async def health_check():
    """Optimized health check with system stats"""
    pool_stats = db_pool.get_pool_stats()
    cache_stats = await cache_manager.get_cache_stats()
    
    return {
        "status": "success",
        "data": {
            "status": "healthy",
            "version": "2.0.0",
            "environment": "development",
            "timestamp": datetime.utcnow().isoformat(),
            "database_pool": pool_stats,
            "cache": cache_stats
        }
    }

@app.post("/api/v1/auth/register")
async def register_user(user_data: UserRegistration):
    """Optimized user registration with caching"""
    try:
        # Check if user already exists (with caching)
        existing_user = await cache_manager.get_user(user_data.email)
        if not existing_user:
            existing_user = await fetch_one(
                "SELECT id, email FROM users WHERE email = $1", 
                user_data.email
            )
            if existing_user:
                await cache_manager.set_user(user_data.email, dict(existing_user), ttl=300)
        
        if existing_user:
            raise HTTPException(status_code=409, detail="User already exists")
        
        # Create user
        user_id = str(uuid.uuid4())
        org_id = str(uuid.uuid4())
        
        # Use transaction for data consistency
        async with db_pool.transaction() as conn:
            async with conn.transaction():
                # Create user
                await conn.execute("""
                    INSERT INTO users (id, email, password_hash, first_name, last_name, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
                """, user_id, user_data.email, "hashed_password", user_data.first_name, user_data.last_name)
                
                # Create organization if provided
                if user_data.organization_name:
                    org_slug = user_data.organization_slug or user_data.organization_name.lower().replace(" ", "-")
                    await conn.execute("""
                        INSERT INTO organizations (id, name, slug, allowed_domains, created_at, updated_at)
                        VALUES ($1, $2, $3, $4, NOW(), NOW())
                    """, org_id, user_data.organization_name, org_slug, ['agnoshin.com', 'agno.com'])
                    
                    # Add user as owner
                    await conn.execute("""
                        INSERT INTO organization_members (user_id, organization_id, role, created_at, updated_at)
                        VALUES ($1, $2, 'owner', NOW(), NOW())
                    """, user_id, org_id)
        
        # Generate token
        access_token = generate_token(user_id)
        
        # Cache user data
        user_cache_data = {
            "id": user_id,
            "email": user_data.email,
            "first_name": user_data.first_name,
            "last_name": user_data.last_name
        }
        await cache_manager.set_user(user_id, user_cache_data, ttl=600)
        
        return {
            "status": "success",
            "data": {
                "user": user_cache_data,
                "tokens": {
                    "access_token": access_token,
                    "token_type": "bearer"
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Registration error: {e}")
        raise HTTPException(status_code=500, detail="Registration failed")

@app.post("/api/v1/auth/login")
async def login_user(login_data: UserLogin):
    """Optimized user login with caching"""
    try:
        # Check cache first
        user = await cache_manager.get_user(login_data.email)
        if not user:
            user = await fetch_one(
                "SELECT id, email, first_name, last_name FROM users WHERE email = $1",
                login_data.email
            )
            if user:
                user_dict = dict(user)
                await cache_manager.set_user(user_dict["id"], user_dict, ttl=600)
                await cache_manager.set_user(login_data.email, user_dict, ttl=600)
        
        if not user:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Generate token
        user_id = user["id"] if isinstance(user, dict) else user.id
        access_token = generate_token(user_id)
        
        return {
            "status": "success",
            "data": {
                "user": dict(user) if not isinstance(user, dict) else user,
                "tokens": {
                    "access_token": access_token,
                    "token_type": "bearer"
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")

@app.get("/api/v1/users/profile")
async def get_user_profile(user_id: str = Depends(verify_token)):
    """Get user profile with caching"""
    try:
        # Check cache first
        user = await cache_manager.get_user(user_id)
        if not user:
            user = await fetch_one(
                "SELECT id, email, first_name, last_name, created_at FROM users WHERE id = $1",
                user_id
            )
            if user:
                user_dict = dict(user)
                await cache_manager.set_user(user_id, user_dict, ttl=600)
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get user organizations (cached)
        organizations = await cache_manager.get_user_organizations(user_id)
        if not organizations:
            organizations = await fetch_all("""
                SELECT o.id, o.name, o.slug, om.role
                FROM organizations o
                JOIN organization_members om ON o.id = om.organization_id
                WHERE om.user_id = $1
            """, user_id)
            organizations = [dict(org) for org in organizations]
            await cache_manager.set_user_organizations(user_id, organizations, ttl=300)
        
        user_data = dict(user) if not isinstance(user, dict) else user
        user_data["organizations"] = organizations
        
        return {
            "status": "success",
            "data": user_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Profile error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get profile")

@app.get("/api/v1/organizations")
async def get_organizations(user_id: str = Depends(verify_token)):
    """Get user organizations with caching"""
    try:
        organizations = await cache_manager.get_user_organizations(user_id)
        if not organizations:
            organizations = await fetch_all("""
                SELECT o.id, o.name, o.slug, o.allowed_domains, om.role
                FROM organizations o
                JOIN organization_members om ON o.id = om.organization_id
                WHERE om.user_id = $1
            """, user_id)
            organizations = [dict(org) for org in organizations]
            await cache_manager.set_user_organizations(user_id, organizations, ttl=300)
        
        return {
            "status": "success",
            "data": organizations
        }
        
    except Exception as e:
        print(f"Organizations error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get organizations")

@app.get("/api/v1/dashboard/stats")
@cache_result("dashboard_stats", ttl=180)  # Cache for 3 minutes
async def get_dashboard_stats(user_id: str = Depends(verify_token)):
    """Get dashboard statistics with caching"""
    try:
        # Get user organizations
        user_orgs = await fetch_all("""
            SELECT organization_id FROM organization_members WHERE user_id = $1
        """, user_id)
        
        if not user_orgs:
            return {
                "status": "success",
                "data": {
                    "total_projects": 0,
                    "total_boards": 0,
                    "total_cards": 0,
                    "organizations": 0
                }
            }
        
        org_ids = [org["organization_id"] for org in user_orgs]
        
        # Get statistics
        stats = {}
        
        # Count projects
        stats["total_projects"] = await fetch_value(
            "SELECT COUNT(*) FROM projects WHERE organization_id = ANY($1)",
            org_ids
        )
        
        # Count boards
        stats["total_boards"] = await fetch_value("""
            SELECT COUNT(*) FROM boards b
            JOIN projects p ON b.project_id = p.id
            WHERE p.organization_id = ANY($1)
        """, org_ids)
        
        # Count cards
        stats["total_cards"] = await fetch_value("""
            SELECT COUNT(*) FROM cards c
            JOIN board_columns bc ON c.column_id = bc.id
            JOIN boards b ON bc.board_id = b.id
            JOIN projects p ON b.project_id = p.id
            WHERE p.organization_id = ANY($1)
        """, org_ids)
        
        stats["organizations"] = len(org_ids)
        
        return {
            "status": "success",
            "data": stats
        }
        
    except Exception as e:
        print(f"Dashboard stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard stats")

@app.get("/api/v1/projects")
async def get_projects(user_id: str = Depends(verify_token)):
    """Get user projects with caching"""
    try:
        # Get user organizations
        user_orgs = await fetch_all("""
            SELECT organization_id FROM organization_members WHERE user_id = $1
        """, user_id)
        
        if not user_orgs:
            return {"status": "success", "data": []}
        
        org_ids = [org["organization_id"] for org in user_orgs]
        
        # Check cache for each organization
        all_projects = []
        for org_id in org_ids:
            projects = await cache_manager.get_projects(org_id)
            if not projects:
                projects = await fetch_all("""
                    SELECT id, name, description, organization_id, created_at
                    FROM projects WHERE organization_id = $1
                """, org_id)
                projects = [dict(project) for project in projects]
                await cache_manager.set_projects(org_id, projects, ttl=300)
            
            all_projects.extend(projects)
        
        return {
            "status": "success",
            "data": all_projects
        }
        
    except Exception as e:
        print(f"Projects error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get projects")

@app.get("/api/v1/boards")
async def get_boards(user_id: str = Depends(verify_token)):
    """Get user boards with caching"""
    try:
        # Get user projects first
        user_orgs = await fetch_all("""
            SELECT organization_id FROM organization_members WHERE user_id = $1
        """, user_id)
        
        if not user_orgs:
            return {"status": "success", "data": []}
        
        org_ids = [org["organization_id"] for org in user_orgs]
        
        # Get all boards for user's organizations
        boards = await fetch_all("""
            SELECT b.id, b.name, b.description, b.project_id, p.name as project_name
            FROM boards b
            JOIN projects p ON b.project_id = p.id
            WHERE p.organization_id = ANY($1)
        """, org_ids)
        
        return {
            "status": "success",
            "data": [dict(board) for board in boards]
        }
        
    except Exception as e:
        print(f"Boards error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get boards")

if __name__ == "__main__":
    print("🚀 Starting Optimized Agno WorkSphere Server...")
    uvicorn.run(
        "optimized_server:app",
        host="0.0.0.0",
        port=3001,
        reload=False,  # Disable reload for better performance
        workers=1,     # Single worker for development
        log_level="info"
    )
