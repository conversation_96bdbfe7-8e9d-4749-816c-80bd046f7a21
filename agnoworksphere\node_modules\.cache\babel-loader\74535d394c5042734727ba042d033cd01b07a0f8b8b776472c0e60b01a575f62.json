{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\card-details\\\\index.jsx\",\n  _s = $RefreshSig$();\n// ... imports\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport CardHeader from './components/CardHeader';\nimport CardDescription from './components/CardDescription';\nimport MemberAssignment from './components/MemberAssignment';\nimport DueDatePicker from './components/DueDatePicker';\nimport LabelManager from './components/LabelManager';\nimport ChecklistManager from './components/ChecklistManager';\nimport ActivityTimeline from './components/ActivityTimeline';\nimport authService from '../../utils/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CardDetails = () => {\n  _s();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const cardId = searchParams.get('id');\n\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());\n  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const [cardData, setCardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Load card data from location state or URL params\n  useEffect(() => {\n    const loadCardData = () => {\n      var _location$state;\n      setIsLoading(true);\n\n      // First try to get card data from location state (when navigated from kanban board)\n      if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.card) {\n        console.log('Loading card from location state:', location.state.card);\n        setCardData(location.state.card);\n        setIsLoading(false);\n        return;\n      }\n\n      // If no state data, try to find card in localStorage (for page refresh)\n      const savedCards = localStorage.getItem('kanban-cards');\n      if (savedCards && cardId) {\n        try {\n          const cards = JSON.parse(savedCards);\n          const foundCard = cards.find(card => card.id === cardId);\n          if (foundCard) {\n            console.log('Loading card from localStorage:', foundCard);\n            setCardData(foundCard);\n            setIsLoading(false);\n            return;\n          }\n        } catch (error) {\n          console.error('Error parsing saved cards:', error);\n        }\n      }\n\n      // Fallback: use mock data if no card found\n      console.log('No card data found, using fallback data');\n      setCardData({\n        id: cardId || '1',\n        title: 'Card Not Found',\n        description: 'This card could not be loaded. Please return to the board and try again.',\n        columnTitle: 'Unknown',\n        assignedMembers: [],\n        dueDate: null,\n        labels: [],\n        checklist: [],\n        completed: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      });\n      setIsLoading(false);\n    };\n    loadCardData();\n  }, [cardId, location.state]);\n  const handleTitleChange = newTitle => {\n    setCardData(prev => ({\n      ...prev,\n      title: newTitle,\n      updatedAt: new Date().toISOString()\n    }));\n    // Save updated card data to localStorage\n    updateCardInStorage(prev => ({\n      ...prev,\n      title: newTitle,\n      updatedAt: new Date().toISOString()\n    }));\n  };\n  const handleDescriptionChange = newDescription => {\n    setCardData(prev => ({\n      ...prev,\n      description: newDescription,\n      updatedAt: new Date().toISOString()\n    }));\n    // Save updated card data to localStorage\n    updateCardInStorage(prev => ({\n      ...prev,\n      description: newDescription,\n      updatedAt: new Date().toISOString()\n    }));\n  };\n\n  // Helper function to update card in localStorage\n  const updateCardInStorage = updatedCard => {\n    try {\n      const savedCards = localStorage.getItem('kanban-cards');\n      if (savedCards) {\n        const cards = JSON.parse(savedCards);\n        const cardIndex = cards.findIndex(card => card.id === cardData.id);\n        if (cardIndex !== -1) {\n          cards[cardIndex] = typeof updatedCard === 'function' ? updatedCard(cards[cardIndex]) : updatedCard;\n          localStorage.setItem('kanban-cards', JSON.stringify(cards));\n        }\n      }\n    } catch (error) {\n      console.error('Error updating card in storage:', error);\n    }\n  };\n  const handleMembersChange = newMembers => {\n    const updatedCard = prev => ({\n      ...prev,\n      assignedMembers: newMembers,\n      updatedAt: new Date().toISOString()\n    });\n    setCardData(updatedCard);\n    updateCardInStorage(updatedCard);\n  };\n  const handleDueDateChange = newDueDate => {\n    const updatedCard = prev => ({\n      ...prev,\n      dueDate: newDueDate,\n      updatedAt: new Date().toISOString()\n    });\n    setCardData(updatedCard);\n    updateCardInStorage(updatedCard);\n  };\n  const handleLabelsChange = newLabels => {\n    const updatedCard = prev => ({\n      ...prev,\n      labels: newLabels,\n      updatedAt: new Date().toISOString()\n    });\n    setCardData(updatedCard);\n    updateCardInStorage(updatedCard);\n  };\n  const handleChecklistChange = newChecklist => {\n    const updatedCard = prev => ({\n      ...prev,\n      checklist: newChecklist,\n      updatedAt: new Date().toISOString()\n    });\n    setCardData(updatedCard);\n    updateCardInStorage(updatedCard);\n  };\n  const handleAddComment = comment => {\n    console.log('New comment added:', comment);\n  };\n  const handleClose = () => navigate('/kanban-board');\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this card?')) {\n      console.log('Card deleted:', cardData.id);\n      navigate('/kanban-board');\n    }\n  };\n\n  // Load user authentication data\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResponse = await authService.getCurrentUser();\n        const orgResponse = await authService.getCurrentOrganization();\n        if (userResponse.data && userResponse.data.user) {\n          setCurrentUser(userResponse.data.user);\n          setUserRole(userResponse.data.user.role || 'member');\n        }\n        if (orgResponse.data && orgResponse.data.organization) {\n          setCurrentOrganization(orgResponse.data.organization);\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Set default values if auth fails\n        setUserRole('member');\n      }\n    };\n    loadUserData();\n  }, []);\n  useEffect(() => {\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  // Show loading state\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        },\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-text-primary mb-2\",\n            children: \"Loading card...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary\",\n            children: \"Please wait while we load the card details.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show error state if card not found\n  if (!cardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        },\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-text-primary mb-2\",\n            children: \"Card not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary mb-4\",\n            children: \"The requested card could not be found.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"text-primary hover:underline\",\n            children: \"Return to Board\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName} ${currentUser.lastName}`,\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 z-1000 pt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-center min-h-full p-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full max-w-4xl bg-surface rounded-lg shadow-focused my-8 max-h-screen overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            card: cardData,\n            onTitleChange: handleTitleChange,\n            onClose: handleClose,\n            onDelete: handleDelete,\n            canEdit: canEdit,\n            canDelete: canDelete\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 lg:w-3/5 p-6 space-y-8\",\n              children: [/*#__PURE__*/_jsxDEV(CardDescription, {\n                card: cardData,\n                onDescriptionChange: handleDescriptionChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ChecklistManager, {\n                card: cardData,\n                onChecklistChange: handleChecklistChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ActivityTimeline, {\n                card: cardData,\n                onAddComment: handleAddComment,\n                canComment: canComment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:w-2/5 p-6 bg-muted/30 border-l border-border space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(MemberAssignment, {\n                card: cardData,\n                onMembersChange: handleMembersChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DueDatePicker, {\n                card: cardData,\n                onDueDateChange: handleDueDateChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(LabelManager, {\n                card: cardData,\n                onLabelsChange: handleLabelsChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4 pt-4 border-t border-border\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-text-primary\",\n                  children: \"Card Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary\",\n                      children: \"Created:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary\",\n                      children: new Date(cardData.createdAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary\",\n                      children: \"Last updated:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary\",\n                      children: new Date(cardData.updatedAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary\",\n                      children: \"Card ID:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary font-mono text-xs\",\n                      children: [\"#\", cardData.id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), (canEdit || canDelete) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 pt-4 border-t border-border\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-text-primary text-sm\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\",\n                    children: \"Archive Card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 35\n                  }, this), canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\",\n                    children: \"Copy Card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 35\n                  }, this), canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\",\n                    children: \"Move Card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 35\n                  }, this), canDelete && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleDelete,\n                    className: \"w-full text-left px-3 py-2 text-sm text-destructive hover:bg-destructive/10 rounded-md\",\n                    children: \"Delete Card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s(CardDetails, \"HwJFMPq6UcvETLgExBp5TXwkD0c=\", false, function () {\n  return [useNavigate, useSearchParams];\n});\n_c = CardDetails;\nexport default CardDetails;\nvar _c;\n$RefreshReg$(_c, \"CardDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSearchParams", "useLocation", "RoleBasedHeader", "<PERSON><PERSON><PERSON><PERSON>", "CardDescription", "MemberAssignment", "DueDatePicker", "LabelManager", "ChecklistManager", "ActivityTimeline", "authService", "jsxDEV", "_jsxDEV", "CardDetails", "_s", "navigate", "searchParams", "cardId", "get", "currentUser", "setCurrentUser", "userRole", "setUserRole", "currentOrganization", "setCurrentOrganization", "canEdit", "includes", "toLowerCase", "canDelete", "canComment", "cardData", "setCardData", "isLoading", "setIsLoading", "loadCardData", "_location$state", "location", "state", "card", "console", "log", "savedCards", "localStorage", "getItem", "cards", "JSON", "parse", "foundCard", "find", "id", "error", "title", "description", "columnTitle", "assignedMembers", "dueDate", "labels", "checklist", "completed", "createdAt", "Date", "toISOString", "updatedAt", "handleTitleChange", "newTitle", "prev", "updateCardInStorage", "handleDescriptionChange", "newDescription", "updatedCard", "cardIndex", "findIndex", "setItem", "stringify", "handleMembersChange", "newMembers", "handleDueDateChange", "newDueDate", "handleLabelsChange", "<PERSON><PERSON><PERSON><PERSON>", "handleChecklistChange", "newChecklist", "handleAddComment", "comment", "handleClose", "handleDelete", "window", "confirm", "loadUserData", "userResponse", "getCurrentUser", "orgResponse", "getCurrentOrganization", "data", "user", "role", "organization", "document", "body", "style", "overflow", "className", "children", "name", "firstName", "lastName", "email", "avatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onTitleChange", "onClose", "onDelete", "onDescriptionChange", "onChecklistChange", "onAddComment", "onMembersChange", "onDueDateChange", "onLabelsChange", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/card-details/index.jsx"], "sourcesContent": ["// ... imports\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport CardHeader from './components/CardHeader';\nimport CardDescription from './components/CardDescription';\nimport MemberAssignment from './components/MemberAssignment';\nimport DueDatePicker from './components/DueDatePicker';\nimport LabelManager from './components/LabelManager';\nimport ChecklistManager from './components/ChecklistManager';\nimport ActivityTimeline from './components/ActivityTimeline';\nimport authService from '../../utils/authService';\n\nconst CardDetails = () => {\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const cardId = searchParams.get('id');\n\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n\n  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());\n  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n\n  const [cardData, setCardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Load card data from location state or URL params\n  useEffect(() => {\n    const loadCardData = () => {\n      setIsLoading(true);\n\n      // First try to get card data from location state (when navigated from kanban board)\n      if (location.state?.card) {\n        console.log('Loading card from location state:', location.state.card);\n        setCardData(location.state.card);\n        setIsLoading(false);\n        return;\n      }\n\n      // If no state data, try to find card in localStorage (for page refresh)\n      const savedCards = localStorage.getItem('kanban-cards');\n      if (savedCards && cardId) {\n        try {\n          const cards = JSON.parse(savedCards);\n          const foundCard = cards.find(card => card.id === cardId);\n          if (foundCard) {\n            console.log('Loading card from localStorage:', foundCard);\n            setCardData(foundCard);\n            setIsLoading(false);\n            return;\n          }\n        } catch (error) {\n          console.error('Error parsing saved cards:', error);\n        }\n      }\n\n      // Fallback: use mock data if no card found\n      console.log('No card data found, using fallback data');\n      setCardData({\n        id: cardId || '1',\n        title: 'Card Not Found',\n        description: 'This card could not be loaded. Please return to the board and try again.',\n        columnTitle: 'Unknown',\n        assignedMembers: [],\n        dueDate: null,\n        labels: [],\n        checklist: [],\n        completed: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      });\n      setIsLoading(false);\n    };\n\n    loadCardData();\n  }, [cardId, location.state]);\n\n  const handleTitleChange = (newTitle) => {\n    setCardData(prev => ({ ...prev, title: newTitle, updatedAt: new Date().toISOString() }));\n    // Save updated card data to localStorage\n    updateCardInStorage(prev => ({ ...prev, title: newTitle, updatedAt: new Date().toISOString() }));\n  };\n\n  const handleDescriptionChange = (newDescription) => {\n    setCardData(prev => ({ ...prev, description: newDescription, updatedAt: new Date().toISOString() }));\n    // Save updated card data to localStorage\n    updateCardInStorage(prev => ({ ...prev, description: newDescription, updatedAt: new Date().toISOString() }));\n  };\n\n  // Helper function to update card in localStorage\n  const updateCardInStorage = (updatedCard) => {\n    try {\n      const savedCards = localStorage.getItem('kanban-cards');\n      if (savedCards) {\n        const cards = JSON.parse(savedCards);\n        const cardIndex = cards.findIndex(card => card.id === cardData.id);\n        if (cardIndex !== -1) {\n          cards[cardIndex] = typeof updatedCard === 'function' ? updatedCard(cards[cardIndex]) : updatedCard;\n          localStorage.setItem('kanban-cards', JSON.stringify(cards));\n        }\n      }\n    } catch (error) {\n      console.error('Error updating card in storage:', error);\n    }\n  };\n\n  const handleMembersChange = (newMembers) => {\n    const updatedCard = prev => ({ ...prev, assignedMembers: newMembers, updatedAt: new Date().toISOString() });\n    setCardData(updatedCard);\n    updateCardInStorage(updatedCard);\n  };\n\n  const handleDueDateChange = (newDueDate) => {\n    const updatedCard = prev => ({ ...prev, dueDate: newDueDate, updatedAt: new Date().toISOString() });\n    setCardData(updatedCard);\n    updateCardInStorage(updatedCard);\n  };\n\n  const handleLabelsChange = (newLabels) => {\n    const updatedCard = prev => ({ ...prev, labels: newLabels, updatedAt: new Date().toISOString() });\n    setCardData(updatedCard);\n    updateCardInStorage(updatedCard);\n  };\n\n  const handleChecklistChange = (newChecklist) => {\n    const updatedCard = prev => ({ ...prev, checklist: newChecklist, updatedAt: new Date().toISOString() });\n    setCardData(updatedCard);\n    updateCardInStorage(updatedCard);\n  };\n\n  const handleAddComment = (comment) => {\n    console.log('New comment added:', comment);\n  };\n\n  const handleClose = () => navigate('/kanban-board');\n\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this card?')) {\n      console.log('Card deleted:', cardData.id);\n      navigate('/kanban-board');\n    }\n  };\n\n  // Load user authentication data\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResponse = await authService.getCurrentUser();\n        const orgResponse = await authService.getCurrentOrganization();\n\n        if (userResponse.data && userResponse.data.user) {\n          setCurrentUser(userResponse.data.user);\n          setUserRole(userResponse.data.user.role || 'member');\n        }\n\n        if (orgResponse.data && orgResponse.data.organization) {\n          setCurrentOrganization(orgResponse.data.organization);\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Set default values if auth fails\n        setUserRole('member');\n      }\n    };\n\n    loadUserData();\n  }, []);\n\n  useEffect(() => {\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  // Show loading state\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n          currentOrganization={currentOrganization}\n        />\n        <div className=\"pt-16 flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n            <div className=\"text-lg font-medium text-text-primary mb-2\">Loading card...</div>\n            <div className=\"text-text-secondary\">Please wait while we load the card details.</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state if card not found\n  if (!cardData) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n          currentOrganization={currentOrganization}\n        />\n        <div className=\"pt-16 flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"text-lg font-medium text-text-primary mb-2\">Card not found</div>\n            <div className=\"text-text-secondary mb-4\">The requested card could not be found.</div>\n            <button onClick={handleClose} className=\"text-primary hover:underline\">\n              Return to Board\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Modal Overlay */}\n      <div className=\"fixed inset-0 bg-black/50 z-1000 pt-16\">\n        <div className=\"flex items-start justify-center min-h-full p-4 overflow-y-auto\">\n          {/* Modal Content */}\n          <div className=\"w-full max-w-4xl bg-surface rounded-lg shadow-focused my-8 max-h-screen overflow-y-auto\">\n            {/* Card Header */}\n            <CardHeader\n              card={cardData}\n              onTitleChange={handleTitleChange}\n              onClose={handleClose}\n              onDelete={handleDelete}\n              canEdit={canEdit}\n              canDelete={canDelete}\n            />\n\n            {/* Main Content */}\n            <div className=\"flex flex-col lg:flex-row\">\n              <div className=\"flex-1 lg:w-3/5 p-6 space-y-8\">\n                <CardDescription card={cardData} onDescriptionChange={handleDescriptionChange} canEdit={canEdit} />\n                <ChecklistManager card={cardData} onChecklistChange={handleChecklistChange} canEdit={canEdit} />\n                <ActivityTimeline card={cardData} onAddComment={handleAddComment} canComment={canComment} />\n              </div>\n\n              <div className=\"lg:w-2/5 p-6 bg-muted/30 border-l border-border space-y-6\">\n                <MemberAssignment card={cardData} onMembersChange={handleMembersChange} canEdit={canEdit} />\n                <DueDatePicker card={cardData} onDueDateChange={handleDueDateChange} canEdit={canEdit} />\n                <LabelManager card={cardData} onLabelsChange={handleLabelsChange} canEdit={canEdit} />\n\n                <div className=\"space-y-4 pt-4 border-t border-border\">\n                  <h4 className=\"font-medium text-text-primary\">Card Information</h4>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Created:</span>\n                      <span className=\"text-text-primary\">{new Date(cardData.createdAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Last updated:</span>\n                      <span className=\"text-text-primary\">{new Date(cardData.updatedAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Card ID:</span>\n                      <span className=\"text-text-primary font-mono text-xs\">#{cardData.id}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {(canEdit || canDelete) && (\n                  <div className=\"space-y-2 pt-4 border-t border-border\">\n                    <h4 className=\"font-medium text-text-primary text-sm\">Actions</h4>\n                    <div className=\"space-y-2\">\n                      {canEdit && <button className=\"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\">Archive Card</button>}\n                      {canEdit && <button className=\"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\">Copy Card</button>}\n                      {canEdit && <button className=\"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\">Move Card</button>}\n                      {canDelete && <button onClick={handleDelete} className=\"w-full text-left px-3 py-2 text-sm text-destructive hover:bg-destructive/10 rounded-md\">Delete Card</button>}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CardDetails;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC5E,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,YAAY,CAAC,GAAGhB,eAAe,CAAC,CAAC;EACxC,MAAMiB,MAAM,GAAGD,YAAY,CAACE,GAAG,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC0B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAEpE,MAAM4B,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EAC7E,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAACF,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EACrE,MAAME,UAAU,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACH,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EAEhF,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoC,YAAY,GAAGA,CAAA,KAAM;MAAA,IAAAC,eAAA;MACzBF,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,KAAAE,eAAA,GAAIC,QAAQ,CAACC,KAAK,cAAAF,eAAA,eAAdA,eAAA,CAAgBG,IAAI,EAAE;QACxBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEJ,QAAQ,CAACC,KAAK,CAACC,IAAI,CAAC;QACrEP,WAAW,CAACK,QAAQ,CAACC,KAAK,CAACC,IAAI,CAAC;QAChCL,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;;MAEA;MACA,MAAMQ,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACvD,IAAIF,UAAU,IAAIxB,MAAM,EAAE;QACxB,IAAI;UACF,MAAM2B,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;UACpC,MAAMM,SAAS,GAAGH,KAAK,CAACI,IAAI,CAACV,IAAI,IAAIA,IAAI,CAACW,EAAE,KAAKhC,MAAM,CAAC;UACxD,IAAI8B,SAAS,EAAE;YACbR,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEO,SAAS,CAAC;YACzDhB,WAAW,CAACgB,SAAS,CAAC;YACtBd,YAAY,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF;;MAEA;MACAX,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDT,WAAW,CAAC;QACVkB,EAAE,EAAEhC,MAAM,IAAI,GAAG;QACjBkC,KAAK,EAAE,gBAAgB;QACvBC,WAAW,EAAE,0EAA0E;QACvFC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,EAAE;QACnBC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC;MACF5B,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACjB,MAAM,EAAEmB,QAAQ,CAACC,KAAK,CAAC,CAAC;EAE5B,MAAM0B,iBAAiB,GAAIC,QAAQ,IAAK;IACtCjC,WAAW,CAACkC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEd,KAAK,EAAEa,QAAQ;MAAEF,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;IACxF;IACAK,mBAAmB,CAACD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEd,KAAK,EAAEa,QAAQ;MAAEF,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EAClG,CAAC;EAED,MAAMM,uBAAuB,GAAIC,cAAc,IAAK;IAClDrC,WAAW,CAACkC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEb,WAAW,EAAEgB,cAAc;MAAEN,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;IACpG;IACAK,mBAAmB,CAACD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEb,WAAW,EAAEgB,cAAc;MAAEN,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EAC9G,CAAC;;EAED;EACA,MAAMK,mBAAmB,GAAIG,WAAW,IAAK;IAC3C,IAAI;MACF,MAAM5B,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACvD,IAAIF,UAAU,EAAE;QACd,MAAMG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACpC,MAAM6B,SAAS,GAAG1B,KAAK,CAAC2B,SAAS,CAACjC,IAAI,IAAIA,IAAI,CAACW,EAAE,KAAKnB,QAAQ,CAACmB,EAAE,CAAC;QAClE,IAAIqB,SAAS,KAAK,CAAC,CAAC,EAAE;UACpB1B,KAAK,CAAC0B,SAAS,CAAC,GAAG,OAAOD,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACzB,KAAK,CAAC0B,SAAS,CAAC,CAAC,GAAGD,WAAW;UAClG3B,YAAY,CAAC8B,OAAO,CAAC,cAAc,EAAE3B,IAAI,CAAC4B,SAAS,CAAC7B,KAAK,CAAC,CAAC;QAC7D;MACF;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMwB,mBAAmB,GAAIC,UAAU,IAAK;IAC1C,MAAMN,WAAW,GAAGJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEX,eAAe,EAAEqB,UAAU;MAAEb,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;IAC3G9B,WAAW,CAACsC,WAAW,CAAC;IACxBH,mBAAmB,CAACG,WAAW,CAAC;EAClC,CAAC;EAED,MAAMO,mBAAmB,GAAIC,UAAU,IAAK;IAC1C,MAAMR,WAAW,GAAGJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEV,OAAO,EAAEsB,UAAU;MAAEf,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;IACnG9B,WAAW,CAACsC,WAAW,CAAC;IACxBH,mBAAmB,CAACG,WAAW,CAAC;EAClC,CAAC;EAED,MAAMS,kBAAkB,GAAIC,SAAS,IAAK;IACxC,MAAMV,WAAW,GAAGJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET,MAAM,EAAEuB,SAAS;MAAEjB,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;IACjG9B,WAAW,CAACsC,WAAW,CAAC;IACxBH,mBAAmB,CAACG,WAAW,CAAC;EAClC,CAAC;EAED,MAAMW,qBAAqB,GAAIC,YAAY,IAAK;IAC9C,MAAMZ,WAAW,GAAGJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,SAAS,EAAEwB,YAAY;MAAEnB,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;IACvG9B,WAAW,CAACsC,WAAW,CAAC;IACxBH,mBAAmB,CAACG,WAAW,CAAC;EAClC,CAAC;EAED,MAAMa,gBAAgB,GAAIC,OAAO,IAAK;IACpC5C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2C,OAAO,CAAC;EAC5C,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAMrE,QAAQ,CAAC,eAAe,CAAC;EAEnD,MAAMsE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChEhD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEV,QAAQ,CAACmB,EAAE,CAAC;MACzClC,QAAQ,CAAC,eAAe,CAAC;IAC3B;EACF,CAAC;;EAED;EACAjB,SAAS,CAAC,MAAM;IACd,MAAM0F,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,YAAY,GAAG,MAAM/E,WAAW,CAACgF,cAAc,CAAC,CAAC;QACvD,MAAMC,WAAW,GAAG,MAAMjF,WAAW,CAACkF,sBAAsB,CAAC,CAAC;QAE9D,IAAIH,YAAY,CAACI,IAAI,IAAIJ,YAAY,CAACI,IAAI,CAACC,IAAI,EAAE;UAC/C1E,cAAc,CAACqE,YAAY,CAACI,IAAI,CAACC,IAAI,CAAC;UACtCxE,WAAW,CAACmE,YAAY,CAACI,IAAI,CAACC,IAAI,CAACC,IAAI,IAAI,QAAQ,CAAC;QACtD;QAEA,IAAIJ,WAAW,CAACE,IAAI,IAAIF,WAAW,CAACE,IAAI,CAACG,YAAY,EAAE;UACrDxE,sBAAsB,CAACmE,WAAW,CAACE,IAAI,CAACG,YAAY,CAAC;QACvD;MACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACA5B,WAAW,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC;IAEDkE,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN1F,SAAS,CAAC,MAAM;IACdmG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvC,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIpE,SAAS,EAAE;IACb,oBACEpB,OAAA;MAAKyF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC1F,OAAA,CAACV,eAAe;QACdmB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;QACjCR,WAAW,EAAEA,WAAW,GAAG;UACzBoF,IAAI,EAAE,GAAGpF,WAAW,CAACqF,SAAS,IAAIrF,WAAW,CAACsF,QAAQ,EAAE;UACxDC,KAAK,EAAEvF,WAAW,CAACuF,KAAK;UACxBC,MAAM,EAAExF,WAAW,CAACwF,MAAM,IAAI,2BAA2B;UACzDZ,IAAI,EAAE1E;QACR,CAAC,GAAG;UACFkF,IAAI,EAAE,YAAY;UAClBG,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnCZ,IAAI,EAAE1E;QACR,CAAE;QACFE,mBAAmB,EAAEA;MAAoB;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACFnG,OAAA;QAAKyF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClE1F,OAAA;UAAKyF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1F,OAAA;YAAKyF,SAAS,EAAC;UAA0E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChGnG,OAAA;YAAKyF,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjFnG,OAAA;YAAKyF,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAA2C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACjF,QAAQ,EAAE;IACb,oBACElB,OAAA;MAAKyF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC1F,OAAA,CAACV,eAAe;QACdmB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;QACjCR,WAAW,EAAEA,WAAW,GAAG;UACzBoF,IAAI,EAAE,GAAGpF,WAAW,CAACqF,SAAS,IAAIrF,WAAW,CAACsF,QAAQ,EAAE;UACxDC,KAAK,EAAEvF,WAAW,CAACuF,KAAK;UACxBC,MAAM,EAAExF,WAAW,CAACwF,MAAM,IAAI,2BAA2B;UACzDZ,IAAI,EAAE1E;QACR,CAAC,GAAG;UACFkF,IAAI,EAAE,YAAY;UAClBG,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnCZ,IAAI,EAAE1E;QACR,CAAE;QACFE,mBAAmB,EAAEA;MAAoB;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACFnG,OAAA;QAAKyF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClE1F,OAAA;UAAKyF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1F,OAAA;YAAKyF,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChFnG,OAAA;YAAKyF,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAsC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtFnG,OAAA;YAAQoG,OAAO,EAAE5B,WAAY;YAACiB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAEvE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnG,OAAA;IAAKyF,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzC1F,OAAA,CAACV,eAAe;MACdmB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;MACjCR,WAAW,EAAEA,WAAW,GAAG;QACzBoF,IAAI,EAAE,GAAGpF,WAAW,CAACqF,SAAS,IAAIrF,WAAW,CAACsF,QAAQ,EAAE;QACxDC,KAAK,EAAEvF,WAAW,CAACuF,KAAK;QACxBC,MAAM,EAAExF,WAAW,CAACwF,MAAM,IAAI,2BAA2B;QACzDZ,IAAI,EAAE1E;MACR,CAAC,GAAG;QACFkF,IAAI,EAAE,YAAY;QAClBG,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,2BAA2B;QACnCZ,IAAI,EAAE1E;MACR,CAAE;MACFE,mBAAmB,EAAEA;IAAoB;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFnG,OAAA;MAAKyF,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrD1F,OAAA;QAAKyF,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAE7E1F,OAAA;UAAKyF,SAAS,EAAC,yFAAyF;UAAAC,QAAA,gBAEtG1F,OAAA,CAACT,UAAU;YACTmC,IAAI,EAAER,QAAS;YACfmF,aAAa,EAAElD,iBAAkB;YACjCmD,OAAO,EAAE9B,WAAY;YACrB+B,QAAQ,EAAE9B,YAAa;YACvB5D,OAAO,EAAEA,OAAQ;YACjBG,SAAS,EAAEA;UAAU;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAGFnG,OAAA;YAAKyF,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxC1F,OAAA;cAAKyF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5C1F,OAAA,CAACR,eAAe;gBAACkC,IAAI,EAAER,QAAS;gBAACsF,mBAAmB,EAAEjD,uBAAwB;gBAAC1C,OAAO,EAAEA;cAAQ;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnGnG,OAAA,CAACJ,gBAAgB;gBAAC8B,IAAI,EAAER,QAAS;gBAACuF,iBAAiB,EAAErC,qBAAsB;gBAACvD,OAAO,EAAEA;cAAQ;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChGnG,OAAA,CAACH,gBAAgB;gBAAC6B,IAAI,EAAER,QAAS;gBAACwF,YAAY,EAAEpC,gBAAiB;gBAACrD,UAAU,EAAEA;cAAW;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eAENnG,OAAA;cAAKyF,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxE1F,OAAA,CAACP,gBAAgB;gBAACiC,IAAI,EAAER,QAAS;gBAACyF,eAAe,EAAE7C,mBAAoB;gBAACjD,OAAO,EAAEA;cAAQ;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5FnG,OAAA,CAACN,aAAa;gBAACgC,IAAI,EAAER,QAAS;gBAAC0F,eAAe,EAAE5C,mBAAoB;gBAACnD,OAAO,EAAEA;cAAQ;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzFnG,OAAA,CAACL,YAAY;gBAAC+B,IAAI,EAAER,QAAS;gBAAC2F,cAAc,EAAE3C,kBAAmB;gBAACrD,OAAO,EAAEA;cAAQ;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEtFnG,OAAA;gBAAKyF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD1F,OAAA;kBAAIyF,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEnG,OAAA;kBAAKyF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1F,OAAA;oBAAKyF,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnC1F,OAAA;sBAAMyF,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrDnG,OAAA;sBAAMyF,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAE,IAAI1C,IAAI,CAAC9B,QAAQ,CAAC6B,SAAS,CAAC,CAAC+D,kBAAkB,CAAC;oBAAC;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC,eACNnG,OAAA;oBAAKyF,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnC1F,OAAA;sBAAMyF,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1DnG,OAAA;sBAAMyF,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAE,IAAI1C,IAAI,CAAC9B,QAAQ,CAACgC,SAAS,CAAC,CAAC4D,kBAAkB,CAAC;oBAAC;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC,eACNnG,OAAA;oBAAKyF,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnC1F,OAAA;sBAAMyF,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrDnG,OAAA;sBAAMyF,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,GAAC,GAAC,EAACxE,QAAQ,CAACmB,EAAE;oBAAA;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL,CAACtF,OAAO,IAAIG,SAAS,kBACpBhB,OAAA;gBAAKyF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD1F,OAAA;kBAAIyF,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEnG,OAAA;kBAAKyF,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACvB7E,OAAO,iBAAIb,OAAA;oBAAQyF,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACjHtF,OAAO,iBAAIb,OAAA;oBAAQyF,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC9GtF,OAAO,iBAAIb,OAAA;oBAAQyF,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC9GnF,SAAS,iBAAIhB,OAAA;oBAAQoG,OAAO,EAAE3B,YAAa;oBAACgB,SAAS,EAAC,wFAAwF;oBAAAC,QAAA,EAAC;kBAAW;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjG,EAAA,CAtTID,WAAW;EAAA,QACEd,WAAW,EACLC,eAAe;AAAA;AAAA2H,EAAA,GAFlC9G,WAAW;AAwTjB,eAAeA,WAAW;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}