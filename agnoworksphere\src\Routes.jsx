import React from "react";
import { Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";

// Page imports
import Login from "./pages/login";
import Register from "./pages/register";
import KanbanBoard from "./pages/kanban-board";
import CardDetails from "./pages/card-details";
import TeamMembers from "./pages/team-members";
import OrganizationSettings from "./pages/organization-settings";
import OrganizationDashboard from "./pages/organization-dashboard";
import UserProfileSettings from "./pages/user-profile-settings";
import ProjectManagement from "./pages/project-management";
import ProjectOverview from "./pages/project-overview";
import RoleBasedDashboard from "./pages/role-based-dashboard";
import Analytics from "./pages/analytics";
import Billing from "./pages/billing";
import NotFound from "./pages/NotFound";

const Routes = () => {
  return (
    <>
      <ScrollToTop />
      <RouterRoutes>
        <Route path="/" element={<OrganizationDashboard />} />
        <Route path="/dashboard" element={<OrganizationDashboard />} />
        <Route path="/organization-dashboard" element={<OrganizationDashboard />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/kanban-board" element={<KanbanBoard />} />
        <Route path="/card-details" element={<CardDetails />} />
        <Route path="/team-members" element={<TeamMembers />} />
        <Route path="/organization-settings" element={<OrganizationSettings />} />
        <Route path="/user-profile-settings" element={<UserProfileSettings />} />
        <Route path="/project-management" element={<ProjectManagement />} />
        <Route path="/project-overview" element={<ProjectOverview />} />
        <Route path="/project-overview/:projectId" element={<ProjectOverview />} />
        <Route path="/role-based-dashboard" element={<RoleBasedDashboard />} />
        <Route path="/analytics" element={<Analytics />} />
        <Route path="/billing" element={<Billing />} />
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
    </>
  );
};

export default Routes;
