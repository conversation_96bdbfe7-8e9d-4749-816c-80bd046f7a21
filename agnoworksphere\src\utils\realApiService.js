// src/utils/realApiService.js
// Real API service that connects to the backend

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';

// Helper function to get headers with authentication
const getAuthHeaders = (organizationId = null) => {
  const token = localStorage.getItem('accessToken');
  const headers = {
    'Content-Type': 'application/json',
    ...(organizationId && { 'X-Organization-ID': organizationId })
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error?.message || result.message || 'API request failed');
  }

  return result;
};

const realApiService = {
  // Authentication
  auth: {
    // Register new user
    register: async (userData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: userData.email,
            password: userData.password,
            first_name: userData.firstName || userData.first_name || '',
            last_name: userData.lastName !== undefined ? userData.lastName : (userData.last_name || ''),
            organization_name: userData.organizationName || userData.organization_name || '',
            organization_slug: userData.organizationSlug || userData.organization_slug || ''
          }),
        });

        const result = await handleResponse(response);
        
        // Store tokens and user info
        if (result.data && result.data.tokens) {
          localStorage.setItem('accessToken', result.data.tokens.access_token);
          localStorage.setItem('currentUser', JSON.stringify(result.data.user));
          
          if (result.data.user.organizations && result.data.user.organizations.length > 0) {
            localStorage.setItem('organizationId', result.data.user.organizations[0].id);
            localStorage.setItem('userRole', result.data.user.organizations[0].role);
          }
        }

        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Registration error:', error);
        return {
          data: null,
          error: error.message || 'Registration failed'
        };
      }
    },

    // Login user
    login: async (email, password) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            password
          }),
        });

        const result = await handleResponse(response);
        
        // Store tokens and user info
        if (result.data && result.data.tokens) {
          localStorage.setItem('accessToken', result.data.tokens.access_token);
          localStorage.setItem('currentUser', JSON.stringify(result.data.user));
          
          if (result.data.user.organizations && result.data.user.organizations.length > 0) {
            localStorage.setItem('organizationId', result.data.user.organizations[0].id);
            localStorage.setItem('userRole', result.data.user.organizations[0].role);
          }
        }

        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Login error:', error);
        return {
          data: null,
          error: error.message || 'Login failed'
        };
      }
    },

    // Get current user profile
    getCurrentUser: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/users/profile`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);

        return {
          data: {
            user: result.data
          },
          error: null
        };
      } catch (error) {
        console.error('Get current user error:', error);
        return {
          data: { user: null },
          error: error.message || 'Failed to get user profile'
        };
      }
    },

    // Update user profile
    updateProfile: async (profileData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/users/profile`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(profileData),
        });

        const result = await handleResponse(response);

        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Update profile error:', error);
        return {
          data: null,
          error: error.message || 'Failed to update profile'
        };
      }
    },

    // Logout
    logout: async () => {
      try {
        // Clear stored tokens and user data
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('userRole');
        localStorage.removeItem('organizationId');
        localStorage.removeItem('currentUser');

        return {
          error: null
        };
      } catch (error) {
        return {
          error: error.message || 'Logout failed'
        };
      }
    },

    // Check if user is authenticated
    isAuthenticated: () => {
      const token = localStorage.getItem('accessToken');
      return !!token;
    },

    // Get stored access token
    getAccessToken: () => {
      return localStorage.getItem('accessToken');
    },

    // Get user role
    getUserRole: () => {
      return localStorage.getItem('userRole') || 'member';
    },

    // Get organization ID
    getOrganizationId: () => {
      return localStorage.getItem('organizationId');
    }
  },

  // Organizations
  organizations: {
    // Get all organizations
    getAll: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/organizations`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get organizations error:', error);
        throw error;
      }
    },

    // Get organization by ID
    getById: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${id}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get organization error:', error);
        throw error;
      }
    }
  },

  // Projects
  projects: {
    // Get all projects
    getAll: async (organizationId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {
          method: 'GET',
          headers: getAuthHeaders(organizationId),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get projects error:', error);
        throw error;
      }
    },

    // Get project by ID
    getById: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get project error:', error);
        throw error;
      }
    },

    // Create project
    create: async (organizationId, projectData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {
          method: 'POST',
          headers: getAuthHeaders(organizationId),
          body: JSON.stringify(projectData),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Create project error:', error);
        throw error;
      }
    },

    // Update project
    update: async (id, projectData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(projectData),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Update project error:', error);
        throw error;
      }
    },

    // Delete project
    delete: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { success: true, data: result.data };
      } catch (error) {
        console.error('Delete project error:', error);
        throw error;
      }
    }
  },

  // Boards
  boards: {
    // Get boards by project
    getByProject: async (projectId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/boards?project_id=${projectId}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get boards error:', error);
        throw error;
      }
    }
  },

  // Dashboard
  dashboard: {
    // Get dashboard stats
    getStats: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/stats`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Get dashboard stats error:', error);
        return {
          data: null,
          error: error.message || 'Failed to get dashboard stats'
        };
      }
    }
  }
};

export default realApiService;
