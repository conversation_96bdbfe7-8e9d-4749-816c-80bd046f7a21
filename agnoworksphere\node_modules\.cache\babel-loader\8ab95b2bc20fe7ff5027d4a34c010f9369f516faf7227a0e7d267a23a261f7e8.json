{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\kanban-board\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport authService from '../../utils/authService';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport BoardHeader from './components/BoardHeader';\nimport BoardColumn from './components/BoardColumn';\nimport AddCardModal from './components/AddCardModal';\nimport AddColumnModal from './components/AddColumnModal';\nimport InviteMemberModal from './components/InviteMemberModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KanbanBoard = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n\n  // User state and role\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n\n  // Project context - get from location state or default\n  const [currentProject] = useState(() => {\n    const locationState = window.location.state;\n    return locationState !== null && locationState !== void 0 && locationState.projectId ? {\n      id: locationState.projectId,\n      name: 'Current Project',\n      memberRole: 'assigned' // This would come from API\n    } : {\n      id: 1,\n      name: 'Website Redesign',\n      memberRole: 'assigned' // assigned, not-assigned\n    };\n  });\n\n  // Check if current user is assigned to this project\n  const isUserAssignedToProject = () => {\n    // For members, check if they're assigned to this specific project\n    if (userRole === 'member') {\n      return currentProject.memberRole === 'assigned';\n    }\n    // Admins and owners have access to all projects\n    return ['admin', 'owner'].includes(userRole);\n  };\n\n  // Mock data\n  const [board] = useState({\n    id: 'board-1',\n    title: 'Project Management Board',\n    description: 'Main project tracking board for Q4 initiatives',\n    isPrivate: false,\n    createdAt: '2025-01-15T10:00:00Z',\n    updatedAt: '2025-01-28T05:54:23Z'\n  });\n\n  // Real members data - will be loaded from team service\n  const [members, setMembers] = useState([]);\n  const [columns, setColumns] = useState([{\n    id: 'col-1',\n    title: 'To Do',\n    status: 'todo',\n    order: 1,\n    createdAt: '2025-01-15T10:00:00Z'\n  }, {\n    id: 'col-2',\n    title: 'In Progress',\n    status: 'in-progress',\n    order: 2,\n    createdAt: '2025-01-15T10:00:00Z'\n  }, {\n    id: 'col-3',\n    title: 'Review',\n    status: 'review',\n    order: 3,\n    createdAt: '2025-01-15T10:00:00Z'\n  }, {\n    id: 'col-4',\n    title: 'Done',\n    status: 'done',\n    order: 4,\n    createdAt: '2025-01-15T10:00:00Z'\n  }]);\n  const [cards, setCards] = useState([{\n    id: 'card-1',\n    columnId: 'col-1',\n    title: 'Design user authentication flow',\n    description: 'Create wireframes and mockups for the login and registration process',\n    priority: 'high',\n    assignedTo: ['user-1', 'user-2'],\n    dueDate: '2025-02-05',\n    labels: [{\n      id: 'design',\n      name: 'Design',\n      color: '#3b82f6'\n    }, {\n      id: 'ux',\n      name: 'UX',\n      color: '#8b5cf6'\n    }],\n    checklist: [{\n      id: 'check-1',\n      text: 'Research competitor flows',\n      completed: true\n    }, {\n      id: 'check-2',\n      text: 'Create wireframes',\n      completed: false\n    }, {\n      id: 'check-3',\n      text: 'Design mockups',\n      completed: false\n    }],\n    comments: [{\n      id: 'comment-1',\n      author: 'user-2',\n      content: 'Should we include social login options?',\n      createdAt: '2025-01-27T14:30:00Z'\n    }],\n    attachments: [],\n    createdAt: '2025-01-25T09:00:00Z',\n    updatedAt: '2025-01-27T14:30:00Z'\n  }, {\n    id: 'card-2',\n    columnId: 'col-1',\n    title: 'Set up project repository',\n    description: 'Initialize Git repository with proper folder structure and documentation',\n    priority: 'medium',\n    assignedTo: ['user-3'],\n    dueDate: '2025-01-30',\n    labels: [{\n      id: 'development',\n      name: 'Development',\n      color: '#10b981'\n    }],\n    checklist: [],\n    comments: [],\n    attachments: [],\n    createdAt: '2025-01-26T11:00:00Z',\n    updatedAt: '2025-01-26T11:00:00Z'\n  }, {\n    id: 'card-3',\n    columnId: 'col-2',\n    title: 'Implement user registration API',\n    description: 'Build backend endpoints for user registration with validation and email verification',\n    priority: 'high',\n    assignedTo: ['user-2', 'user-5'],\n    dueDate: '2025-02-10',\n    labels: [{\n      id: 'backend',\n      name: 'Backend',\n      color: '#f59e0b'\n    }, {\n      id: 'api',\n      name: 'API',\n      color: '#ef4444'\n    }],\n    checklist: [{\n      id: 'check-4',\n      text: 'Design database schema',\n      completed: true\n    }, {\n      id: 'check-5',\n      text: 'Implement validation',\n      completed: true\n    }, {\n      id: 'check-6',\n      text: 'Add email verification',\n      completed: false\n    }, {\n      id: 'check-7',\n      text: 'Write unit tests',\n      completed: false\n    }],\n    comments: [{\n      id: 'comment-2',\n      author: 'user-1',\n      content: 'Make sure to include proper error handling',\n      createdAt: '2025-01-26T16:45:00Z'\n    }, {\n      id: 'comment-3',\n      author: 'user-5',\n      content: 'Working on the email service integration',\n      createdAt: '2025-01-27T10:15:00Z'\n    }],\n    attachments: [{\n      id: 'att-1',\n      name: 'api-spec.pdf',\n      size: '2.4 MB'\n    }],\n    createdAt: '2025-01-24T13:00:00Z',\n    updatedAt: '2025-01-27T10:15:00Z'\n  }, {\n    id: 'card-4',\n    columnId: 'col-3',\n    title: 'Review dashboard components',\n    description: 'Code review for the new dashboard UI components',\n    priority: 'medium',\n    assignedTo: ['user-1', 'user-4'],\n    dueDate: '2025-01-29',\n    labels: [{\n      id: 'review',\n      name: 'Review',\n      color: '#8b5cf6'\n    }, {\n      id: 'frontend',\n      name: 'Frontend',\n      color: '#06b6d4'\n    }],\n    checklist: [{\n      id: 'check-8',\n      text: 'Check code quality',\n      completed: true\n    }, {\n      id: 'check-9',\n      text: 'Test responsiveness',\n      completed: false\n    }, {\n      id: 'check-10',\n      text: 'Verify accessibility',\n      completed: false\n    }],\n    comments: [],\n    attachments: [],\n    createdAt: '2025-01-23T15:30:00Z',\n    updatedAt: '2025-01-27T09:20:00Z'\n  }, {\n    id: 'card-5',\n    columnId: 'col-4',\n    title: 'Update project documentation',\n    description: 'Refresh README and API documentation with latest changes',\n    priority: 'low',\n    assignedTo: ['user-3'],\n    dueDate: null,\n    labels: [{\n      id: 'documentation',\n      name: 'Documentation',\n      color: '#f59e0b'\n    }],\n    checklist: [{\n      id: 'check-11',\n      text: 'Update README',\n      completed: true\n    }, {\n      id: 'check-12',\n      text: 'Update API docs',\n      completed: true\n    }, {\n      id: 'check-13',\n      text: 'Add deployment guide',\n      completed: true\n    }],\n    comments: [{\n      id: 'comment-4',\n      author: 'user-1',\n      content: 'Great work on the documentation!',\n      createdAt: '2025-01-25T12:00:00Z'\n    }],\n    attachments: [],\n    createdAt: '2025-01-20T10:00:00Z',\n    updatedAt: '2025-01-25T12:00:00Z'\n  }]);\n\n  // Modal states\n  const [showAddCardModal, setShowAddCardModal] = useState(false);\n  const [showAddColumnModal, setShowAddColumnModal] = useState(false);\n  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);\n  const [selectedColumnId, setSelectedColumnId] = useState(null);\n\n  // Filter and search states\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeFilters, setActiveFilters] = useState({});\n\n  // Load user data and role\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || 'member');\n        }\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n      }\n    };\n    loadUserData();\n  }, []);\n\n  // Role-based and project-based permission checks\n  const canCreateCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canEditCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canDeleteCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canCreateColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canEditColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canDeleteColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canInviteMembers = () => {\n    return ['admin', 'owner'].includes(userRole);\n  };\n  const canDragCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  // Filter cards based on search and filters\n  const filteredCards = cards.filter(card => {\n    var _activeFilters$priori, _activeFilters$assign, _activeFilters$dueDat;\n    // Search filter\n    if (searchQuery) {\n      var _card$labels;\n      const query = searchQuery.toLowerCase();\n      const matchesSearch = card.title.toLowerCase().includes(query) || card.description.toLowerCase().includes(query) || ((_card$labels = card.labels) === null || _card$labels === void 0 ? void 0 : _card$labels.some(label => label.name.toLowerCase().includes(query)));\n      if (!matchesSearch) return false;\n    }\n\n    // Priority filter\n    if (((_activeFilters$priori = activeFilters.priority) === null || _activeFilters$priori === void 0 ? void 0 : _activeFilters$priori.length) > 0) {\n      if (!activeFilters.priority.includes(card.priority)) return false;\n    }\n\n    // Assignee filter\n    if (((_activeFilters$assign = activeFilters.assignee) === null || _activeFilters$assign === void 0 ? void 0 : _activeFilters$assign.length) > 0) {\n      var _card$assignedTo;\n      const hasAssignee = (_card$assignedTo = card.assignedTo) === null || _card$assignedTo === void 0 ? void 0 : _card$assignedTo.some(assigneeId => activeFilters.assignee.includes(assigneeId));\n      if (!hasAssignee) return false;\n    }\n\n    // Due date filter\n    if (((_activeFilters$dueDat = activeFilters.dueDate) === null || _activeFilters$dueDat === void 0 ? void 0 : _activeFilters$dueDat.length) > 0) {\n      const today = new Date();\n      const cardDueDate = card.dueDate ? new Date(card.dueDate) : null;\n      const matchesDueDate = activeFilters.dueDate.some(filter => {\n        if (filter === 'overdue') {\n          return cardDueDate && cardDueDate < today;\n        }\n        if (filter === 'today') {\n          return cardDueDate && cardDueDate.toDateString() === today.toDateString();\n        }\n        if (filter === 'this-week') {\n          const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n          return cardDueDate && cardDueDate >= today && cardDueDate <= weekFromNow;\n        }\n        if (filter === 'this-month') {\n          const monthFromNow = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());\n          return cardDueDate && cardDueDate >= today && cardDueDate <= monthFromNow;\n        }\n        return false;\n      });\n      if (!matchesDueDate) return false;\n    }\n    return true;\n  });\n\n  // Handle card movement between columns\n  const handleCardMove = (cardId, sourceColumnId, targetColumnId) => {\n    // Check if user can drag cards\n    if (!canDragCards()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot move cards');\n      } else {\n        console.log('You can only move cards in projects you are assigned to');\n      }\n      return;\n    }\n    setCards(prevCards => prevCards.map(card => card.id === cardId ? {\n      ...card,\n      columnId: targetColumnId,\n      updatedAt: new Date().toISOString()\n    } : card));\n  };\n\n  // Handle card click - navigate to card details\n  const handleCardClick = card => {\n    console.log('Navigating to card details:', card);\n\n    // Save current cards to localStorage for persistence\n    localStorage.setItem('kanban-cards', JSON.stringify(cards));\n\n    // Navigate with card data in state and URL params\n    navigate(`/card-details?id=${card.id}`, {\n      state: {\n        card: card,\n        members: members,\n        returnPath: '/kanban-board'\n      }\n    });\n  };\n\n  // Handle adding new card\n  const handleAddCard = columnId => {\n    if (!canCreateCards()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot create cards');\n      } else {\n        console.log('You can only create cards in projects you are assigned to');\n      }\n      return;\n    }\n    setSelectedColumnId(columnId);\n    setShowAddCardModal(true);\n  };\n  const handleSaveCard = newCard => {\n    const updatedCards = [...cards, newCard];\n    setCards(updatedCards);\n    // Save to localStorage for persistence\n    localStorage.setItem('kanban-cards', JSON.stringify(updatedCards));\n    console.log('Card saved:', newCard);\n  };\n\n  // Handle adding new column\n  const handleSaveColumn = newColumn => {\n    if (!canCreateColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot create columns');\n      } else {\n        console.log('You can only create columns in projects you are assigned to');\n      }\n      return;\n    }\n    setColumns(prevColumns => [...prevColumns, newColumn]);\n  };\n\n  // Handle column operations\n  const handleEditColumn = (columnId, updates) => {\n    if (!canEditColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot edit columns');\n      } else {\n        console.log('You can only edit columns in projects you are assigned to');\n      }\n      return;\n    }\n    setColumns(prevColumns => prevColumns.map(col => col.id === columnId ? {\n      ...col,\n      ...updates,\n      updatedAt: new Date().toISOString()\n    } : col));\n  };\n  const handleDeleteColumn = columnId => {\n    var _columns$;\n    if (!canDeleteColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot delete columns');\n      } else {\n        console.log('You can only delete columns in projects you are assigned to');\n      }\n      return;\n    }\n    // Move cards from deleted column to first column\n    const firstColumnId = (_columns$ = columns[0]) === null || _columns$ === void 0 ? void 0 : _columns$.id;\n    if (firstColumnId && firstColumnId !== columnId) {\n      setCards(prevCards => prevCards.map(card => card.columnId === columnId ? {\n        ...card,\n        columnId: firstColumnId\n      } : card));\n    }\n    setColumns(prevColumns => prevColumns.filter(col => col.id !== columnId));\n  };\n\n  // Handle member invitation\n  const handleMemberInvite = () => {\n    if (!canInviteMembers()) {\n      console.log('Only admins and owners can invite members');\n      return;\n    }\n    setShowInviteMemberModal(true);\n  };\n  const handleSendInvitation = invitation => {\n    console.log('Invitation sent:', invitation);\n    // In real app, this would send the invitation via API\n  };\n\n  // Handle board updates\n  const handleBoardUpdate = updates => {\n    console.log('Board updated:', updates);\n    // In real app, this would update the board via API\n  };\n\n  // Get cards for a specific column\n  const getCardsForColumn = columnId => {\n    return filteredCards.filter(card => card.columnId === columnId);\n  };\n  return /*#__PURE__*/_jsxDEV(DndProvider, {\n    backend: HTML5Backend,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"pt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-full px-4 sm:px-6 lg:px-8 py-8\",\n          children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-primary rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"Kanban\",\n                  size: 20,\n                  className: \"text-primary-foreground\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl font-bold text-foreground\",\n                  children: \"Projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted-foreground\",\n                  children: \"Manage tasks and track project progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(BoardHeader, {\n            board: board,\n            members: members,\n            onBoardUpdate: handleBoardUpdate,\n            onMemberInvite: handleMemberInvite,\n            onFilterChange: setActiveFilters,\n            onSearchChange: setSearchQuery,\n            searchQuery: searchQuery,\n            activeFilters: activeFilters,\n            canInviteMembers: canInviteMembers()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-6 overflow-x-auto pb-6\",\n              children: [columns.sort((a, b) => a.order - b.order).map(column => /*#__PURE__*/_jsxDEV(BoardColumn, {\n                column: column,\n                cards: getCardsForColumn(column.id),\n                onCardMove: handleCardMove,\n                onCardClick: handleCardClick,\n                onAddCard: handleAddCard,\n                onEditColumn: handleEditColumn,\n                onDeleteColumn: handleDeleteColumn,\n                members: members,\n                canCreateCards: canCreateCards(),\n                canEditColumns: canEditColumns(),\n                canDeleteColumns: canDeleteColumns(),\n                canDragCards: canDragCards()\n              }, column.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this)), canCreateColumns() && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline\",\n                  onClick: () => setShowAddColumnModal(true),\n                  className: \"w-80 h-32 border-2 border-dashed border-border hover:border-primary hover:bg-primary/5 transition-colors\",\n                  iconName: \"Plus\",\n                  iconPosition: \"left\",\n                  children: \"Add Column\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 11\n            }, this), filteredCards.length === 0 && searchQuery && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Search\",\n                size: 48,\n                className: \"text-text-secondary mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-text-primary mb-2\",\n                children: \"No cards found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary text-center max-w-md\",\n                children: \"No cards match your search criteria. Try adjusting your search terms or filters.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                onClick: () => {\n                  setSearchQuery('');\n                  setActiveFilters({});\n                },\n                className: \"mt-4\",\n                children: \"Clear Search & Filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(AddCardModal, {\n            isOpen: showAddCardModal,\n            onClose: () => {\n              setShowAddCardModal(false);\n              setSelectedColumnId(null);\n            },\n            onSave: handleSaveCard,\n            columnId: selectedColumnId,\n            members: members\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(AddColumnModal, {\n            isOpen: showAddColumnModal,\n            onClose: () => setShowAddColumnModal(false),\n            onSave: handleSaveColumn\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(InviteMemberModal, {\n            isOpen: showInviteMemberModal,\n            onClose: () => setShowInviteMemberModal(false),\n            onInvite: handleSendInvitation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 495,\n    columnNumber: 5\n  }, this);\n};\n_s(KanbanBoard, \"gDrzygfkv+3iayUR9eESxqsn77k=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = KanbanBoard;\nexport default KanbanBoard;\nvar _c;\n$RefreshReg$(_c, \"KanbanBoard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "DndProvider", "HTML5Backend", "useNavigate", "useAuth", "authService", "RoleBasedHeader", "Breadcrumb", "Icon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "BoardColumn", "AddCardModal", "AddColumnModal", "InviteMemberModal", "jsxDEV", "_jsxDEV", "KanbanBoard", "_s", "navigate", "user", "currentUser", "setCurrentUser", "userRole", "setUserRole", "currentProject", "locationState", "window", "location", "state", "projectId", "id", "name", "memberRole", "isUserAssignedToProject", "includes", "board", "title", "description", "isPrivate", "createdAt", "updatedAt", "members", "setMembers", "columns", "setColumns", "status", "order", "cards", "setCards", "columnId", "priority", "assignedTo", "dueDate", "labels", "color", "checklist", "text", "completed", "comments", "author", "content", "attachments", "size", "showAddCardModal", "setShowAddCardModal", "showAddColumnModal", "setShowAddColumnModal", "showInviteMemberModal", "setShowInviteMemberModal", "selectedColumnId", "setSelectedColumnId", "searchQuery", "setSearch<PERSON>uery", "activeFilters", "setActiveFilters", "loadUserData", "userResult", "getCurrentUser", "data", "role", "error", "console", "canCreateCards", "canEditCards", "canDeleteCards", "canCreateColumns", "canEditColumns", "canDeleteColumns", "canInviteMembers", "canDragCards", "filteredCards", "filter", "card", "_activeFilters$priori", "_activeFilters$assign", "_activeFilters$dueDat", "_card$labels", "query", "toLowerCase", "matchesSearch", "some", "label", "length", "assignee", "_card$assignedTo", "<PERSON>As<PERSON>ee", "assigneeId", "today", "Date", "cardDueDate", "matchesDueDate", "toDateString", "weekFromNow", "getTime", "monthFromNow", "getFullYear", "getMonth", "getDate", "handleCardMove", "cardId", "sourceColumnId", "targetColumnId", "log", "prevCards", "map", "toISOString", "handleCardClick", "localStorage", "setItem", "JSON", "stringify", "returnPath", "handleAddCard", "handleSaveCard", "newCard", "updatedCards", "handleSaveColumn", "newColumn", "prevColumns", "handleEditColumn", "updates", "col", "handleDeleteColumn", "_columns$", "firstColumnId", "handleMemberInvite", "handleSendInvitation", "invitation", "handleBoardUpdate", "getCardsForColumn", "backend", "children", "className", "firstName", "lastName", "email", "avatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onBoardUpdate", "onMemberInvite", "onFilterChange", "onSearchChange", "sort", "a", "b", "column", "onCardMove", "onCardClick", "onAddCard", "onEditColumn", "onDeleteColumn", "variant", "onClick", "iconName", "iconPosition", "isOpen", "onClose", "onSave", "onInvite", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/kanban-board/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport authService from '../../utils/authService';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport BoardHeader from './components/BoardHeader';\nimport BoardColumn from './components/BoardColumn';\n\nimport AddCardModal from './components/AddCardModal';\nimport AddColumnModal from './components/AddColumnModal';\nimport InviteMemberModal from './components/InviteMemberModal';\n\nconst KanbanBoard = () => {\n  const navigate = useNavigate();\n  const { user } = useAuth();\n\n  // User state and role\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n\n  // Project context - get from location state or default\n  const [currentProject] = useState(() => {\n    const locationState = window.location.state;\n    return locationState?.projectId ? {\n      id: locationState.projectId,\n      name: 'Current Project',\n      memberRole: 'assigned' // This would come from API\n    } : {\n      id: 1,\n      name: 'Website Redesign',\n      memberRole: 'assigned' // assigned, not-assigned\n    };\n  });\n\n  // Check if current user is assigned to this project\n  const isUserAssignedToProject = () => {\n    // For members, check if they're assigned to this specific project\n    if (userRole === 'member') {\n      return currentProject.memberRole === 'assigned';\n    }\n    // Admins and owners have access to all projects\n    return ['admin', 'owner'].includes(userRole);\n  };\n\n  // Mock data\n  const [board] = useState({\n    id: 'board-1',\n    title: 'Project Management Board',\n    description: 'Main project tracking board for Q4 initiatives',\n    isPrivate: false,\n    createdAt: '2025-01-15T10:00:00Z',\n    updatedAt: '2025-01-28T05:54:23Z'\n  });\n\n  // Real members data - will be loaded from team service\n  const [members, setMembers] = useState([]);\n\n  const [columns, setColumns] = useState([\n    {\n      id: 'col-1',\n      title: 'To Do',\n      status: 'todo',\n      order: 1,\n      createdAt: '2025-01-15T10:00:00Z'\n    },\n    {\n      id: 'col-2',\n      title: 'In Progress',\n      status: 'in-progress',\n      order: 2,\n      createdAt: '2025-01-15T10:00:00Z'\n    },\n    {\n      id: 'col-3',\n      title: 'Review',\n      status: 'review',\n      order: 3,\n      createdAt: '2025-01-15T10:00:00Z'\n    },\n    {\n      id: 'col-4',\n      title: 'Done',\n      status: 'done',\n      order: 4,\n      createdAt: '2025-01-15T10:00:00Z'\n    }\n  ]);\n\n  const [cards, setCards] = useState([\n    {\n      id: 'card-1',\n      columnId: 'col-1',\n      title: 'Design user authentication flow',\n      description: 'Create wireframes and mockups for the login and registration process',\n      priority: 'high',\n      assignedTo: ['user-1', 'user-2'],\n      dueDate: '2025-02-05',\n      labels: [\n        { id: 'design', name: 'Design', color: '#3b82f6' },\n        { id: 'ux', name: 'UX', color: '#8b5cf6' }\n      ],\n      checklist: [\n        { id: 'check-1', text: 'Research competitor flows', completed: true },\n        { id: 'check-2', text: 'Create wireframes', completed: false },\n        { id: 'check-3', text: 'Design mockups', completed: false }\n      ],\n      comments: [\n        {\n          id: 'comment-1',\n          author: 'user-2',\n          content: 'Should we include social login options?',\n          createdAt: '2025-01-27T14:30:00Z'\n        }\n      ],\n      attachments: [],\n      createdAt: '2025-01-25T09:00:00Z',\n      updatedAt: '2025-01-27T14:30:00Z'\n    },\n    {\n      id: 'card-2',\n      columnId: 'col-1',\n      title: 'Set up project repository',\n      description: 'Initialize Git repository with proper folder structure and documentation',\n      priority: 'medium',\n      assignedTo: ['user-3'],\n      dueDate: '2025-01-30',\n      labels: [\n        { id: 'development', name: 'Development', color: '#10b981' }\n      ],\n      checklist: [],\n      comments: [],\n      attachments: [],\n      createdAt: '2025-01-26T11:00:00Z',\n      updatedAt: '2025-01-26T11:00:00Z'\n    },\n    {\n      id: 'card-3',\n      columnId: 'col-2',\n      title: 'Implement user registration API',\n      description: 'Build backend endpoints for user registration with validation and email verification',\n      priority: 'high',\n      assignedTo: ['user-2', 'user-5'],\n      dueDate: '2025-02-10',\n      labels: [\n        { id: 'backend', name: 'Backend', color: '#f59e0b' },\n        { id: 'api', name: 'API', color: '#ef4444' }\n      ],\n      checklist: [\n        { id: 'check-4', text: 'Design database schema', completed: true },\n        { id: 'check-5', text: 'Implement validation', completed: true },\n        { id: 'check-6', text: 'Add email verification', completed: false },\n        { id: 'check-7', text: 'Write unit tests', completed: false }\n      ],\n      comments: [\n        {\n          id: 'comment-2',\n          author: 'user-1',\n          content: 'Make sure to include proper error handling',\n          createdAt: '2025-01-26T16:45:00Z'\n        },\n        {\n          id: 'comment-3',\n          author: 'user-5',\n          content: 'Working on the email service integration',\n          createdAt: '2025-01-27T10:15:00Z'\n        }\n      ],\n      attachments: [\n        { id: 'att-1', name: 'api-spec.pdf', size: '2.4 MB' }\n      ],\n      createdAt: '2025-01-24T13:00:00Z',\n      updatedAt: '2025-01-27T10:15:00Z'\n    },\n    {\n      id: 'card-4',\n      columnId: 'col-3',\n      title: 'Review dashboard components',\n      description: 'Code review for the new dashboard UI components',\n      priority: 'medium',\n      assignedTo: ['user-1', 'user-4'],\n      dueDate: '2025-01-29',\n      labels: [\n        { id: 'review', name: 'Review', color: '#8b5cf6' },\n        { id: 'frontend', name: 'Frontend', color: '#06b6d4' }\n      ],\n      checklist: [\n        { id: 'check-8', text: 'Check code quality', completed: true },\n        { id: 'check-9', text: 'Test responsiveness', completed: false },\n        { id: 'check-10', text: 'Verify accessibility', completed: false }\n      ],\n      comments: [],\n      attachments: [],\n      createdAt: '2025-01-23T15:30:00Z',\n      updatedAt: '2025-01-27T09:20:00Z'\n    },\n    {\n      id: 'card-5',\n      columnId: 'col-4',\n      title: 'Update project documentation',\n      description: 'Refresh README and API documentation with latest changes',\n      priority: 'low',\n      assignedTo: ['user-3'],\n      dueDate: null,\n      labels: [\n        { id: 'documentation', name: 'Documentation', color: '#f59e0b' }\n      ],\n      checklist: [\n        { id: 'check-11', text: 'Update README', completed: true },\n        { id: 'check-12', text: 'Update API docs', completed: true },\n        { id: 'check-13', text: 'Add deployment guide', completed: true }\n      ],\n      comments: [\n        {\n          id: 'comment-4',\n          author: 'user-1',\n          content: 'Great work on the documentation!',\n          createdAt: '2025-01-25T12:00:00Z'\n        }\n      ],\n      attachments: [],\n      createdAt: '2025-01-20T10:00:00Z',\n      updatedAt: '2025-01-25T12:00:00Z'\n    }\n  ]);\n\n  // Modal states\n  const [showAddCardModal, setShowAddCardModal] = useState(false);\n  const [showAddColumnModal, setShowAddColumnModal] = useState(false);\n  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);\n  const [selectedColumnId, setSelectedColumnId] = useState(null);\n\n  // Filter and search states\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeFilters, setActiveFilters] = useState({});\n\n  // Load user data and role\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || 'member');\n        }\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n      }\n    };\n\n    loadUserData();\n  }, []);\n\n  // Role-based and project-based permission checks\n  const canCreateCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canEditCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canDeleteCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canCreateColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canEditColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canDeleteColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canInviteMembers = () => {\n    return ['admin', 'owner'].includes(userRole);\n  };\n\n  const canDragCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  // Filter cards based on search and filters\n  const filteredCards = cards.filter(card => {\n    // Search filter\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      const matchesSearch = \n        card.title.toLowerCase().includes(query) ||\n        card.description.toLowerCase().includes(query) ||\n        card.labels?.some(label => label.name.toLowerCase().includes(query));\n      \n      if (!matchesSearch) return false;\n    }\n\n    // Priority filter\n    if (activeFilters.priority?.length > 0) {\n      if (!activeFilters.priority.includes(card.priority)) return false;\n    }\n\n    // Assignee filter\n    if (activeFilters.assignee?.length > 0) {\n      const hasAssignee = card.assignedTo?.some(assigneeId => \n        activeFilters.assignee.includes(assigneeId)\n      );\n      if (!hasAssignee) return false;\n    }\n\n    // Due date filter\n    if (activeFilters.dueDate?.length > 0) {\n      const today = new Date();\n      const cardDueDate = card.dueDate ? new Date(card.dueDate) : null;\n      \n      const matchesDueDate = activeFilters.dueDate.some(filter => {\n        if (filter === 'overdue') {\n          return cardDueDate && cardDueDate < today;\n        }\n        if (filter === 'today') {\n          return cardDueDate && cardDueDate.toDateString() === today.toDateString();\n        }\n        if (filter === 'this-week') {\n          const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n          return cardDueDate && cardDueDate >= today && cardDueDate <= weekFromNow;\n        }\n        if (filter === 'this-month') {\n          const monthFromNow = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());\n          return cardDueDate && cardDueDate >= today && cardDueDate <= monthFromNow;\n        }\n        return false;\n      });\n      \n      if (!matchesDueDate) return false;\n    }\n\n    return true;\n  });\n\n  // Handle card movement between columns\n  const handleCardMove = (cardId, sourceColumnId, targetColumnId) => {\n    // Check if user can drag cards\n    if (!canDragCards()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot move cards');\n      } else {\n        console.log('You can only move cards in projects you are assigned to');\n      }\n      return;\n    }\n\n    setCards(prevCards =>\n      prevCards.map(card =>\n        card.id === cardId\n          ? { ...card, columnId: targetColumnId, updatedAt: new Date().toISOString() }\n          : card\n      )\n    );\n  };\n\n  // Handle card click - navigate to card details\n  const handleCardClick = (card) => {\n    console.log('Navigating to card details:', card);\n\n    // Save current cards to localStorage for persistence\n    localStorage.setItem('kanban-cards', JSON.stringify(cards));\n\n    // Navigate with card data in state and URL params\n    navigate(`/card-details?id=${card.id}`, {\n      state: {\n        card: card,\n        members: members,\n        returnPath: '/kanban-board'\n      }\n    });\n  };\n\n  // Handle adding new card\n  const handleAddCard = (columnId) => {\n    if (!canCreateCards()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot create cards');\n      } else {\n        console.log('You can only create cards in projects you are assigned to');\n      }\n      return;\n    }\n    setSelectedColumnId(columnId);\n    setShowAddCardModal(true);\n  };\n\n  const handleSaveCard = (newCard) => {\n    const updatedCards = [...cards, newCard];\n    setCards(updatedCards);\n    // Save to localStorage for persistence\n    localStorage.setItem('kanban-cards', JSON.stringify(updatedCards));\n    console.log('Card saved:', newCard);\n  };\n\n  // Handle adding new column\n  const handleSaveColumn = (newColumn) => {\n    if (!canCreateColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot create columns');\n      } else {\n        console.log('You can only create columns in projects you are assigned to');\n      }\n      return;\n    }\n    setColumns(prevColumns => [...prevColumns, newColumn]);\n  };\n\n  // Handle column operations\n  const handleEditColumn = (columnId, updates) => {\n    if (!canEditColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot edit columns');\n      } else {\n        console.log('You can only edit columns in projects you are assigned to');\n      }\n      return;\n    }\n    setColumns(prevColumns =>\n      prevColumns.map(col =>\n        col.id === columnId\n          ? { ...col, ...updates, updatedAt: new Date().toISOString() }\n          : col\n      )\n    );\n  };\n\n  const handleDeleteColumn = (columnId) => {\n    if (!canDeleteColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot delete columns');\n      } else {\n        console.log('You can only delete columns in projects you are assigned to');\n      }\n      return;\n    }\n    // Move cards from deleted column to first column\n    const firstColumnId = columns[0]?.id;\n    if (firstColumnId && firstColumnId !== columnId) {\n      setCards(prevCards =>\n        prevCards.map(card =>\n          card.columnId === columnId\n            ? { ...card, columnId: firstColumnId }\n            : card\n        )\n      );\n    }\n    \n    setColumns(prevColumns => prevColumns.filter(col => col.id !== columnId));\n  };\n\n  // Handle member invitation\n  const handleMemberInvite = () => {\n    if (!canInviteMembers()) {\n      console.log('Only admins and owners can invite members');\n      return;\n    }\n    setShowInviteMemberModal(true);\n  };\n\n  const handleSendInvitation = (invitation) => {\n    console.log('Invitation sent:', invitation);\n    // In real app, this would send the invitation via API\n  };\n\n  // Handle board updates\n  const handleBoardUpdate = (updates) => {\n    console.log('Board updated:', updates);\n    // In real app, this would update the board via API\n  };\n\n  // Get cards for a specific column\n  const getCardsForColumn = (columnId) => {\n    return filteredCards.filter(card => card.columnId === columnId);\n  };\n\n  return (\n    <DndProvider backend={HTML5Backend}>\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n        />\n\n        <main className=\"pt-16\">\n          <div className=\"max-w-full px-4 sm:px-6 lg:px-8 py-8\">\n            <Breadcrumb />\n\n            {/* Page Header */}\n            <div className=\"mb-6\">\n              <div className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"w-10 h-10 bg-primary rounded-lg flex items-center justify-center\">\n                  <Icon name=\"Kanban\" size={20} className=\"text-primary-foreground\" />\n                </div>\n                <div>\n                  <h1 className=\"text-3xl font-bold text-foreground\">Projects</h1>\n                  <p className=\"text-muted-foreground\">\n                    Manage tasks and track project progress\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Board Header */}\n            <BoardHeader\n              board={board}\n              members={members}\n              onBoardUpdate={handleBoardUpdate}\n              onMemberInvite={handleMemberInvite}\n              onFilterChange={setActiveFilters}\n              onSearchChange={setSearchQuery}\n              searchQuery={searchQuery}\n              activeFilters={activeFilters}\n              canInviteMembers={canInviteMembers()}\n            />\n\n        {/* Board Content */}\n        <div className=\"flex-1 p-6\">\n          <div className=\"flex space-x-6 overflow-x-auto pb-6\">\n            {/* Columns */}\n            {columns\n              .sort((a, b) => a.order - b.order)\n              .map(column => (\n                <BoardColumn\n                  key={column.id}\n                  column={column}\n                  cards={getCardsForColumn(column.id)}\n                  onCardMove={handleCardMove}\n                  onCardClick={handleCardClick}\n                  onAddCard={handleAddCard}\n                  onEditColumn={handleEditColumn}\n                  onDeleteColumn={handleDeleteColumn}\n                  members={members}\n                  canCreateCards={canCreateCards()}\n                  canEditColumns={canEditColumns()}\n                  canDeleteColumns={canDeleteColumns()}\n                  canDragCards={canDragCards()}\n                />\n              ))}\n\n            {/* Add Column Button - Only show for non-viewers */}\n            {canCreateColumns() && (\n              <div className=\"flex-shrink-0\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowAddColumnModal(true)}\n                  className=\"w-80 h-32 border-2 border-dashed border-border hover:border-primary hover:bg-primary/5 transition-colors\"\n                  iconName=\"Plus\"\n                  iconPosition=\"left\"\n                >\n                  Add Column\n                </Button>\n              </div>\n            )}\n          </div>\n\n          {/* Empty State */}\n          {filteredCards.length === 0 && searchQuery && (\n            <div className=\"flex flex-col items-center justify-center py-12\">\n              <Icon name=\"Search\" size={48} className=\"text-text-secondary mb-4\" />\n              <h3 className=\"text-lg font-medium text-text-primary mb-2\">No cards found</h3>\n              <p className=\"text-text-secondary text-center max-w-md\">\n                No cards match your search criteria. Try adjusting your search terms or filters.\n              </p>\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSearchQuery('');\n                  setActiveFilters({});\n                }}\n                className=\"mt-4\"\n              >\n                Clear Search & Filters\n              </Button>\n            </div>\n          )}\n        </div>\n\n        {/* Modals */}\n        <AddCardModal\n          isOpen={showAddCardModal}\n          onClose={() => {\n            setShowAddCardModal(false);\n            setSelectedColumnId(null);\n          }}\n          onSave={handleSaveCard}\n          columnId={selectedColumnId}\n          members={members}\n        />\n\n        <AddColumnModal\n          isOpen={showAddColumnModal}\n          onClose={() => setShowAddColumnModal(false)}\n          onSave={handleSaveColumn}\n        />\n\n        <InviteMemberModal\n          isOpen={showInviteMemberModal}\n          onClose={() => setShowInviteMemberModal(false)}\n          onInvite={handleSendInvitation}\n        />\n          </div>\n        </main>\n      </div>\n    </DndProvider>\n  );\n};\n\nexport default KanbanBoard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,0BAA0B;AAElD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,iBAAiB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB;EAAK,CAAC,GAAGhB,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM,CAAC0B,cAAc,CAAC,GAAG1B,QAAQ,CAAC,MAAM;IACtC,MAAM2B,aAAa,GAAGC,MAAM,CAACC,QAAQ,CAACC,KAAK;IAC3C,OAAOH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEI,SAAS,GAAG;MAChCC,EAAE,EAAEL,aAAa,CAACI,SAAS;MAC3BE,IAAI,EAAE,iBAAiB;MACvBC,UAAU,EAAE,UAAU,CAAC;IACzB,CAAC,GAAG;MACFF,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,kBAAkB;MACxBC,UAAU,EAAE,UAAU,CAAC;IACzB,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC;IACA,IAAIX,QAAQ,KAAK,QAAQ,EAAE;MACzB,OAAOE,cAAc,CAACQ,UAAU,KAAK,UAAU;IACjD;IACA;IACA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAACE,QAAQ,CAACZ,QAAQ,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM,CAACa,KAAK,CAAC,GAAGrC,QAAQ,CAAC;IACvBgC,EAAE,EAAE,SAAS;IACbM,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,gDAAgD;IAC7DC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,CACrC;IACEgC,EAAE,EAAE,OAAO;IACXM,KAAK,EAAE,OAAO;IACdS,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,CAAC;IACRP,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,OAAO;IACXM,KAAK,EAAE,aAAa;IACpBS,MAAM,EAAE,aAAa;IACrBC,KAAK,EAAE,CAAC;IACRP,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,OAAO;IACXM,KAAK,EAAE,QAAQ;IACfS,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,CAAC;IACRP,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,OAAO;IACXM,KAAK,EAAE,MAAM;IACbS,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,CAAC;IACRP,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,CACjC;IACEgC,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,iCAAiC;IACxCC,WAAW,EAAE,sEAAsE;IACnFa,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,QAAQ;MAAEuB,KAAK,EAAE;IAAU,CAAC,EAClD;MAAExB,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAEuB,KAAK,EAAE;IAAU,CAAC,CAC3C;IACDC,SAAS,EAAE,CACT;MAAEzB,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAK,CAAC,EACrE;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,mBAAmB;MAAEC,SAAS,EAAE;IAAM,CAAC,EAC9D;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,gBAAgB;MAAEC,SAAS,EAAE;IAAM,CAAC,CAC5D;IACDC,QAAQ,EAAE,CACR;MACE5B,EAAE,EAAE,WAAW;MACf6B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,yCAAyC;MAClDrB,SAAS,EAAE;IACb,CAAC,CACF;IACDsB,WAAW,EAAE,EAAE;IACftB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,0EAA0E;IACvFa,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtBC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,aAAa;MAAEC,IAAI,EAAE,aAAa;MAAEuB,KAAK,EAAE;IAAU,CAAC,CAC7D;IACDC,SAAS,EAAE,EAAE;IACbG,QAAQ,EAAE,EAAE;IACZG,WAAW,EAAE,EAAE;IACftB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,iCAAiC;IACxCC,WAAW,EAAE,sFAAsF;IACnGa,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,SAAS;MAAEuB,KAAK,EAAE;IAAU,CAAC,EACpD;MAAExB,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAEuB,KAAK,EAAE;IAAU,CAAC,CAC7C;IACDC,SAAS,EAAE,CACT;MAAEzB,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,wBAAwB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAClE;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAChE;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,wBAAwB;MAAEC,SAAS,EAAE;IAAM,CAAC,EACnE;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,kBAAkB;MAAEC,SAAS,EAAE;IAAM,CAAC,CAC9D;IACDC,QAAQ,EAAE,CACR;MACE5B,EAAE,EAAE,WAAW;MACf6B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,4CAA4C;MACrDrB,SAAS,EAAE;IACb,CAAC,EACD;MACET,EAAE,EAAE,WAAW;MACf6B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,0CAA0C;MACnDrB,SAAS,EAAE;IACb,CAAC,CACF;IACDsB,WAAW,EAAE,CACX;MAAE/B,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,cAAc;MAAE+B,IAAI,EAAE;IAAS,CAAC,CACtD;IACDvB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,iDAAiD;IAC9Da,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,QAAQ;MAAEuB,KAAK,EAAE;IAAU,CAAC,EAClD;MAAExB,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,UAAU;MAAEuB,KAAK,EAAE;IAAU,CAAC,CACvD;IACDC,SAAS,EAAE,CACT;MAAEzB,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,oBAAoB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC9D;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,qBAAqB;MAAEC,SAAS,EAAE;IAAM,CAAC,EAChE;MAAE3B,EAAE,EAAE,UAAU;MAAE0B,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAM,CAAC,CACnE;IACDC,QAAQ,EAAE,EAAE;IACZG,WAAW,EAAE,EAAE;IACftB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,0DAA0D;IACvEa,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtBC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,eAAe;MAAEC,IAAI,EAAE,eAAe;MAAEuB,KAAK,EAAE;IAAU,CAAC,CACjE;IACDC,SAAS,EAAE,CACT;MAAEzB,EAAE,EAAE,UAAU;MAAE0B,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC1D;MAAE3B,EAAE,EAAE,UAAU;MAAE0B,IAAI,EAAE,iBAAiB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC5D;MAAE3B,EAAE,EAAE,UAAU;MAAE0B,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAK,CAAC,CAClE;IACDC,QAAQ,EAAE,CACR;MACE5B,EAAE,EAAE,WAAW;MACf6B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,kCAAkC;MAC3CrB,SAAS,EAAE;IACb,CAAC,CACF;IACDsB,WAAW,EAAE,EAAE;IACftB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACuE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4E,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,UAAU,GAAG,MAAMxE,WAAW,CAACyE,cAAc,CAAC,CAAC;QACrD,IAAID,UAAU,CAACE,IAAI,CAAC3D,IAAI,EAAE;UACxBE,cAAc,CAACuD,UAAU,CAACE,IAAI,CAAC3D,IAAI,CAAC;UACpCI,WAAW,CAACqD,UAAU,CAACE,IAAI,CAAC3D,IAAI,CAAC4D,IAAI,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAEDL,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI5D,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMkD,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI7D,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMmD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9D,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMoD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/D,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMqD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIhE,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMsD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIjE,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMuD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAACtD,QAAQ,CAACZ,QAAQ,CAAC;EAC9C,CAAC;EAED,MAAMmE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInE,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;;EAED;EACA,MAAMyD,aAAa,GAAG3C,KAAK,CAAC4C,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACzC;IACA,IAAIxB,WAAW,EAAE;MAAA,IAAAyB,YAAA;MACf,MAAMC,KAAK,GAAG1B,WAAW,CAAC2B,WAAW,CAAC,CAAC;MACvC,MAAMC,aAAa,GACjBP,IAAI,CAACxD,KAAK,CAAC8D,WAAW,CAAC,CAAC,CAAChE,QAAQ,CAAC+D,KAAK,CAAC,IACxCL,IAAI,CAACvD,WAAW,CAAC6D,WAAW,CAAC,CAAC,CAAChE,QAAQ,CAAC+D,KAAK,CAAC,MAAAD,YAAA,GAC9CJ,IAAI,CAACvC,MAAM,cAAA2C,YAAA,uBAAXA,YAAA,CAAaI,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACtE,IAAI,CAACmE,WAAW,CAAC,CAAC,CAAChE,QAAQ,CAAC+D,KAAK,CAAC,CAAC;MAEtE,IAAI,CAACE,aAAa,EAAE,OAAO,KAAK;IAClC;;IAEA;IACA,IAAI,EAAAN,qBAAA,GAAApB,aAAa,CAACvB,QAAQ,cAAA2C,qBAAA,uBAAtBA,qBAAA,CAAwBS,MAAM,IAAG,CAAC,EAAE;MACtC,IAAI,CAAC7B,aAAa,CAACvB,QAAQ,CAAChB,QAAQ,CAAC0D,IAAI,CAAC1C,QAAQ,CAAC,EAAE,OAAO,KAAK;IACnE;;IAEA;IACA,IAAI,EAAA4C,qBAAA,GAAArB,aAAa,CAAC8B,QAAQ,cAAAT,qBAAA,uBAAtBA,qBAAA,CAAwBQ,MAAM,IAAG,CAAC,EAAE;MAAA,IAAAE,gBAAA;MACtC,MAAMC,WAAW,IAAAD,gBAAA,GAAGZ,IAAI,CAACzC,UAAU,cAAAqD,gBAAA,uBAAfA,gBAAA,CAAiBJ,IAAI,CAACM,UAAU,IAClDjC,aAAa,CAAC8B,QAAQ,CAACrE,QAAQ,CAACwE,UAAU,CAC5C,CAAC;MACD,IAAI,CAACD,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAI,EAAAV,qBAAA,GAAAtB,aAAa,CAACrB,OAAO,cAAA2C,qBAAA,uBAArBA,qBAAA,CAAuBO,MAAM,IAAG,CAAC,EAAE;MACrC,MAAMK,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAMC,WAAW,GAAGjB,IAAI,CAACxC,OAAO,GAAG,IAAIwD,IAAI,CAAChB,IAAI,CAACxC,OAAO,CAAC,GAAG,IAAI;MAEhE,MAAM0D,cAAc,GAAGrC,aAAa,CAACrB,OAAO,CAACgD,IAAI,CAACT,MAAM,IAAI;QAC1D,IAAIA,MAAM,KAAK,SAAS,EAAE;UACxB,OAAOkB,WAAW,IAAIA,WAAW,GAAGF,KAAK;QAC3C;QACA,IAAIhB,MAAM,KAAK,OAAO,EAAE;UACtB,OAAOkB,WAAW,IAAIA,WAAW,CAACE,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC;QAC3E;QACA,IAAIpB,MAAM,KAAK,WAAW,EAAE;UAC1B,MAAMqB,WAAW,GAAG,IAAIJ,IAAI,CAACD,KAAK,CAACM,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;UACvE,OAAOJ,WAAW,IAAIA,WAAW,IAAIF,KAAK,IAAIE,WAAW,IAAIG,WAAW;QAC1E;QACA,IAAIrB,MAAM,KAAK,YAAY,EAAE;UAC3B,MAAMuB,YAAY,GAAG,IAAIN,IAAI,CAACD,KAAK,CAACQ,WAAW,CAAC,CAAC,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAET,KAAK,CAACU,OAAO,CAAC,CAAC,CAAC;UACzF,OAAOR,WAAW,IAAIA,WAAW,IAAIF,KAAK,IAAIE,WAAW,IAAIK,YAAY;QAC3E;QACA,OAAO,KAAK;MACd,CAAC,CAAC;MAEF,IAAI,CAACJ,cAAc,EAAE,OAAO,KAAK;IACnC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMQ,cAAc,GAAGA,CAACC,MAAM,EAAEC,cAAc,EAAEC,cAAc,KAAK;IACjE;IACA,IAAI,CAAChC,YAAY,CAAC,CAAC,EAAE;MACnB,IAAInE,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACyC,GAAG,CAAC,2BAA2B,CAAC;MAC1C,CAAC,MAAM;QACLzC,OAAO,CAACyC,GAAG,CAAC,yDAAyD,CAAC;MACxE;MACA;IACF;IAEA1E,QAAQ,CAAC2E,SAAS,IAChBA,SAAS,CAACC,GAAG,CAAChC,IAAI,IAChBA,IAAI,CAAC9D,EAAE,KAAKyF,MAAM,GACd;MAAE,GAAG3B,IAAI;MAAE3C,QAAQ,EAAEwE,cAAc;MAAEjF,SAAS,EAAE,IAAIoE,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC;IAAE,CAAC,GAC1EjC,IACN,CACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAIlC,IAAI,IAAK;IAChCX,OAAO,CAACyC,GAAG,CAAC,6BAA6B,EAAE9B,IAAI,CAAC;;IAEhD;IACAmC,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACnF,KAAK,CAAC,CAAC;;IAE3D;IACA7B,QAAQ,CAAC,oBAAoB0E,IAAI,CAAC9D,EAAE,EAAE,EAAE;MACtCF,KAAK,EAAE;QACLgE,IAAI,EAAEA,IAAI;QACVnD,OAAO,EAAEA,OAAO;QAChB0F,UAAU,EAAE;MACd;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,aAAa,GAAInF,QAAQ,IAAK;IAClC,IAAI,CAACiC,cAAc,CAAC,CAAC,EAAE;MACrB,IAAI5D,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACyC,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,MAAM;QACLzC,OAAO,CAACyC,GAAG,CAAC,2DAA2D,CAAC;MAC1E;MACA;IACF;IACApD,mBAAmB,CAACrB,QAAQ,CAAC;IAC7Be,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMqE,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAG,CAAC,GAAGxF,KAAK,EAAEuF,OAAO,CAAC;IACxCtF,QAAQ,CAACuF,YAAY,CAAC;IACtB;IACAR,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACK,YAAY,CAAC,CAAC;IAClEtD,OAAO,CAACyC,GAAG,CAAC,aAAa,EAAEY,OAAO,CAAC;EACrC,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAIC,SAAS,IAAK;IACtC,IAAI,CAACpD,gBAAgB,CAAC,CAAC,EAAE;MACvB,IAAI/D,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACyC,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACLzC,OAAO,CAACyC,GAAG,CAAC,6DAA6D,CAAC;MAC5E;MACA;IACF;IACA9E,UAAU,CAAC8F,WAAW,IAAI,CAAC,GAAGA,WAAW,EAAED,SAAS,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAC1F,QAAQ,EAAE2F,OAAO,KAAK;IAC9C,IAAI,CAACtD,cAAc,CAAC,CAAC,EAAE;MACrB,IAAIhE,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACyC,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,MAAM;QACLzC,OAAO,CAACyC,GAAG,CAAC,2DAA2D,CAAC;MAC1E;MACA;IACF;IACA9E,UAAU,CAAC8F,WAAW,IACpBA,WAAW,CAACd,GAAG,CAACiB,GAAG,IACjBA,GAAG,CAAC/G,EAAE,KAAKmB,QAAQ,GACf;MAAE,GAAG4F,GAAG;MAAE,GAAGD,OAAO;MAAEpG,SAAS,EAAE,IAAIoE,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC;IAAE,CAAC,GAC3DgB,GACN,CACF,CAAC;EACH,CAAC;EAED,MAAMC,kBAAkB,GAAI7F,QAAQ,IAAK;IAAA,IAAA8F,SAAA;IACvC,IAAI,CAACxD,gBAAgB,CAAC,CAAC,EAAE;MACvB,IAAIjE,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACyC,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACLzC,OAAO,CAACyC,GAAG,CAAC,6DAA6D,CAAC;MAC5E;MACA;IACF;IACA;IACA,MAAMsB,aAAa,IAAAD,SAAA,GAAGpG,OAAO,CAAC,CAAC,CAAC,cAAAoG,SAAA,uBAAVA,SAAA,CAAYjH,EAAE;IACpC,IAAIkH,aAAa,IAAIA,aAAa,KAAK/F,QAAQ,EAAE;MAC/CD,QAAQ,CAAC2E,SAAS,IAChBA,SAAS,CAACC,GAAG,CAAChC,IAAI,IAChBA,IAAI,CAAC3C,QAAQ,KAAKA,QAAQ,GACtB;QAAE,GAAG2C,IAAI;QAAE3C,QAAQ,EAAE+F;MAAc,CAAC,GACpCpD,IACN,CACF,CAAC;IACH;IAEAhD,UAAU,CAAC8F,WAAW,IAAIA,WAAW,CAAC/C,MAAM,CAACkD,GAAG,IAAIA,GAAG,CAAC/G,EAAE,KAAKmB,QAAQ,CAAC,CAAC;EAC3E,CAAC;;EAED;EACA,MAAMgG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACzD,gBAAgB,CAAC,CAAC,EAAE;MACvBP,OAAO,CAACyC,GAAG,CAAC,2CAA2C,CAAC;MACxD;IACF;IACAtD,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAM8E,oBAAoB,GAAIC,UAAU,IAAK;IAC3ClE,OAAO,CAACyC,GAAG,CAAC,kBAAkB,EAAEyB,UAAU,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIR,OAAO,IAAK;IACrC3D,OAAO,CAACyC,GAAG,CAAC,gBAAgB,EAAEkB,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAIpG,QAAQ,IAAK;IACtC,OAAOyC,aAAa,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC3C,QAAQ,KAAKA,QAAQ,CAAC;EACjE,CAAC;EAED,oBACElC,OAAA,CAACf,WAAW;IAACsJ,OAAO,EAAErJ,YAAa;IAAAsJ,QAAA,eACjCxI,OAAA;MAAKyI,SAAS,EAAC,4BAA4B;MAAAD,QAAA,gBACzCxI,OAAA,CAACV,eAAe;QACdiB,QAAQ,EAAEA,QAAQ,CAAC4E,WAAW,CAAC,CAAE;QACjC9E,WAAW,EAAEA,WAAW,GAAG;UACzBW,IAAI,EAAE,GAAGX,WAAW,CAACqI,SAAS,IAAIrI,WAAW,CAACsI,QAAQ,EAAE;UACxDC,KAAK,EAAEvI,WAAW,CAACuI,KAAK;UACxBC,MAAM,EAAExI,WAAW,CAACwI,MAAM,IAAI,2BAA2B;UACzD7E,IAAI,EAAEzD;QACR,CAAC,GAAG;UACFS,IAAI,EAAE,YAAY;UAClB4H,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnC7E,IAAI,EAAEzD;QACR;MAAE;QAAAuI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFjJ,OAAA;QAAMyI,SAAS,EAAC,OAAO;QAAAD,QAAA,eACrBxI,OAAA;UAAKyI,SAAS,EAAC,sCAAsC;UAAAD,QAAA,gBACnDxI,OAAA,CAACT,UAAU;YAAAuJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGdjJ,OAAA;YAAKyI,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnBxI,OAAA;cAAKyI,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC/CxI,OAAA;gBAAKyI,SAAS,EAAC,kEAAkE;gBAAAD,QAAA,eAC/ExI,OAAA,CAACR,IAAI;kBAACwB,IAAI,EAAC,QAAQ;kBAAC+B,IAAI,EAAE,EAAG;kBAAC0F,SAAS,EAAC;gBAAyB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNjJ,OAAA;gBAAAwI,QAAA,gBACExI,OAAA;kBAAIyI,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChEjJ,OAAA;kBAAGyI,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjJ,OAAA,CAACN,WAAW;YACV0B,KAAK,EAAEA,KAAM;YACbM,OAAO,EAAEA,OAAQ;YACjBwH,aAAa,EAAEb,iBAAkB;YACjCc,cAAc,EAAEjB,kBAAmB;YACnCkB,cAAc,EAAEzF,gBAAiB;YACjC0F,cAAc,EAAE5F,cAAe;YAC/BD,WAAW,EAAEA,WAAY;YACzBE,aAAa,EAAEA,aAAc;YAC7Be,gBAAgB,EAAEA,gBAAgB,CAAC;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAGNjJ,OAAA;YAAKyI,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzBxI,OAAA;cAAKyI,SAAS,EAAC,qCAAqC;cAAAD,QAAA,GAEjD5G,OAAO,CACL0H,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxH,KAAK,GAAGyH,CAAC,CAACzH,KAAK,CAAC,CACjC8E,GAAG,CAAC4C,MAAM,iBACTzJ,OAAA,CAACL,WAAW;gBAEV8J,MAAM,EAAEA,MAAO;gBACfzH,KAAK,EAAEsG,iBAAiB,CAACmB,MAAM,CAAC1I,EAAE,CAAE;gBACpC2I,UAAU,EAAEnD,cAAe;gBAC3BoD,WAAW,EAAE5C,eAAgB;gBAC7B6C,SAAS,EAAEvC,aAAc;gBACzBwC,YAAY,EAAEjC,gBAAiB;gBAC/BkC,cAAc,EAAE/B,kBAAmB;gBACnCrG,OAAO,EAAEA,OAAQ;gBACjByC,cAAc,EAAEA,cAAc,CAAC,CAAE;gBACjCI,cAAc,EAAEA,cAAc,CAAC,CAAE;gBACjCC,gBAAgB,EAAEA,gBAAgB,CAAC,CAAE;gBACrCE,YAAY,EAAEA,YAAY,CAAC;cAAE,GAZxB+E,MAAM,CAAC1I,EAAE;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACF,CAAC,EAGH3E,gBAAgB,CAAC,CAAC,iBACjBtE,OAAA;gBAAKyI,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC5BxI,OAAA,CAACP,MAAM;kBACLsK,OAAO,EAAC,SAAS;kBACjBC,OAAO,EAAEA,CAAA,KAAM7G,qBAAqB,CAAC,IAAI,CAAE;kBAC3CsF,SAAS,EAAC,0GAA0G;kBACpHwB,QAAQ,EAAC,MAAM;kBACfC,YAAY,EAAC,MAAM;kBAAA1B,QAAA,EACpB;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGLtE,aAAa,CAACY,MAAM,KAAK,CAAC,IAAI/B,WAAW,iBACxCxD,OAAA;cAAKyI,SAAS,EAAC,iDAAiD;cAAAD,QAAA,gBAC9DxI,OAAA,CAACR,IAAI;gBAACwB,IAAI,EAAC,QAAQ;gBAAC+B,IAAI,EAAE,EAAG;gBAAC0F,SAAS,EAAC;cAA0B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrEjJ,OAAA;gBAAIyI,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9EjJ,OAAA;gBAAGyI,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAExD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJjJ,OAAA,CAACP,MAAM;gBACLsK,OAAO,EAAC,SAAS;gBACjBC,OAAO,EAAEA,CAAA,KAAM;kBACbvG,cAAc,CAAC,EAAE,CAAC;kBAClBE,gBAAgB,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAE;gBACF8E,SAAS,EAAC,MAAM;gBAAAD,QAAA,EACjB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNjJ,OAAA,CAACJ,YAAY;YACXuK,MAAM,EAAEnH,gBAAiB;YACzBoH,OAAO,EAAEA,CAAA,KAAM;cACbnH,mBAAmB,CAAC,KAAK,CAAC;cAC1BM,mBAAmB,CAAC,IAAI,CAAC;YAC3B,CAAE;YACF8G,MAAM,EAAE/C,cAAe;YACvBpF,QAAQ,EAAEoB,gBAAiB;YAC3B5B,OAAO,EAAEA;UAAQ;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEFjJ,OAAA,CAACH,cAAc;YACbsK,MAAM,EAAEjH,kBAAmB;YAC3BkH,OAAO,EAAEA,CAAA,KAAMjH,qBAAqB,CAAC,KAAK,CAAE;YAC5CkH,MAAM,EAAE5C;UAAiB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEFjJ,OAAA,CAACF,iBAAiB;YAChBqK,MAAM,EAAE/G,qBAAsB;YAC9BgH,OAAO,EAAEA,CAAA,KAAM/G,wBAAwB,CAAC,KAAK,CAAE;YAC/CiH,QAAQ,EAAEnC;UAAqB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAAC/I,EAAA,CAxmBID,WAAW;EAAA,QACEd,WAAW,EACXC,OAAO;AAAA;AAAAmL,EAAA,GAFpBtK,WAAW;AA0mBjB,eAAeA,WAAW;AAAC,IAAAsK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}