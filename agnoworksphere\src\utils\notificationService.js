import realApiService from './realApiService';

/**
 * Notification service for managing user notifications
 */

// Get notifications for the current user
export const getNotifications = async (filters = {}) => {
  try {
    console.log('Fetching notifications with filters:', filters);

    // For now, return mock data since the API endpoint doesn't exist yet
    const mockNotifications = [
      {
        id: '1',
        title: 'Welcome to Agno WorkSphere',
        message: 'Your account has been successfully created.',
        type: 'info',
        priority: 'medium',
        read: false,
        createdAt: new Date().toISOString(),
        actionUrl: null
      },
      {
        id: '2',
        title: 'Project Update',
        message: 'New task has been assigned to you.',
        type: 'task',
        priority: 'high',
        read: false,
        createdAt: new Date(Date.now() - 3600000).toISOString(),
        actionUrl: '/kanban-board'
      }
    ];

    return {
      success: true,
      data: mockNotifications,
      pagination: {
        page: filters.page || 1,
        limit: filters.limit || 20,
        total: mockNotifications.length,
        totalPages: 1
      }
    };
  } catch (error) {
    console.error('Failed to fetch notifications:', error);

    // Return empty array on error
    return {
      success: false,
      data: [],
      error: error.message
    };
  }
};

// Mark notification as read
export const markNotificationAsRead = async (notificationId) => {
  try {
    console.log(`Marking notification ${notificationId} as read`);

    // Mock successful response
    return {
      success: true,
      message: 'Notification marked as read',
      data: { id: notificationId, read: true }
    };
  } catch (error) {
    console.error('Failed to mark notification as read:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Mark all notifications as read
export const markAllNotificationsAsRead = async () => {
  try {
    console.log('Marking all notifications as read');

    // Mock successful response
    return {
      success: true,
      message: 'All notifications marked as read',
      data: { markedCount: 2 }
    };
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Create a welcome notification for new users
export const createWelcomeNotification = async (userId, organizationName) => {
  try {
    console.log(`Creating welcome notification for user ${userId} in organization ${organizationName}`);

    const welcomeNotification = {
      id: `welcome_${userId}_${Date.now()}`,
      type: 'welcome',
      title: 'Welcome to Agno WorkSphere!',
      message: `Welcome to ${organizationName}! We're excited to have you on board. Start by exploring your dashboard and setting up your first project.`,
      priority: 'high',
      read: false,
      createdAt: new Date().toISOString(),
      data: {
        isWelcome: true,
        organizationName,
        actions: [
          { label: 'Get Started', variant: 'default', action: 'tour' },
          { label: 'View Profile', variant: 'outline', action: 'profile' }
        ]
      }
    };

    // Mock successful creation
    return {
      success: true,
      data: welcomeNotification,
      message: 'Welcome notification created successfully'
    };
  } catch (error) {
    console.error('Failed to create welcome notification:', error);
    // Don't throw error for welcome notifications - they're not critical
    return null;
  }
};

// Create a notification
export const createNotification = async (notificationData) => {
  try {
    console.log('Creating notification:', notificationData);

    const notification = {
      id: `notif_${Date.now()}`,
      ...notificationData,
      createdAt: new Date().toISOString(),
      read: false
    };

    // Mock successful creation
    return {
      success: true,
      data: notification,
      message: 'Notification created successfully'
    };
  } catch (error) {
    console.error('Failed to create notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Delete notification
export const deleteNotification = async (notificationId) => {
  try {
    console.log(`Deleting notification ${notificationId}`);

    // Mock successful deletion
    return {
      success: true,
      message: 'Notification deleted successfully',
      data: { id: notificationId }
    };
  } catch (error) {
    console.error('Failed to delete notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Get notification preferences
export const getNotificationPreferences = async () => {
  try {
    console.log('Fetching notification preferences');

    // Mock preferences
    const mockPreferences = {
      email: true,
      push: true,
      inApp: true,
      types: {
        tasks: true,
        projects: true,
        mentions: true,
        deadlines: true,
        system: false
      }
    };

    return mockPreferences;
  } catch (error) {
    console.error('Failed to fetch notification preferences:', error);
    return {};
  }
};

// Update notification preferences
export const updateNotificationPreferences = async (preferences) => {
  try {
    console.log('Updating notification preferences:', preferences);

    // Mock successful update
    return {
      success: true,
      data: preferences,
      message: 'Notification preferences updated successfully'
    };
  } catch (error) {
    console.error('Failed to update notification preferences:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Check if user is a first-time user and needs welcome notification
export const checkFirstTimeUser = async () => {
  try {
    // Check if user has any notifications or if this is their first login
    const result = await getNotifications({ limit: 1 });
    const notifications = result.data || [];

    // If no notifications exist, this might be a first-time user
    if (notifications.length === 0) {
      return true;
    }

    // Check if there's already a welcome notification
    const hasWelcomeNotification = notifications.some(n => n.type === 'welcome' || n.data?.isWelcome);
    return !hasWelcomeNotification;
  } catch (error) {
    console.error('Failed to check first-time user status:', error);
    return false;
  }
};

// Generate mock notifications for development/fallback
export const generateMockNotifications = () => {
  return [
    {
      id: 'welcome_001',
      type: 'welcome',
      title: 'Welcome to Agno WorkSphere!',
      message: 'Welcome to your new workspace! Start by exploring your dashboard and setting up your first project.',
      timestamp: new Date(),
      isRead: false,
      read: false,
      priority: 'high',
      data: {
        isWelcome: true,
        actions: [
          { label: 'Get Started', variant: 'default', action: 'tour' },
          { label: 'View Profile', variant: 'outline', action: 'profile' }
        ]
      }
    }
  ];
};

// Default export
const notificationService = {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  createWelcomeNotification,
  createNotification,
  deleteNotification,
  getNotificationPreferences,
  updateNotificationPreferences,
  checkFirstTimeUser,
  generateMockNotifications
};

export default notificationService;
