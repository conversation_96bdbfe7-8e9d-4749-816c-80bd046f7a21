{"ast": null, "code": "// src/utils/realApiService.js\n// Real API service that connects to the backend\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';\n\n// Helper function to get headers with authentication\nconst getAuthHeaders = (organizationId = null) => {\n  const token = localStorage.getItem('accessToken');\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(organizationId && {\n      'X-Organization-ID': organizationId\n    })\n  };\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n  return headers;\n};\n\n// Helper function to handle API responses\nconst handleResponse = async response => {\n  const result = await response.json();\n  if (!response.ok) {\n    var _result$error;\n    throw new Error(((_result$error = result.error) === null || _result$error === void 0 ? void 0 : _result$error.message) || result.message || 'API request failed');\n  }\n  return result;\n};\nconst realApiService = {\n  // Authentication\n  auth: {\n    // Register new user\n    register: async userData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            email: userData.email,\n            password: userData.password,\n            first_name: userData.firstName || userData.first_name || '',\n            last_name: userData.lastName !== undefined ? userData.lastName : userData.last_name || '',\n            organization_name: userData.organizationName || userData.organization_name || '',\n            organization_slug: userData.organizationSlug || userData.organization_slug || ''\n          })\n        });\n        const result = await handleResponse(response);\n\n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n          if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Registration error:', error);\n        return {\n          data: null,\n          error: error.message || 'Registration failed'\n        };\n      }\n    },\n    // Login user\n    login: async (email, password) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            email,\n            password\n          })\n        });\n        const result = await handleResponse(response);\n\n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n          if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Login error:', error);\n        return {\n          data: null,\n          error: error.message || 'Login failed'\n        };\n      }\n    },\n    // Get current user profile\n    getCurrentUser: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/profile`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: {\n            user: result.data\n          },\n          error: null\n        };\n      } catch (error) {\n        console.error('Get current user error:', error);\n        return {\n          data: {\n            user: null\n          },\n          error: error.message || 'Failed to get user profile'\n        };\n      }\n    },\n    // Update user profile\n    updateProfile: async profileData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/profile`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(profileData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Update profile error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to update profile'\n        };\n      }\n    },\n    // Logout\n    logout: async () => {\n      try {\n        // Clear stored tokens and user data\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('organizationId');\n        localStorage.removeItem('currentUser');\n        return {\n          error: null\n        };\n      } catch (error) {\n        return {\n          error: error.message || 'Logout failed'\n        };\n      }\n    },\n    // Check if user is authenticated\n    isAuthenticated: () => {\n      const token = localStorage.getItem('accessToken');\n      return !!token;\n    },\n    // Get stored access token\n    getAccessToken: () => {\n      return localStorage.getItem('accessToken');\n    },\n    // Get user role\n    getUserRole: () => {\n      return localStorage.getItem('userRole') || 'member';\n    },\n    // Get organization ID\n    getOrganizationId: () => {\n      return localStorage.getItem('organizationId');\n    }\n  },\n  // Organizations\n  organizations: {\n    // Get all organizations\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organizations error:', error);\n        throw error;\n      }\n    },\n    // Get organization by ID\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization error:', error);\n        throw error;\n      }\n    }\n  },\n  // Projects\n  projects: {\n    // Get all projects\n    getAll: async organizationId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'GET',\n          headers: getAuthHeaders(organizationId)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get projects error:', error);\n        throw error;\n      }\n    },\n    // Get project by ID\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get project error:', error);\n        throw error;\n      }\n    },\n    // Create project\n    create: async (organizationId, projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'POST',\n          headers: getAuthHeaders(organizationId),\n          body: JSON.stringify(projectData)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Create project error:', error);\n        throw error;\n      }\n    },\n    // Update project\n    update: async (id, projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(projectData)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Update project error:', error);\n        throw error;\n      }\n    },\n    // Delete project\n    delete: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Delete project error:', error);\n        throw error;\n      }\n    }\n  },\n  // Boards\n  boards: {\n    // Get boards by project\n    getByProject: async projectId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/boards?project_id=${projectId}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get boards error:', error);\n        throw error;\n      }\n    }\n  },\n  // Dashboard\n  dashboard: {\n    // Get dashboard stats\n    getStats: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/stats`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get dashboard stats error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get dashboard stats'\n        };\n      }\n    }\n  }\n};\nexport default realApiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "getAuthHeaders", "organizationId", "token", "localStorage", "getItem", "headers", "handleResponse", "response", "result", "json", "ok", "_result$error", "Error", "error", "message", "realApiService", "auth", "register", "userData", "fetch", "method", "body", "JSON", "stringify", "email", "password", "first_name", "firstName", "last_name", "lastName", "undefined", "organization_name", "organizationName", "organization_slug", "organizationSlug", "data", "tokens", "setItem", "access_token", "user", "organizations", "length", "id", "role", "console", "login", "getCurrentUser", "updateProfile", "profileData", "logout", "removeItem", "isAuthenticated", "getAccessToken", "getUserRole", "getOrganizationId", "getAll", "getById", "projects", "create", "projectData", "update", "delete", "success", "boards", "getByProject", "projectId", "dashboard", "getStats"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/utils/realApiService.js"], "sourcesContent": ["// src/utils/realApiService.js\n// Real API service that connects to the backend\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';\n\n// Helper function to get headers with authentication\nconst getAuthHeaders = (organizationId = null) => {\n  const token = localStorage.getItem('accessToken');\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(organizationId && { 'X-Organization-ID': organizationId })\n  };\n\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n\n  return headers;\n};\n\n// Helper function to handle API responses\nconst handleResponse = async (response) => {\n  const result = await response.json();\n\n  if (!response.ok) {\n    throw new Error(result.error?.message || result.message || 'API request failed');\n  }\n\n  return result;\n};\n\nconst realApiService = {\n  // Authentication\n  auth: {\n    // Register new user\n    register: async (userData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email: userData.email,\n            password: userData.password,\n            first_name: userData.firstName || userData.first_name || '',\n            last_name: userData.lastName !== undefined ? userData.lastName : (userData.last_name || ''),\n            organization_name: userData.organizationName || userData.organization_name || '',\n            organization_slug: userData.organizationSlug || userData.organization_slug || ''\n          }),\n        });\n\n        const result = await handleResponse(response);\n        \n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n          \n          if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Registration error:', error);\n        return {\n          data: null,\n          error: error.message || 'Registration failed'\n        };\n      }\n    },\n\n    // Login user\n    login: async (email, password) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email,\n            password\n          }),\n        });\n\n        const result = await handleResponse(response);\n        \n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n          \n          if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Login error:', error);\n        return {\n          data: null,\n          error: error.message || 'Login failed'\n        };\n      }\n    },\n\n    // Get current user profile\n    getCurrentUser: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/profile`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n\n        return {\n          data: {\n            user: result.data\n          },\n          error: null\n        };\n      } catch (error) {\n        console.error('Get current user error:', error);\n        return {\n          data: { user: null },\n          error: error.message || 'Failed to get user profile'\n        };\n      }\n    },\n\n    // Update user profile\n    updateProfile: async (profileData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/profile`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(profileData),\n        });\n\n        const result = await handleResponse(response);\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Update profile error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to update profile'\n        };\n      }\n    },\n\n    // Logout\n    logout: async () => {\n      try {\n        // Clear stored tokens and user data\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('organizationId');\n        localStorage.removeItem('currentUser');\n\n        return {\n          error: null\n        };\n      } catch (error) {\n        return {\n          error: error.message || 'Logout failed'\n        };\n      }\n    },\n\n    // Check if user is authenticated\n    isAuthenticated: () => {\n      const token = localStorage.getItem('accessToken');\n      return !!token;\n    },\n\n    // Get stored access token\n    getAccessToken: () => {\n      return localStorage.getItem('accessToken');\n    },\n\n    // Get user role\n    getUserRole: () => {\n      return localStorage.getItem('userRole') || 'member';\n    },\n\n    // Get organization ID\n    getOrganizationId: () => {\n      return localStorage.getItem('organizationId');\n    }\n  },\n\n  // Organizations\n  organizations: {\n    // Get all organizations\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organizations error:', error);\n        throw error;\n      }\n    },\n\n    // Get organization by ID\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Projects\n  projects: {\n    // Get all projects\n    getAll: async (organizationId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'GET',\n          headers: getAuthHeaders(organizationId),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get projects error:', error);\n        throw error;\n      }\n    },\n\n    // Get project by ID\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get project error:', error);\n        throw error;\n      }\n    },\n\n    // Create project\n    create: async (organizationId, projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'POST',\n          headers: getAuthHeaders(organizationId),\n          body: JSON.stringify(projectData),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Create project error:', error);\n        throw error;\n      }\n    },\n\n    // Update project\n    update: async (id, projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(projectData),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Update project error:', error);\n        throw error;\n      }\n    },\n\n    // Delete project\n    delete: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result.data };\n      } catch (error) {\n        console.error('Delete project error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Boards\n  boards: {\n    // Get boards by project\n    getByProject: async (projectId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/boards?project_id=${projectId}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get boards error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Dashboard\n  dashboard: {\n    // Get dashboard stats\n    getStats: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/stats`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get dashboard stats error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get dashboard stats'\n        };\n      }\n    }\n  }\n};\n\nexport default realApiService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA,MAAMC,cAAc,GAAGA,CAACC,cAAc,GAAG,IAAI,KAAK;EAChD,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EACjD,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE,kBAAkB;IAClC,IAAIJ,cAAc,IAAI;MAAE,mBAAmB,EAAEA;IAAe,CAAC;EAC/D,CAAC;EAED,IAAIC,KAAK,EAAE;IACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;EAC9C;EAEA,OAAOG,OAAO;AAChB,CAAC;;AAED;AACA,MAAMC,cAAc,GAAG,MAAOC,QAAQ,IAAK;EACzC,MAAMC,MAAM,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;EAEpC,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;IAAA,IAAAC,aAAA;IAChB,MAAM,IAAIC,KAAK,CAAC,EAAAD,aAAA,GAAAH,MAAM,CAACK,KAAK,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,OAAO,KAAIN,MAAM,CAACM,OAAO,IAAI,oBAAoB,CAAC;EAClF;EAEA,OAAON,MAAM;AACf,CAAC;AAED,MAAMO,cAAc,GAAG;EACrB;EACAC,IAAI,EAAE;IACJ;IACAC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;MAC5B,IAAI;QACF,MAAMX,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,uBAAuB,EAAE;UACnEwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDgB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,KAAK,EAAEN,QAAQ,CAACM,KAAK;YACrBC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ;YAC3BC,UAAU,EAAER,QAAQ,CAACS,SAAS,IAAIT,QAAQ,CAACQ,UAAU,IAAI,EAAE;YAC3DE,SAAS,EAAEV,QAAQ,CAACW,QAAQ,KAAKC,SAAS,GAAGZ,QAAQ,CAACW,QAAQ,GAAIX,QAAQ,CAACU,SAAS,IAAI,EAAG;YAC3FG,iBAAiB,EAAEb,QAAQ,CAACc,gBAAgB,IAAId,QAAQ,CAACa,iBAAiB,IAAI,EAAE;YAChFE,iBAAiB,EAAEf,QAAQ,CAACgB,gBAAgB,IAAIhB,QAAQ,CAACe,iBAAiB,IAAI;UAChF,CAAC;QACH,CAAC,CAAC;QAEF,MAAMzB,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;;QAE7C;QACA,IAAIC,MAAM,CAAC2B,IAAI,IAAI3B,MAAM,CAAC2B,IAAI,CAACC,MAAM,EAAE;UACrCjC,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAE7B,MAAM,CAAC2B,IAAI,CAACC,MAAM,CAACE,YAAY,CAAC;UACpEnC,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAEf,IAAI,CAACC,SAAS,CAACf,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAAC,CAAC;UAErE,IAAI/B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,IAAIhC,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;YAC/EtC,YAAY,CAACkC,OAAO,CAAC,gBAAgB,EAAE7B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC;YAC5EvC,YAAY,CAACkC,OAAO,CAAC,UAAU,EAAE7B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC;UAC1E;QACF;QAEA,OAAO;UACLR,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACA+B,KAAK,EAAE,MAAAA,CAAOrB,KAAK,EAAEC,QAAQ,KAAK;MAChC,IAAI;QACF,MAAMlB,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,oBAAoB,EAAE;UAChEwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDgB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,KAAK;YACLC;UACF,CAAC;QACH,CAAC,CAAC;QAEF,MAAMjB,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;;QAE7C;QACA,IAAIC,MAAM,CAAC2B,IAAI,IAAI3B,MAAM,CAAC2B,IAAI,CAACC,MAAM,EAAE;UACrCjC,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAE7B,MAAM,CAAC2B,IAAI,CAACC,MAAM,CAACE,YAAY,CAAC;UACpEnC,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAEf,IAAI,CAACC,SAAS,CAACf,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAAC,CAAC;UAErE,IAAI/B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,IAAIhC,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;YAC/EtC,YAAY,CAACkC,OAAO,CAAC,gBAAgB,EAAE7B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC;YAC5EvC,YAAY,CAACkC,OAAO,CAAC,UAAU,EAAE7B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC;UAC1E;QACF;QAEA,OAAO;UACLR,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpC,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAgC,cAAc,EAAE,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACF,MAAMvC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,uBAAuB,EAAE;UACnEwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAE7C,OAAO;UACL4B,IAAI,EAAE;YACJI,IAAI,EAAE/B,MAAM,CAAC2B;UACf,CAAC;UACDtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,OAAO;UACLsB,IAAI,EAAE;YAAEI,IAAI,EAAE;UAAK,CAAC;UACpB1B,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAiC,aAAa,EAAE,MAAOC,WAAW,IAAK;MACpC,IAAI;QACF,MAAMzC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,uBAAuB,EAAE;UACnEwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACyB,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMxC,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAE7C,OAAO;UACL4B,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAmC,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF;QACA9C,YAAY,CAAC+C,UAAU,CAAC,aAAa,CAAC;QACtC/C,YAAY,CAAC+C,UAAU,CAAC,cAAc,CAAC;QACvC/C,YAAY,CAAC+C,UAAU,CAAC,UAAU,CAAC;QACnC/C,YAAY,CAAC+C,UAAU,CAAC,gBAAgB,CAAC;QACzC/C,YAAY,CAAC+C,UAAU,CAAC,aAAa,CAAC;QAEtC,OAAO;UACLrC,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd,OAAO;UACLA,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAqC,eAAe,EAAEA,CAAA,KAAM;MACrB,MAAMjD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACjD,OAAO,CAAC,CAACF,KAAK;IAChB,CAAC;IAED;IACAkD,cAAc,EAAEA,CAAA,KAAM;MACpB,OAAOjD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC5C,CAAC;IAED;IACAiD,WAAW,EAAEA,CAAA,KAAM;MACjB,OAAOlD,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,QAAQ;IACrD,CAAC;IAED;IACAkD,iBAAiB,EAAEA,CAAA,KAAM;MACvB,OAAOnD,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC/C;EACF,CAAC;EAED;EACAoC,aAAa,EAAE;IACb;IACAe,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF,MAAMhD,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,uBAAuB,EAAE;UACnEwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA2C,OAAO,EAAE,MAAOd,EAAE,IAAK;MACrB,IAAI;QACF,MAAMnC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyB8C,EAAE,EAAE,EAAE;UACzEtB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA4C,QAAQ,EAAE;IACR;IACAF,MAAM,EAAE,MAAOtD,cAAc,IAAK;MAChC,IAAI;QACF,MAAMM,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,kBAAkB,EAAE;UAC9DwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAACC,cAAc;QACxC,CAAC,CAAC;QAEF,MAAMO,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA2C,OAAO,EAAE,MAAOd,EAAE,IAAK;MACrB,IAAI;QACF,MAAMnC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,oBAAoB8C,EAAE,EAAE,EAAE;UACpEtB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA6C,MAAM,EAAE,MAAAA,CAAOzD,cAAc,EAAE0D,WAAW,KAAK;MAC7C,IAAI;QACF,MAAMpD,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,kBAAkB,EAAE;UAC9DwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAACC,cAAc,CAAC;UACvCoB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACoC,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMnD,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA+C,MAAM,EAAE,MAAAA,CAAOlB,EAAE,EAAEiB,WAAW,KAAK;MACjC,IAAI;QACF,MAAMpD,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,oBAAoB8C,EAAE,EAAE,EAAE;UACpEtB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACoC,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMnD,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAgD,MAAM,EAAE,MAAOnB,EAAE,IAAK;MACpB,IAAI;QACF,MAAMnC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,oBAAoB8C,EAAE,EAAE,EAAE;UACpEtB,MAAM,EAAE,QAAQ;UAChBf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEuD,OAAO,EAAE,IAAI;UAAE3B,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC7C,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAkD,MAAM,EAAE;IACN;IACAC,YAAY,EAAE,MAAOC,SAAS,IAAK;MACjC,IAAI;QACF,MAAM1D,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,6BAA6BqE,SAAS,EAAE,EAAE;UACpF7C,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAqD,SAAS,EAAE;IACT;IACAC,QAAQ,EAAE,MAAAA,CAAA,KAAY;MACpB,IAAI;QACF,MAAM5D,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyB,EAAE;UACrEwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACL4B,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF;EACF;AACF,CAAC;AAED,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}