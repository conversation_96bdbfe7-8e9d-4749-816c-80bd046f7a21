{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\ui\\\\RoleBasedHeader.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport Icon from '../AppIcon';\nimport Button from './Button';\nimport authService from '../../utils/authService';\nimport * as notificationService from '../../utils/notificationService';\nimport CreateProjectModal from '../modals/CreateProjectModal';\nimport CreateOrganizationModal from '../modals/CreateOrganizationModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoleBasedHeader = ({\n  userRole = 'member',\n  currentUser: propCurrentUser,\n  currentOrganization: propCurrentOrganization\n}) => {\n  _s();\n  var _organization$name, _organization$name2;\n  const [isOrgDropdownOpen, setIsOrgDropdownOpen] = useState(false);\n  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = useState(false);\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] = useState(false);\n  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState(false);\n  const [isCreateOrganizationModalOpen, setIsCreateOrganizationModalOpen] = useState(false);\n  const location = useLocation();\n  const navigate = useNavigate();\n  const orgDropdownRef = useRef(null);\n  const projectDropdownRef = useRef(null);\n  const userDropdownRef = useRef(null);\n  const mobileMenuRef = useRef(null);\n  const notificationDropdownRef = useRef(null);\n\n  // State for real user and organization data\n  const [currentUser, setCurrentUser] = useState(propCurrentUser || {\n    name: 'Loading...',\n    email: '',\n    avatar: '/assets/images/avatar.jpg',\n    role: userRole\n  });\n  const [organization, setOrganization] = useState(propCurrentOrganization || {\n    name: 'Loading...',\n    domain: '',\n    logo: '/assets/images/org-logo.png'\n  });\n  const [availableOrganizations, setAvailableOrganizations] = useState([]);\n\n  // Load real user and organization data\n  useEffect(() => {\n    const loadUserAndOrganizationData = async () => {\n      try {\n        const result = await authService.getCurrentUser();\n        console.log('Auth service result:', result); // Debug log\n\n        if (result.data && result.data.user && result.data.user.email) {\n          const userData = result.data.user;\n\n          // Construct user name with better fallback logic\n          let userName = '';\n          if (userData.firstName || userData.lastName) {\n            userName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim();\n          }\n          if (!userName && userData.email) {\n            userName = userData.email.split('@')[0];\n          }\n          if (!userName) {\n            userName = 'User';\n          }\n          console.log('User data loaded:', {\n            userData,\n            userName\n          }); // Debug log\n\n          setCurrentUser({\n            name: userName,\n            email: userData.email || '',\n            avatar: userData.avatar || userData.profilePicture || '/assets/images/avatar.jpg',\n            role: userData.role || userRole\n          });\n\n          // Load organizations\n          if (result.data.organizations && result.data.organizations.length > 0) {\n            const userOrgs = result.data.organizations.map(org => {\n              var _org$organization, _org$organization2, _org$organization3;\n              return {\n                id: org.id || ((_org$organization = org.organization) === null || _org$organization === void 0 ? void 0 : _org$organization.id),\n                name: org.name || ((_org$organization2 = org.organization) === null || _org$organization2 === void 0 ? void 0 : _org$organization2.name),\n                domain: org.domain || ((_org$organization3 = org.organization) === null || _org$organization3 === void 0 ? void 0 : _org$organization3.domain),\n                role: org.role\n              };\n            });\n            setAvailableOrganizations(userOrgs);\n\n            // Set current organization\n            const currentOrgId = authService.getOrganizationId();\n            const currentOrg = userOrgs.find(org => org.id === currentOrgId) || userOrgs[0];\n            if (currentOrg) {\n              setOrganization({\n                id: currentOrg.id,\n                name: currentOrg.name || 'Organization',\n                domain: currentOrg.domain || '',\n                logo: '/assets/images/org-logo.png'\n              });\n            }\n          }\n        } else {\n          console.warn('No user data received from API');\n          console.log('Full API result:', result);\n          // Only set fallback if we truly have no data\n        }\n      } catch (error) {\n        var _error$message, _error$message2;\n        console.error('Failed to load user and organization data:', error);\n\n        // Only set fallback data if we truly can't get user data\n        // Check if it's an authentication error\n        if ((_error$message = error.message) !== null && _error$message !== void 0 && _error$message.includes('401') || (_error$message2 = error.message) !== null && _error$message2 !== void 0 && _error$message2.includes('Unauthorized')) {\n          console.warn('User not authenticated, using fallback data');\n\n          // Set fallback user data to prevent crashes\n          setCurrentUser({\n            name: 'User',\n            email: '<EMAIL>',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          });\n\n          // Set fallback organization\n          setOrganization({\n            id: 'fallback-org',\n            name: 'Organization',\n            domain: 'example.com',\n            logo: '/assets/images/org-logo.png'\n          });\n        } else {\n          // For other errors, log but don't override data\n          console.error('Non-auth error loading user data:', error);\n        }\n      }\n    };\n\n    // Only load if not provided via props\n    if (!propCurrentUser || !propCurrentOrganization) {\n      loadUserAndOrganizationData();\n    }\n  }, [propCurrentUser, propCurrentOrganization, userRole]);\n\n  // Update state when props change\n  useEffect(() => {\n    if (propCurrentUser) {\n      setCurrentUser(propCurrentUser);\n    }\n  }, [propCurrentUser]);\n  useEffect(() => {\n    if (propCurrentOrganization) {\n      setOrganization(propCurrentOrganization);\n    }\n  }, [propCurrentOrganization]);\n\n  // Use the current user data\n  const user = currentUser;\n\n  // Project state and data\n  const [currentProject, setCurrentProject] = useState(null);\n  const [availableProjects, setAvailableProjects] = useState([]);\n\n  // Load projects when organization changes\n  useEffect(() => {\n    const loadProjects = async () => {\n      if (!(organization !== null && organization !== void 0 && organization.id)) return;\n      try {\n        // Import apiService dynamically to avoid circular imports\n        const {\n          default: apiService\n        } = await import('../../utils/apiService');\n        const projects = await apiService.projects.getAll(organization.id);\n        const transformedProjects = (projects || []).map(project => ({\n          id: project.id,\n          name: project.name || 'Untitled Project',\n          description: project.description || '',\n          status: project.status || 'active'\n        }));\n        setAvailableProjects(transformedProjects);\n\n        // Set current project from localStorage or first project\n        const savedProjectId = localStorage.getItem('currentProjectId');\n        if (savedProjectId) {\n          const savedProject = transformedProjects.find(p => p.id === savedProjectId);\n          if (savedProject) {\n            setCurrentProject(savedProject);\n          }\n        } else if (transformedProjects.length > 0) {\n          setCurrentProject(transformedProjects[0]);\n          localStorage.setItem('currentProjectId', transformedProjects[0].id);\n        }\n      } catch (error) {\n        console.error('Failed to load projects:', error);\n        setAvailableProjects([]);\n      }\n    };\n    loadProjects();\n  }, [organization === null || organization === void 0 ? void 0 : organization.id]);\n\n  // Role-based navigation configuration\n  const getNavigationItems = role => {\n    const baseItems = [{\n      label: 'Dashboard',\n      path: '/role-based-dashboard',\n      icon: 'Home',\n      roles: ['viewer', 'member', 'admin', 'owner']\n    }, {\n      label: 'Projects',\n      path: '/kanban-board',\n      icon: 'Kanban',\n      roles: ['viewer', 'member', 'admin', 'owner']\n    }, {\n      label: 'Team Members',\n      path: '/team-members',\n      icon: 'Users',\n      roles: ['member', 'admin', 'owner']\n    }];\n    const adminItems = [\n      // Organization settings moved to owner-only items\n    ];\n    const ownerItems = [{\n      label: 'Organization',\n      path: '/organization-settings',\n      icon: 'Settings',\n      roles: ['owner']\n    }, {\n      label: 'Analytics',\n      path: '/analytics',\n      icon: 'BarChart3',\n      roles: ['owner']\n    }, {\n      label: 'Billing',\n      path: '/billing',\n      icon: 'CreditCard',\n      roles: ['owner']\n    }];\n\n    // Filter items based on user role\n    const allItems = [...baseItems, ...adminItems, ...ownerItems];\n    return allItems.filter(item => item.roles.includes(role.toLowerCase()));\n  };\n\n  // Get role-specific features\n  const getRoleFeatures = role => {\n    const features = {\n      viewer: {\n        canCreateProjects: false,\n        canInviteMembers: false,\n        canManageSettings: false,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: false\n      },\n      member: {\n        canCreateProjects: true,\n        canInviteMembers: false,\n        canManageSettings: false,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: false\n      },\n      admin: {\n        canCreateProjects: true,\n        canInviteMembers: true,\n        canManageSettings: true,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: true\n      },\n      owner: {\n        canCreateProjects: true,\n        canInviteMembers: true,\n        canManageSettings: true,\n        canViewAnalytics: true,\n        canManageBilling: true,\n        canSwitchOrganizations: true\n      }\n    };\n    return features[role.toLowerCase()] || features.viewer;\n  };\n  const navigationItems = getNavigationItems(userRole);\n  const roleFeatures = getRoleFeatures(userRole);\n\n  // Enhanced notification data with comprehensive structure\n  const [notifications, setNotifications] = useState([]); // Real notifications loaded from API\n\n  // Mock notifications removed - now using real API data\n\n  // Load real notifications from API\n  useEffect(() => {\n    const loadNotifications = async () => {\n      try {\n        const realNotifications = await notificationService.getNotifications({\n          limit: 10\n        });\n        setNotifications(realNotifications || []);\n      } catch (error) {\n        console.error('Failed to load notifications:', error);\n        setNotifications([]);\n      }\n    };\n    loadNotifications();\n  }, []);\n\n  // Notification state and filtering\n  const [notificationFilter, setNotificationFilter] = useState('all'); // 'all', 'unread', 'high_priority'\n  const [isLoading, setIsLoading] = useState(false);\n  const unreadCount = notifications.filter(n => !n.isRead).length;\n  const highPriorityCount = notifications.filter(n => n.priority === 'high' && !n.isRead).length;\n\n  // Filter notifications based on current filter\n  const filteredNotifications = notifications.filter(notification => {\n    switch (notificationFilter) {\n      case 'unread':\n        return !notification.isRead;\n      case 'high_priority':\n        return notification.priority === 'high';\n      default:\n        return true;\n    }\n  });\n  const markAsRead = notificationId => {\n    setNotifications(prev => prev.map(notification => notification.id === notificationId ? {\n      ...notification,\n      isRead: true\n    } : notification));\n  };\n  const markAllAsRead = () => {\n    setNotifications(prev => prev.map(notification => ({\n      ...notification,\n      isRead: true\n    })));\n  };\n  const getNotificationIcon = type => {\n    switch (type) {\n      case 'task_assignment':\n        return 'UserPlus';\n      case 'meeting_invite':\n        return 'Calendar';\n      case 'meeting_reminder':\n        return 'Clock';\n      case 'ai_suggestion':\n        return 'Zap';\n      case 'project_update':\n        return 'AlertTriangle';\n      default:\n        return 'Bell';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-destructive';\n      case 'medium':\n        return 'text-warning';\n      case 'low':\n        return 'text-muted-foreground';\n      default:\n        return 'text-muted-foreground';\n    }\n  };\n  const formatTimestamp = timestamp => {\n    const now = new Date();\n    const diff = now - timestamp;\n    const minutes = Math.floor(diff / (1000 * 60));\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    if (minutes < 1) return 'Just now';\n    if (minutes < 60) return `${minutes}m ago`;\n    if (hours < 24) return `${hours}h ago`;\n    if (days < 7) return `${days}d ago`;\n    return timestamp.toLocaleDateString();\n  };\n\n  // Role-based notification filtering\n  const getRoleBasedNotifications = (notifications, userRole) => {\n    return notifications.filter(notification => {\n      switch (notification.type) {\n        case 'meeting_invite':\n          // All roles can receive meeting invites\n          return true;\n        case 'ai_suggestion':\n          // AI suggestions available to all roles\n          return true;\n        case 'task_assignment':\n          // Task assignments for members and above only\n          // Viewers cannot receive task assignments in this system\n          return ['member', 'admin', 'owner'].includes(userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase());\n        case 'project_update':\n          // Project updates for all roles\n          return true;\n        default:\n          return true;\n      }\n    });\n  };\n\n  // Notification action handlers\n  const handleNotificationAction = async (notificationId, action, notificationData) => {\n    setIsLoading(true);\n    try {\n      switch (action) {\n        case 'accept_task':\n          // Accept task assignment\n          console.log('Accepting task:', notificationData.taskId);\n          // Navigate to kanban board with task highlighted\n          navigate('/kanban-board', {\n            state: {\n              projectId: notificationData.projectId,\n              highlightTaskId: notificationData.taskId,\n              taskAccepted: true\n            }\n          });\n          // Remove notification after acceptance\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n        case 'view_task':\n          // View task details\n          navigate('/kanban-board', {\n            state: {\n              projectId: notificationData.projectId,\n              highlightTaskId: notificationData.taskId\n            }\n          });\n          markAsRead(notificationId);\n          break;\n        case 'decline_task':\n          // Decline task assignment\n          const reason = prompt('Please provide a reason for declining this task (optional):');\n          console.log('Declining task:', notificationData.taskId, 'Reason:', reason);\n          // Remove notification after declining\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n        case 'accept_meeting':\n          // Accept meeting invitation\n          console.log('Accepting meeting:', notificationData.meetingId);\n          // Update meeting status and remove notification\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n        case 'decline_meeting':\n          // Decline meeting invitation\n          console.log('Declining meeting:', notificationData.meetingId);\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n        case 'view_meeting':\n          // View meeting details\n          console.log('Viewing meeting details:', notificationData.meetingId);\n          markAsRead(notificationId);\n          break;\n        case 'join_meeting':\n          // Join meeting (open meeting link)\n          console.log('Joining meeting:', notificationData.meetingId);\n          window.open('https://zoom.us/j/meeting-room', '_blank');\n          markAsRead(notificationId);\n          break;\n        case 'apply_ai_suggestion':\n          // Apply AI suggestion\n          console.log('Applying AI suggestion:', notificationData.suggestionType);\n          markAsRead(notificationId);\n          break;\n        case 'view_project':\n          // View project details\n          navigate('/project-overview', {\n            state: {\n              projectId: notificationData.projectId\n            }\n          });\n          markAsRead(notificationId);\n          break;\n        default:\n          console.log('Unknown action:', action);\n          markAsRead(notificationId);\n      }\n    } catch (error) {\n      console.error('Error handling notification action:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const isActivePath = path => location.pathname === path;\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target)) {\n        setIsOrgDropdownOpen(false);\n      }\n      if (projectDropdownRef.current && !projectDropdownRef.current.contains(event.target)) {\n        setIsProjectDropdownOpen(false);\n      }\n      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {\n        setIsUserDropdownOpen(false);\n      }\n      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {\n        setIsMobileMenuOpen(false);\n      }\n      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target)) {\n        setIsNotificationDropdownOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n  const handleOrganizationSwitch = async orgId => {\n    try {\n      console.log('Switching to organization:', orgId);\n\n      // Find the organization in available organizations\n      const selectedOrg = availableOrganizations.find(org => org.id === orgId);\n      if (selectedOrg) {\n        // Update local storage\n        localStorage.setItem('organizationId', orgId);\n        localStorage.setItem('userRole', selectedOrg.role);\n\n        // Update state\n        setOrganization({\n          id: selectedOrg.id,\n          name: selectedOrg.name,\n          domain: selectedOrg.domain,\n          logo: '/assets/images/org-logo.png'\n        });\n\n        // Refresh the page to load data for the new organization\n        window.location.reload();\n      }\n      setIsOrgDropdownOpen(false);\n    } catch (error) {\n      console.error('Failed to switch organization:', error);\n      setIsOrgDropdownOpen(false);\n    }\n  };\n  const handleProjectSwitch = project => {\n    console.log('Switching to project:', project);\n    setCurrentProject(project);\n    setIsProjectDropdownOpen(false);\n\n    // Save selected project to localStorage\n    localStorage.setItem('currentProjectId', project.id);\n\n    // Navigate to the project overview page with project ID in URL\n    navigate(`/project-overview?id=${project.id}`, {\n      state: {\n        projectId: project.id,\n        project: project\n      }\n    });\n  };\n  const handleCreateProject = () => {\n    console.log('Creating new project...');\n    setIsProjectDropdownOpen(false);\n    setIsCreateProjectModalOpen(true);\n  };\n  const handleCreateProjectSubmit = async projectData => {\n    try {\n      console.log('Creating project:', projectData);\n      // Here you would typically call an API to create the project\n      // For now, we'll just add it to the available projects\n      const newProject = {\n        id: Date.now(),\n        name: projectData.name,\n        description: projectData.description,\n        status: 'active',\n        memberRole: 'assigned'\n      };\n\n      // Update current project to the newly created one\n      setCurrentProject(newProject);\n      setIsCreateProjectModalOpen(false);\n\n      // Navigate to the new project's overview page\n      navigate(`/project-overview?id=${newProject.id}`, {\n        state: {\n          projectId: newProject.id,\n          project: newProject\n        }\n      });\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      throw error;\n    }\n  };\n  const handleCreateOrganization = () => {\n    console.log('Creating new organization...');\n    setIsOrgDropdownOpen(false);\n    setIsCreateOrganizationModalOpen(true);\n  };\n  const handleCreateOrganizationSubmit = async (organizationData, logoFile) => {\n    try {\n      console.log('Creating organization:', organizationData);\n      // Here you would typically call an API to create the organization\n      // For now, we'll just simulate the creation\n      const newOrganization = {\n        id: Date.now(),\n        name: organizationData.name,\n        description: organizationData.description,\n        logo_url: logoFile ? URL.createObjectURL(logoFile) : null,\n        ...organizationData\n      };\n      setIsCreateOrganizationModalOpen(false);\n\n      // Show success message and potentially navigate to organization settings\n      console.log('Organization created successfully:', newOrganization);\n      // You might want to refresh the organization list or navigate somewhere\n      navigate('/organization-settings', {\n        state: {\n          message: `Organization \"${organizationData.name}\" created successfully!`,\n          type: 'success'\n        }\n      });\n    } catch (error) {\n      console.error('Failed to create organization:', error);\n      throw error;\n    }\n  };\n  const handleLogout = async () => {\n    try {\n      console.log('Logging out...');\n\n      // Clear authentication state\n      await authService.logout();\n\n      // Close any open dropdowns\n      setIsUserDropdownOpen(false);\n      setIsOrgDropdownOpen(false);\n      setIsProjectDropdownOpen(false);\n      setIsMobileMenuOpen(false);\n\n      // Redirect to login page\n      navigate('/login', {\n        replace: true\n      });\n    } catch (error) {\n      console.error('Error during logout:', error);\n      // Even if logout fails, redirect to login page\n      navigate('/login', {\n        replace: true\n      });\n    }\n  };\n\n  // Don't render header on auth pages\n  if (location.pathname === '/login' || location.pathname === '/register') {\n    return null;\n  }\n\n  // Get role badge color\n  const getRoleBadgeColor = role => {\n    const colors = {\n      viewer: 'bg-gray-100 text-gray-800',\n      member: 'bg-blue-100 text-blue-800',\n      admin: 'bg-purple-100 text-purple-800',\n      owner: 'bg-green-100 text-green-800'\n    };\n    return colors[role.toLowerCase()] || colors.viewer;\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"fixed top-0 left-0 right-0 z-1000 bg-surface border-b border-border shadow-enterprise\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between h-16 px-4 lg:px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/role-based-dashboard\",\n          className: \"flex items-center space-x-3 hover-lift\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-primary rounded-md flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              viewBox: \"0 0 24 24\",\n              className: \"w-5 h-5 text-primary-foreground\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl font-semibold text-text-primary\",\n            children: \"Agno WorkSphere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden lg:flex items-center space-x-6\",\n        children: [roleFeatures.canSwitchOrganizations ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: orgDropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"ghost\",\n            onClick: () => setIsOrgDropdownOpen(!isOrgDropdownOpen),\n            className: \"flex items-center space-x-2 px-3 py-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-6 h-6 bg-muted rounded-sm flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs font-medium text-text-primary\",\n                children: (organization === null || organization === void 0 ? void 0 : (_organization$name = organization.name) === null || _organization$name === void 0 ? void 0 : _organization$name.charAt(0)) || 'O'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-text-primary\",\n              children: (organization === null || organization === void 0 ? void 0 : organization.name) || 'Organization'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"ChevronDown\",\n              size: 16,\n              className: \"text-text-secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 15\n          }, this), isOrgDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-full left-0 mt-1 w-64 bg-popover border border-border rounded-md shadow-elevated z-1010\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium text-text-secondary uppercase tracking-wide px-2 py-1\",\n                children: \"Switch Organization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 21\n              }, this), userRole === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCreateOrganization,\n                  className: \"w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left border-b border-border mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-primary rounded-sm flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Plus\",\n                      size: 16,\n                      className: \"text-primary-foreground\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 700,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-primary\",\n                      children: \"Create New Organization\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-text-secondary\",\n                      children: \"Start a new organization\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 704,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false), availableOrganizations.map(org => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleOrganizationSwitch(org.id),\n                className: \"w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-muted rounded-sm flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs font-medium text-text-primary\",\n                    children: org.name.charAt(0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-text-primary\",\n                    children: org.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-text-secondary\",\n                    children: org.domain\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs px-2 py-1 rounded ${getRoleBadgeColor(org.role)}`,\n                  children: org.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 23\n                }, this), org.id === 1 && /*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"Check\",\n                  size: 16,\n                  className: \"text-success\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 25\n                }, this)]\n              }, org.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        // For members and viewers - show organization name without dropdown\n        _jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 px-3 py-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 bg-muted rounded-sm flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-medium text-text-primary\",\n              children: (organization === null || organization === void 0 ? void 0 : (_organization$name2 = organization.name) === null || _organization$name2 === void 0 ? void 0 : _organization$name2.charAt(0)) || 'O'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium text-text-primary\",\n            children: (organization === null || organization === void 0 ? void 0 : organization.name) || 'Organization'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 13\n        }, this), currentProject && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 px-3 py-2 border-l border-border\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-5 h-5 bg-primary/10 rounded-sm flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Folder\",\n              size: 12,\n              className: \"text-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-text-secondary\",\n              children: \"Project\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-text-primary\",\n              children: currentProject.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 751,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex items-center space-x-1\",\n          children: navigationItems.map(item => {\n            // Special handling for Projects item - make it a dropdown\n            if (item.label === 'Projects') {\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                ref: projectDropdownRef,\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"ghost\",\n                  onClick: () => setIsProjectDropdownOpen(!isProjectDropdownOpen),\n                  className: `flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${isActivePath(item.path) ? 'bg-primary text-primary-foreground' : 'text-text-secondary hover:text-text-primary hover:bg-muted'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: item.icon,\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: item.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"ChevronDown\",\n                    size: 12,\n                    className: \"ml-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 21\n                }, this), isProjectDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-full left-0 mt-1 w-80 bg-popover border border-border rounded-md shadow-elevated z-1010\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs font-medium text-text-secondary uppercase tracking-wide px-2 py-1\",\n                      children: \"Switch Project\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 786,\n                      columnNumber: 27\n                    }, this), (userRole === 'admin' || userRole === 'owner') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: handleCreateProject,\n                        className: \"w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left border-b border-border mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-primary rounded-sm flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Icon, {\n                            name: \"Plus\",\n                            size: 16,\n                            className: \"text-primary-foreground\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 798,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 797,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm font-medium text-primary\",\n                            children: \"Create New Project\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 801,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-text-secondary\",\n                            children: \"Start a new project\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 802,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 800,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 793,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false), availableProjects.map(project => /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleProjectSwitch(project),\n                      className: \"w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-muted rounded-sm flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(Icon, {\n                          name: \"Folder\",\n                          size: 16,\n                          className: \"text-text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 816,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 815,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm font-medium text-text-primary\",\n                          children: project.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 819,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs text-text-secondary truncate\",\n                          children: project.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 820,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 818,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col items-end\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `text-xs px-2 py-1 rounded ${project.status === 'active' ? 'bg-success/10 text-success' : project.status === 'planning' ? 'bg-warning/10 text-warning' : 'bg-muted text-text-secondary'}`,\n                          children: project.status\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 823,\n                          columnNumber: 33\n                        }, this), userRole === 'member' && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-text-secondary mt-1\",\n                          children: \"Assigned\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 831,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 822,\n                        columnNumber: 31\n                      }, this), project.id === currentProject.id && /*#__PURE__*/_jsxDEV(Icon, {\n                        name: \"Check\",\n                        size: 16,\n                        className: \"text-success\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 33\n                      }, this)]\n                    }, project.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 810,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 23\n                }, this)]\n              }, item.path, true, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this);\n            }\n\n            // Regular navigation items\n            return /*#__PURE__*/_jsxDEV(Link, {\n              to: item.path,\n              className: `flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${isActivePath(item.path) ? 'bg-primary text-primary-foreground' : 'text-text-secondary hover:text-text-primary hover:bg-muted'}`,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: item.icon,\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 19\n              }, this)]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: notificationDropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"ghost\",\n            size: \"icon\",\n            onClick: () => setIsNotificationDropdownOpen(!isNotificationDropdownOpen),\n            className: \"relative h-9 w-9\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Bell\",\n              size: 18,\n              className: \"text-text-secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 15\n            }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute -top-1 -right-1 h-5 w-5 bg-destructive text-destructive-foreground text-xs font-medium rounded-full flex items-center justify-center\",\n              children: unreadCount > 9 ? '9+' : unreadCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 13\n          }, this), isNotificationDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-full right-0 mt-1 w-96 bg-popover border border-border rounded-md shadow-elevated z-1010 md:w-80\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-border\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-text-primary flex items-center\",\n                  children: [\"Notifications\", unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 bg-destructive text-destructive-foreground text-xs px-2 py-1 rounded-full\",\n                    children: unreadCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"ghost\",\n                  size: \"sm\",\n                  onClick: markAllAsRead,\n                  className: \"text-xs text-primary hover:text-primary\",\n                  children: \"Mark all read\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setNotificationFilter('all'),\n                  className: `px-3 py-1 text-xs rounded-full transition-colors ${notificationFilter === 'all' ? 'bg-primary text-primary-foreground' : 'bg-muted text-text-secondary hover:bg-muted/80'}`,\n                  children: [\"All \", notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: notifications.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 918,\n                    columnNumber: 56\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setNotificationFilter('unread'),\n                  className: `px-3 py-1 text-xs rounded-full transition-colors ${notificationFilter === 'unread' ? 'bg-primary text-primary-foreground' : 'bg-muted text-text-secondary hover:bg-muted/80'}`,\n                  children: [\"Unread \", unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: unreadCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 928,\n                    columnNumber: 50\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setNotificationFilter('high_priority'),\n                  className: `px-3 py-1 text-xs rounded-full transition-colors ${notificationFilter === 'high_priority' ? 'bg-primary text-primary-foreground' : 'bg-muted text-text-secondary hover:bg-muted/80'}`,\n                  children: [\"High Priority \", highPriorityCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: highPriorityCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 938,\n                    columnNumber: 63\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-96 overflow-y-auto\",\n              children: filteredNotifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 text-center text-text-secondary\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"Bell\",\n                  size: 32,\n                  className: \"mx-auto mb-3 opacity-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: \"No notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm mt-1\",\n                  children: notificationFilter === 'unread' ? 'All caught up!' : notificationFilter === 'high_priority' ? 'No high priority items' : 'You\\'re all set'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 21\n              }, this) : getRoleBasedNotifications(filteredNotifications, userRole).map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 border-b border-border last:border-b-0 hover:bg-muted/30 transition-colors ${!notification.isRead ? 'bg-primary/5 border-l-4 border-l-primary' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-2 rounded-full flex-shrink-0 ${notification.type === 'task_assignment' ? 'bg-blue-100 text-blue-600' : notification.type === 'meeting_invite' ? 'bg-green-100 text-green-600' : notification.type === 'meeting_reminder' ? 'bg-orange-100 text-orange-600' : notification.type === 'ai_suggestion' ? 'bg-purple-100 text-purple-600' : notification.type === 'project_update' ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      name: getNotificationIcon(notification.type),\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 973,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start justify-between mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: `text-sm truncate ${!notification.isRead ? 'font-semibold text-text-primary' : 'font-medium text-text-primary'}`,\n                            children: notification.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 981,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `text-xs px-1.5 py-0.5 rounded ${getPriorityColor(notification.priority)} bg-muted`,\n                            children: notification.priority\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 984,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 980,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-text-secondary text-xs mt-1\",\n                          children: formatTimestamp(notification.timestamp)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 988,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 979,\n                        columnNumber: 31\n                      }, this), !notification.isRead && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2 mt-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 993,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 978,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-text-secondary text-sm mb-3 line-clamp-2\",\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 998,\n                      columnNumber: 29\n                    }, this), notification.type === 'task_assignment' && notification.data && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3 p-2 bg-muted/50 rounded-md\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                          src: notification.data.assignerAvatar,\n                          alt: notification.data.assignerName,\n                          className: \"w-5 h-5 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1006,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-text-secondary\",\n                          children: [\"Assigned by \", notification.data.assignerName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1011,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1005,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-text-secondary\",\n                        children: [\"Project: \", notification.data.projectName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1015,\n                        columnNumber: 33\n                      }, this), notification.data.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-text-secondary\",\n                        children: [\"Due: \", notification.data.dueDate.toLocaleDateString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1019,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1004,\n                      columnNumber: 31\n                    }, this), notification.type === 'meeting_invite' && notification.data && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3 p-2 bg-muted/50 rounded-md\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-2 mb-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                          src: notification.data.organizerAvatar,\n                          alt: notification.data.organizer,\n                          className: \"w-5 h-5 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1029,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-text-secondary\",\n                          children: [\"Organized by \", notification.data.organizer]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1034,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1028,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-text-secondary\",\n                        children: [notification.data.startTime.toLocaleDateString(), \" at \", notification.data.startTime.toLocaleTimeString([], {\n                          hour: '2-digit',\n                          minute: '2-digit'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1038,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-text-secondary\",\n                        children: [\"Duration: \", notification.data.duration, \" minutes\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1041,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1027,\n                      columnNumber: 31\n                    }, this), notification.actions && notification.actions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-wrap gap-2\",\n                      children: notification.actions.map((action, index) => /*#__PURE__*/_jsxDEV(Button, {\n                        variant: action.variant === 'primary' ? 'default' : action.variant === 'danger' ? 'destructive' : 'outline',\n                        size: \"sm\",\n                        onClick: e => {\n                          e.stopPropagation();\n                          handleNotificationAction(notification.id, action.action, notification.data);\n                        },\n                        disabled: isLoading,\n                        className: \"text-xs h-7\",\n                        children: action.label\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1051,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1049,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 25\n                }, this)\n              }, notification.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-border\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                className: \"w-full text-primary hover:text-primary\",\n                onClick: () => {\n                  setIsNotificationDropdownOpen(false);\n                  // Navigate to full notifications page if it exists\n                  console.log('Navigate to full notifications page');\n                },\n                children: \"View all notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: userDropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"ghost\",\n            onClick: () => setIsUserDropdownOpen(!isUserDropdownOpen),\n            className: \"flex items-center space-x-2 px-2 py-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-primary-foreground\",\n                children: user !== null && user !== void 0 && user.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1101,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-medium text-text-primary\",\n                children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-text-secondary\",\n                children: (user === null || user === void 0 ? void 0 : user.role) || 'Member'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"ChevronDown\",\n              size: 16,\n              className: \"text-text-secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1095,\n            columnNumber: 13\n          }, this), isUserDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-full right-0 mt-1 w-56 bg-popover border border-border rounded-md shadow-elevated z-1010\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-2 py-2 border-b border-border\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-text-primary\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1116,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-text-secondary\",\n                  children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1117,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block text-xs px-2 py-1 rounded mt-1 ${getRoleBadgeColor(userRole)}`,\n                  children: userRole.charAt(0).toUpperCase() + userRole.slice(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1118,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-1\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/user-profile-settings\",\n                  className: \"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"User\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1127,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Profile Settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1128,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1123,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Bell\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1131,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1132,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1130,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"HelpCircle\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1135,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Help & Support\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1136,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1134,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-t border-border my-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"w-full flex items-center space-x-2 px-2 py-2 text-sm text-destructive hover:bg-muted rounded-sm transition-micro\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"LogOut\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1143,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Sign Out\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1144,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1139,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1114,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1113,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1094,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"ghost\",\n          size: \"icon\",\n          onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n          className: \"lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: isMobileMenuOpen ? \"X\" : \"Menu\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 866,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: mobileMenuRef,\n      className: \"lg:hidden border-t border-border bg-surface\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-2 space-y-1\",\n        children: [userRole === 'owner' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            handleCreateOrganization();\n            setIsMobileMenuOpen(false);\n          },\n          className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-micro mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Building2\",\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1177,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"New Organization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1178,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 15\n        }, this), roleFeatures.canCreateProjects && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            navigate('/project-management');\n            setIsMobileMenuOpen(false);\n          },\n          className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-micro\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Plus\",\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1191,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"New Project\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1192,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1184,\n          columnNumber: 15\n        }, this), navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          onClick: () => setIsMobileMenuOpen(false),\n          className: `flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${isActivePath(item.path) ? 'bg-primary text-primary-foreground' : 'text-text-secondary hover:text-text-primary hover:bg-muted'}`,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: item.icon,\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1207,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1208,\n            columnNumber: 17\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1197,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1167,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1166,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CreateProjectModal, {\n      isOpen: isCreateProjectModalOpen,\n      onClose: () => setIsCreateProjectModalOpen(false),\n      onCreateProject: handleCreateProjectSubmit,\n      organizationId: organization.id || 1,\n      organizationName: organization.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateOrganizationModal, {\n      isOpen: isCreateOrganizationModalOpen,\n      onClose: () => setIsCreateOrganizationModalOpen(false),\n      onCreateOrganization: handleCreateOrganizationSubmit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1225,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 653,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleBasedHeader, \"pjQLECIIx+QguoKpnSFA4FTiOhI=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = RoleBasedHeader;\nexport default RoleBasedHeader;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedHeader\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Link", "useLocation", "useNavigate", "Icon", "<PERSON><PERSON>", "authService", "notificationService", "CreateProjectModal", "CreateOrganizationModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoleBasedHeader", "userRole", "currentUser", "propCurrentUser", "currentOrganization", "propCurrentOrganization", "_s", "_organization$name", "_organization$name2", "isOrgDropdownOpen", "setIsOrgDropdownOpen", "isProjectDropdownOpen", "setIsProjectDropdownOpen", "isUserDropdownOpen", "setIsUserDropdownOpen", "isMobileMenuOpen", "setIsMobileMenuOpen", "isNotificationDropdownOpen", "setIsNotificationDropdownOpen", "isCreateProjectModalOpen", "setIsCreateProjectModalOpen", "isCreateOrganizationModalOpen", "setIsCreateOrganizationModalOpen", "location", "navigate", "orgDropdownRef", "projectDropdownRef", "userDropdownRef", "mobileMenuRef", "notificationDropdownRef", "setCurrentUser", "name", "email", "avatar", "role", "organization", "setOrganization", "domain", "logo", "availableOrganizations", "setAvailableOrganizations", "loadUserAndOrganizationData", "result", "getCurrentUser", "console", "log", "data", "user", "userData", "userName", "firstName", "lastName", "trim", "split", "profilePicture", "organizations", "length", "userOrgs", "map", "org", "_org$organization", "_org$organization2", "_org$organization3", "id", "currentOrgId", "getOrganizationId", "currentOrg", "find", "warn", "error", "_error$message", "_error$message2", "message", "includes", "currentProject", "setCurrentProject", "availableProjects", "setAvailableProjects", "loadProjects", "default", "apiService", "projects", "getAll", "transformedProjects", "project", "description", "status", "savedProjectId", "localStorage", "getItem", "savedProject", "p", "setItem", "getNavigationItems", "baseItems", "label", "path", "icon", "roles", "adminItems", "ownerItems", "allItems", "filter", "item", "toLowerCase", "getRoleFeatures", "features", "viewer", "canCreateProjects", "canInviteMembers", "canManageSettings", "canViewAnalytics", "canManageBilling", "canSwitchOrganizations", "member", "admin", "owner", "navigationItems", "roleFeatures", "notifications", "setNotifications", "loadNotifications", "realNotifications", "getNotifications", "limit", "notificationFilter", "setNotificationFilter", "isLoading", "setIsLoading", "unreadCount", "n", "isRead", "highPriorityCount", "priority", "filteredNotifications", "notification", "mark<PERSON><PERSON><PERSON>", "notificationId", "prev", "markAllAsRead", "getNotificationIcon", "type", "getPriorityColor", "formatTimestamp", "timestamp", "now", "Date", "diff", "minutes", "Math", "floor", "hours", "days", "toLocaleDateString", "getRoleBasedNotifications", "handleNotificationAction", "action", "notificationData", "taskId", "state", "projectId", "highlightTaskId", "taskAccepted", "reason", "prompt", "meetingId", "window", "open", "suggestionType", "isActivePath", "pathname", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleOrganizationSwitch", "orgId", "<PERSON><PERSON><PERSON>", "reload", "handleProjectSwitch", "handleCreateProject", "handleCreateProjectSubmit", "projectData", "newProject", "memberRole", "handleCreateOrganization", "handleCreateOrganizationSubmit", "organizationData", "logoFile", "newOrganization", "logo_url", "URL", "createObjectURL", "handleLogout", "logout", "replace", "getRoleBadgeColor", "colors", "className", "children", "to", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "variant", "onClick", "char<PERSON>t", "size", "title", "src", "assignerAvatar", "alt", "assignerName", "projectName", "dueDate", "organizer<PERSON><PERSON><PERSON>", "organizer", "startTime", "toLocaleTimeString", "hour", "minute", "duration", "actions", "index", "e", "stopPropagation", "disabled", "join", "toUpperCase", "slice", "isOpen", "onClose", "onCreateProject", "organizationId", "organizationName", "onCreateOrganization", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/ui/RoleBasedHeader.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport Icon from '../AppIcon';\nimport Button from './Button';\nimport authService from '../../utils/authService';\nimport * as notificationService from '../../utils/notificationService';\nimport CreateProjectModal from '../modals/CreateProjectModal';\nimport CreateOrganizationModal from '../modals/CreateOrganizationModal';\n\nconst RoleBasedHeader = ({ userRole = 'member', currentUser: propCurrentUser, currentOrganization: propCurrentOrganization }) => {\n  const [isOrgDropdownOpen, setIsOrgDropdownOpen] = useState(false);\n  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = useState(false);\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] = useState(false);\n  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState(false);\n  const [isCreateOrganizationModalOpen, setIsCreateOrganizationModalOpen] = useState(false);\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  const orgDropdownRef = useRef(null);\n  const projectDropdownRef = useRef(null);\n  const userDropdownRef = useRef(null);\n  const mobileMenuRef = useRef(null);\n  const notificationDropdownRef = useRef(null);\n\n  // State for real user and organization data\n  const [currentUser, setCurrentUser] = useState(propCurrentUser || {\n    name: 'Loading...',\n    email: '',\n    avatar: '/assets/images/avatar.jpg',\n    role: userRole\n  });\n\n  const [organization, setOrganization] = useState(propCurrentOrganization || {\n    name: 'Loading...',\n    domain: '',\n    logo: '/assets/images/org-logo.png'\n  });\n\n  const [availableOrganizations, setAvailableOrganizations] = useState([]);\n\n  // Load real user and organization data\n  useEffect(() => {\n    const loadUserAndOrganizationData = async () => {\n      try {\n        const result = await authService.getCurrentUser();\n        console.log('Auth service result:', result); // Debug log\n\n        if (result.data && result.data.user && result.data.user.email) {\n          const userData = result.data.user;\n\n          // Construct user name with better fallback logic\n          let userName = '';\n          if (userData.firstName || userData.lastName) {\n            userName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim();\n          }\n          if (!userName && userData.email) {\n            userName = userData.email.split('@')[0];\n          }\n          if (!userName) {\n            userName = 'User';\n          }\n\n          console.log('User data loaded:', { userData, userName }); // Debug log\n\n          setCurrentUser({\n            name: userName,\n            email: userData.email || '',\n            avatar: userData.avatar || userData.profilePicture || '/assets/images/avatar.jpg',\n            role: userData.role || userRole\n          });\n\n          // Load organizations\n          if (result.data.organizations && result.data.organizations.length > 0) {\n            const userOrgs = result.data.organizations.map(org => ({\n              id: org.id || org.organization?.id,\n              name: org.name || org.organization?.name,\n              domain: org.domain || org.organization?.domain,\n              role: org.role\n            }));\n\n            setAvailableOrganizations(userOrgs);\n\n            // Set current organization\n            const currentOrgId = authService.getOrganizationId();\n            const currentOrg = userOrgs.find(org => org.id === currentOrgId) || userOrgs[0];\n\n            if (currentOrg) {\n              setOrganization({\n                id: currentOrg.id,\n                name: currentOrg.name || 'Organization',\n                domain: currentOrg.domain || '',\n                logo: '/assets/images/org-logo.png'\n              });\n            }\n          }\n        } else {\n          console.warn('No user data received from API');\n          console.log('Full API result:', result);\n          // Only set fallback if we truly have no data\n        }\n      } catch (error) {\n        console.error('Failed to load user and organization data:', error);\n\n        // Only set fallback data if we truly can't get user data\n        // Check if it's an authentication error\n        if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {\n          console.warn('User not authenticated, using fallback data');\n\n          // Set fallback user data to prevent crashes\n          setCurrentUser({\n            name: 'User',\n            email: '<EMAIL>',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          });\n\n          // Set fallback organization\n          setOrganization({\n            id: 'fallback-org',\n            name: 'Organization',\n            domain: 'example.com',\n            logo: '/assets/images/org-logo.png'\n          });\n        } else {\n          // For other errors, log but don't override data\n          console.error('Non-auth error loading user data:', error);\n        }\n      }\n    };\n\n    // Only load if not provided via props\n    if (!propCurrentUser || !propCurrentOrganization) {\n      loadUserAndOrganizationData();\n    }\n  }, [propCurrentUser, propCurrentOrganization, userRole]);\n\n  // Update state when props change\n  useEffect(() => {\n    if (propCurrentUser) {\n      setCurrentUser(propCurrentUser);\n    }\n  }, [propCurrentUser]);\n\n  useEffect(() => {\n    if (propCurrentOrganization) {\n      setOrganization(propCurrentOrganization);\n    }\n  }, [propCurrentOrganization]);\n\n  // Use the current user data\n  const user = currentUser;\n\n  // Project state and data\n  const [currentProject, setCurrentProject] = useState(null);\n  const [availableProjects, setAvailableProjects] = useState([]);\n\n  // Load projects when organization changes\n  useEffect(() => {\n    const loadProjects = async () => {\n      if (!organization?.id) return;\n\n      try {\n        // Import apiService dynamically to avoid circular imports\n        const { default: apiService } = await import('../../utils/apiService');\n        const projects = await apiService.projects.getAll(organization.id);\n\n        const transformedProjects = (projects || []).map(project => ({\n          id: project.id,\n          name: project.name || 'Untitled Project',\n          description: project.description || '',\n          status: project.status || 'active'\n        }));\n\n        setAvailableProjects(transformedProjects);\n\n        // Set current project from localStorage or first project\n        const savedProjectId = localStorage.getItem('currentProjectId');\n        if (savedProjectId) {\n          const savedProject = transformedProjects.find(p => p.id === savedProjectId);\n          if (savedProject) {\n            setCurrentProject(savedProject);\n          }\n        } else if (transformedProjects.length > 0) {\n          setCurrentProject(transformedProjects[0]);\n          localStorage.setItem('currentProjectId', transformedProjects[0].id);\n        }\n      } catch (error) {\n        console.error('Failed to load projects:', error);\n        setAvailableProjects([]);\n      }\n    };\n\n    loadProjects();\n  }, [organization?.id]);\n\n  // Role-based navigation configuration\n  const getNavigationItems = (role) => {\n    const baseItems = [\n      { label: 'Dashboard', path: '/role-based-dashboard', icon: 'Home', roles: ['viewer', 'member', 'admin', 'owner'] },\n      { label: 'Projects', path: '/kanban-board', icon: 'Kanban', roles: ['viewer', 'member', 'admin', 'owner'] },\n      { label: 'Team Members', path: '/team-members', icon: 'Users', roles: ['member', 'admin', 'owner'] }\n    ];\n\n    const adminItems = [\n      // Organization settings moved to owner-only items\n    ];\n\n    const ownerItems = [\n      { label: 'Organization', path: '/organization-settings', icon: 'Settings', roles: ['owner'] },\n      { label: 'Analytics', path: '/analytics', icon: 'BarChart3', roles: ['owner'] },\n      { label: 'Billing', path: '/billing', icon: 'CreditCard', roles: ['owner'] }\n    ];\n\n    // Filter items based on user role\n    const allItems = [...baseItems, ...adminItems, ...ownerItems];\n    return allItems.filter(item => item.roles.includes(role.toLowerCase()));\n  };\n\n  // Get role-specific features\n  const getRoleFeatures = (role) => {\n    const features = {\n      viewer: {\n        canCreateProjects: false,\n        canInviteMembers: false,\n        canManageSettings: false,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: false\n      },\n      member: {\n        canCreateProjects: true,\n        canInviteMembers: false,\n        canManageSettings: false,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: false\n      },\n      admin: {\n        canCreateProjects: true,\n        canInviteMembers: true,\n        canManageSettings: true,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: true\n      },\n      owner: {\n        canCreateProjects: true,\n        canInviteMembers: true,\n        canManageSettings: true,\n        canViewAnalytics: true,\n        canManageBilling: true,\n        canSwitchOrganizations: true\n      }\n    };\n    return features[role.toLowerCase()] || features.viewer;\n  };\n\n  const navigationItems = getNavigationItems(userRole);\n  const roleFeatures = getRoleFeatures(userRole);\n\n  // Enhanced notification data with comprehensive structure\n  const [notifications, setNotifications] = useState([]); // Real notifications loaded from API\n\n  // Mock notifications removed - now using real API data\n\n  // Load real notifications from API\n  useEffect(() => {\n    const loadNotifications = async () => {\n      try {\n        const realNotifications = await notificationService.getNotifications({ limit: 10 });\n        setNotifications(realNotifications || []);\n      } catch (error) {\n        console.error('Failed to load notifications:', error);\n        setNotifications([]);\n      }\n    };\n\n    loadNotifications();\n  }, []);\n\n  // Notification state and filtering\n  const [notificationFilter, setNotificationFilter] = useState('all'); // 'all', 'unread', 'high_priority'\n  const [isLoading, setIsLoading] = useState(false);\n\n  const unreadCount = notifications.filter(n => !n.isRead).length;\n  const highPriorityCount = notifications.filter(n => n.priority === 'high' && !n.isRead).length;\n\n  // Filter notifications based on current filter\n  const filteredNotifications = notifications.filter(notification => {\n    switch (notificationFilter) {\n      case 'unread':\n        return !notification.isRead;\n      case 'high_priority':\n        return notification.priority === 'high';\n      default:\n        return true;\n    }\n  });\n\n  const markAsRead = (notificationId) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === notificationId\n          ? { ...notification, isRead: true }\n          : notification\n      )\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, isRead: true }))\n    );\n  };\n\n  const getNotificationIcon = (type) => {\n    switch (type) {\n      case 'task_assignment': return 'UserPlus';\n      case 'meeting_invite': return 'Calendar';\n      case 'meeting_reminder': return 'Clock';\n      case 'ai_suggestion': return 'Zap';\n      case 'project_update': return 'AlertTriangle';\n      default: return 'Bell';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high': return 'text-destructive';\n      case 'medium': return 'text-warning';\n      case 'low': return 'text-muted-foreground';\n      default: return 'text-muted-foreground';\n    }\n  };\n\n  const formatTimestamp = (timestamp) => {\n    const now = new Date();\n    const diff = now - timestamp;\n    const minutes = Math.floor(diff / (1000 * 60));\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n\n    if (minutes < 1) return 'Just now';\n    if (minutes < 60) return `${minutes}m ago`;\n    if (hours < 24) return `${hours}h ago`;\n    if (days < 7) return `${days}d ago`;\n    return timestamp.toLocaleDateString();\n  };\n\n  // Role-based notification filtering\n  const getRoleBasedNotifications = (notifications, userRole) => {\n    return notifications.filter(notification => {\n      switch (notification.type) {\n        case 'meeting_invite':\n          // All roles can receive meeting invites\n          return true;\n        case 'ai_suggestion':\n          // AI suggestions available to all roles\n          return true;\n        case 'task_assignment':\n          // Task assignments for members and above only\n          // Viewers cannot receive task assignments in this system\n          return ['member', 'admin', 'owner'].includes(userRole?.toLowerCase());\n        case 'project_update':\n          // Project updates for all roles\n          return true;\n        default:\n          return true;\n      }\n    });\n  };\n\n  // Notification action handlers\n  const handleNotificationAction = async (notificationId, action, notificationData) => {\n    setIsLoading(true);\n\n    try {\n      switch (action) {\n        case 'accept_task':\n          // Accept task assignment\n          console.log('Accepting task:', notificationData.taskId);\n          // Navigate to kanban board with task highlighted\n          navigate('/kanban-board', {\n            state: {\n              projectId: notificationData.projectId,\n              highlightTaskId: notificationData.taskId,\n              taskAccepted: true\n            }\n          });\n          // Remove notification after acceptance\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n\n        case 'view_task':\n          // View task details\n          navigate('/kanban-board', {\n            state: {\n              projectId: notificationData.projectId,\n              highlightTaskId: notificationData.taskId\n            }\n          });\n          markAsRead(notificationId);\n          break;\n\n        case 'decline_task':\n          // Decline task assignment\n          const reason = prompt('Please provide a reason for declining this task (optional):');\n          console.log('Declining task:', notificationData.taskId, 'Reason:', reason);\n          // Remove notification after declining\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n\n        case 'accept_meeting':\n          // Accept meeting invitation\n          console.log('Accepting meeting:', notificationData.meetingId);\n          // Update meeting status and remove notification\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n\n        case 'decline_meeting':\n          // Decline meeting invitation\n          console.log('Declining meeting:', notificationData.meetingId);\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n\n        case 'view_meeting':\n          // View meeting details\n          console.log('Viewing meeting details:', notificationData.meetingId);\n          markAsRead(notificationId);\n          break;\n\n        case 'join_meeting':\n          // Join meeting (open meeting link)\n          console.log('Joining meeting:', notificationData.meetingId);\n          window.open('https://zoom.us/j/meeting-room', '_blank');\n          markAsRead(notificationId);\n          break;\n\n        case 'apply_ai_suggestion':\n          // Apply AI suggestion\n          console.log('Applying AI suggestion:', notificationData.suggestionType);\n          markAsRead(notificationId);\n          break;\n\n        case 'view_project':\n          // View project details\n          navigate('/project-overview', {\n            state: {\n              projectId: notificationData.projectId\n            }\n          });\n          markAsRead(notificationId);\n          break;\n\n        default:\n          console.log('Unknown action:', action);\n          markAsRead(notificationId);\n      }\n    } catch (error) {\n      console.error('Error handling notification action:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isActivePath = (path) => location.pathname === path;\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target)) {\n        setIsOrgDropdownOpen(false);\n      }\n      if (projectDropdownRef.current && !projectDropdownRef.current.contains(event.target)) {\n        setIsProjectDropdownOpen(false);\n      }\n      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {\n        setIsUserDropdownOpen(false);\n      }\n      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {\n        setIsMobileMenuOpen(false);\n      }\n      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target)) {\n        setIsNotificationDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleOrganizationSwitch = async (orgId) => {\n    try {\n      console.log('Switching to organization:', orgId);\n\n      // Find the organization in available organizations\n      const selectedOrg = availableOrganizations.find(org => org.id === orgId);\n\n      if (selectedOrg) {\n        // Update local storage\n        localStorage.setItem('organizationId', orgId);\n        localStorage.setItem('userRole', selectedOrg.role);\n\n        // Update state\n        setOrganization({\n          id: selectedOrg.id,\n          name: selectedOrg.name,\n          domain: selectedOrg.domain,\n          logo: '/assets/images/org-logo.png'\n        });\n\n        // Refresh the page to load data for the new organization\n        window.location.reload();\n      }\n\n      setIsOrgDropdownOpen(false);\n    } catch (error) {\n      console.error('Failed to switch organization:', error);\n      setIsOrgDropdownOpen(false);\n    }\n  };\n\n  const handleProjectSwitch = (project) => {\n    console.log('Switching to project:', project);\n    setCurrentProject(project);\n    setIsProjectDropdownOpen(false);\n\n    // Save selected project to localStorage\n    localStorage.setItem('currentProjectId', project.id);\n\n    // Navigate to the project overview page with project ID in URL\n    navigate(`/project-overview?id=${project.id}`, {\n      state: {\n        projectId: project.id,\n        project: project\n      }\n    });\n  };\n\n  const handleCreateProject = () => {\n    console.log('Creating new project...');\n    setIsProjectDropdownOpen(false);\n    setIsCreateProjectModalOpen(true);\n  };\n\n  const handleCreateProjectSubmit = async (projectData) => {\n    try {\n      console.log('Creating project:', projectData);\n      // Here you would typically call an API to create the project\n      // For now, we'll just add it to the available projects\n      const newProject = {\n        id: Date.now(),\n        name: projectData.name,\n        description: projectData.description,\n        status: 'active',\n        memberRole: 'assigned'\n      };\n\n      // Update current project to the newly created one\n      setCurrentProject(newProject);\n      setIsCreateProjectModalOpen(false);\n\n      // Navigate to the new project's overview page\n      navigate(`/project-overview?id=${newProject.id}`, {\n        state: {\n          projectId: newProject.id,\n          project: newProject\n        }\n      });\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      throw error;\n    }\n  };\n\n  const handleCreateOrganization = () => {\n    console.log('Creating new organization...');\n    setIsOrgDropdownOpen(false);\n    setIsCreateOrganizationModalOpen(true);\n  };\n\n  const handleCreateOrganizationSubmit = async (organizationData, logoFile) => {\n    try {\n      console.log('Creating organization:', organizationData);\n      // Here you would typically call an API to create the organization\n      // For now, we'll just simulate the creation\n      const newOrganization = {\n        id: Date.now(),\n        name: organizationData.name,\n        description: organizationData.description,\n        logo_url: logoFile ? URL.createObjectURL(logoFile) : null,\n        ...organizationData\n      };\n\n      setIsCreateOrganizationModalOpen(false);\n\n      // Show success message and potentially navigate to organization settings\n      console.log('Organization created successfully:', newOrganization);\n      // You might want to refresh the organization list or navigate somewhere\n      navigate('/organization-settings', {\n        state: {\n          message: `Organization \"${organizationData.name}\" created successfully!`,\n          type: 'success'\n        }\n      });\n    } catch (error) {\n      console.error('Failed to create organization:', error);\n      throw error;\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      console.log('Logging out...');\n\n      // Clear authentication state\n      await authService.logout();\n\n      // Close any open dropdowns\n      setIsUserDropdownOpen(false);\n      setIsOrgDropdownOpen(false);\n      setIsProjectDropdownOpen(false);\n      setIsMobileMenuOpen(false);\n\n      // Redirect to login page\n      navigate('/login', { replace: true });\n    } catch (error) {\n      console.error('Error during logout:', error);\n      // Even if logout fails, redirect to login page\n      navigate('/login', { replace: true });\n    }\n  };\n\n  // Don't render header on auth pages\n  if (location.pathname === '/login' || location.pathname === '/register') {\n    return null;\n  }\n\n  // Get role badge color\n  const getRoleBadgeColor = (role) => {\n    const colors = {\n      viewer: 'bg-gray-100 text-gray-800',\n      member: 'bg-blue-100 text-blue-800',\n      admin: 'bg-purple-100 text-purple-800',\n      owner: 'bg-green-100 text-green-800'\n    };\n    return colors[role.toLowerCase()] || colors.viewer;\n  };\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-1000 bg-surface border-b border-border shadow-enterprise\">\n      <div className=\"flex items-center justify-between h-16 px-4 lg:px-6\">\n        {/* Logo */}\n        <div className=\"flex items-center\">\n          <Link to=\"/role-based-dashboard\" className=\"flex items-center space-x-3 hover-lift\">\n            <div className=\"w-8 h-8 bg-primary rounded-md flex items-center justify-center\">\n              <svg viewBox=\"0 0 24 24\" className=\"w-5 h-5 text-primary-foreground\" fill=\"currentColor\">\n                <path d=\"M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z\"/>\n              </svg>\n            </div>\n            <span className=\"text-xl font-semibold text-text-primary\">Agno WorkSphere</span>\n          </Link>\n        </div>\n\n        {/* Organization Context Switcher - Desktop */}\n        <div className=\"hidden lg:flex items-center space-x-6\">\n          {roleFeatures.canSwitchOrganizations ? (\n            <div className=\"relative\" ref={orgDropdownRef}>\n              <Button\n                variant=\"ghost\"\n                onClick={() => setIsOrgDropdownOpen(!isOrgDropdownOpen)}\n                className=\"flex items-center space-x-2 px-3 py-2\"\n              >\n                <div className=\"w-6 h-6 bg-muted rounded-sm flex items-center justify-center\">\n                  <span className=\"text-xs font-medium text-text-primary\">\n                    {organization?.name?.charAt(0) || 'O'}\n                  </span>\n                </div>\n                <span className=\"font-medium text-text-primary\">{organization?.name || 'Organization'}</span>\n                <Icon name=\"ChevronDown\" size={16} className=\"text-text-secondary\" />\n              </Button>\n\n              {isOrgDropdownOpen && (\n                <div className=\"absolute top-full left-0 mt-1 w-64 bg-popover border border-border rounded-md shadow-elevated z-1010\">\n                  <div className=\"p-2\">\n                    <div className=\"text-xs font-medium text-text-secondary uppercase tracking-wide px-2 py-1\">\n                      Switch Organization\n                    </div>\n\n                    {/* Create New Organization - Only for Owners */}\n                    {userRole === 'owner' && (\n                      <>\n                        <button\n                          onClick={handleCreateOrganization}\n                          className=\"w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left border-b border-border mb-2\"\n                        >\n                          <div className=\"w-8 h-8 bg-primary rounded-sm flex items-center justify-center\">\n                            <Icon name=\"Plus\" size={16} className=\"text-primary-foreground\" />\n                          </div>\n                          <div className=\"flex-1\">\n                            <div className=\"text-sm font-medium text-primary\">Create New Organization</div>\n                            <div className=\"text-xs text-text-secondary\">Start a new organization</div>\n                          </div>\n                        </button>\n                      </>\n                    )}\n\n                    {availableOrganizations.map((org) => (\n                      <button\n                        key={org.id}\n                        onClick={() => handleOrganizationSwitch(org.id)}\n                        className=\"w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left\"\n                      >\n                        <div className=\"w-8 h-8 bg-muted rounded-sm flex items-center justify-center\">\n                          <span className=\"text-xs font-medium text-text-primary\">\n                            {org.name.charAt(0)}\n                        </span>\n                      </div>\n                      <div className=\"flex-1\">\n                        <div className=\"text-sm font-medium text-text-primary\">{org.name}</div>\n                        <div className=\"text-xs text-text-secondary\">{org.domain}</div>\n                      </div>\n                      <span className={`text-xs px-2 py-1 rounded ${getRoleBadgeColor(org.role)}`}>\n                        {org.role}\n                      </span>\n                      {org.id === 1 && (\n                        <Icon name=\"Check\" size={16} className=\"text-success\" />\n                      )}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n            </div>\n          ) : (\n            // For members and viewers - show organization name without dropdown\n            <div className=\"flex items-center space-x-2 px-3 py-2\">\n              <div className=\"w-6 h-6 bg-muted rounded-sm flex items-center justify-center\">\n                <span className=\"text-xs font-medium text-text-primary\">\n                  {organization?.name?.charAt(0) || 'O'}\n                </span>\n              </div>\n              <span className=\"font-medium text-text-primary\">{organization?.name || 'Organization'}</span>\n            </div>\n          )}\n\n          {/* Current Project Display */}\n          {currentProject && (\n            <div className=\"flex items-center space-x-2 px-3 py-2 border-l border-border\">\n              <div className=\"w-5 h-5 bg-primary/10 rounded-sm flex items-center justify-center\">\n                <Icon name=\"Folder\" size={12} className=\"text-primary\" />\n              </div>\n              <div className=\"flex flex-col\">\n                <span className=\"text-xs text-text-secondary\">Project</span>\n                <span className=\"text-sm font-medium text-text-primary\">{currentProject.name}</span>\n              </div>\n            </div>\n          )}\n\n          {/* Role-Based Navigation */}\n          <nav className=\"flex items-center space-x-1\">\n            {navigationItems.map((item) => {\n              // Special handling for Projects item - make it a dropdown\n              if (item.label === 'Projects') {\n                return (\n                  <div key={item.path} className=\"relative\" ref={projectDropdownRef}>\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setIsProjectDropdownOpen(!isProjectDropdownOpen)}\n                      className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${\n                        isActivePath(item.path)\n                          ? 'bg-primary text-primary-foreground'\n                          : 'text-text-secondary hover:text-text-primary hover:bg-muted'\n                      }`}\n                    >\n                      <Icon name={item.icon} size={16} />\n                      <span>{item.label}</span>\n                      <Icon name=\"ChevronDown\" size={12} className=\"ml-1\" />\n                    </Button>\n\n                    {isProjectDropdownOpen && (\n                      <div className=\"absolute top-full left-0 mt-1 w-80 bg-popover border border-border rounded-md shadow-elevated z-1010\">\n                        <div className=\"p-2\">\n                          <div className=\"text-xs font-medium text-text-secondary uppercase tracking-wide px-2 py-1\">\n                            Switch Project\n                          </div>\n\n                          {/* Create New Project - Only for Admins and Owners */}\n                          {(userRole === 'admin' || userRole === 'owner') && (\n                            <>\n                              <button\n                                onClick={handleCreateProject}\n                                className=\"w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left border-b border-border mb-2\"\n                              >\n                                <div className=\"w-8 h-8 bg-primary rounded-sm flex items-center justify-center\">\n                                  <Icon name=\"Plus\" size={16} className=\"text-primary-foreground\" />\n                                </div>\n                                <div className=\"flex-1\">\n                                  <div className=\"text-sm font-medium text-primary\">Create New Project</div>\n                                  <div className=\"text-xs text-text-secondary\">Start a new project</div>\n                                </div>\n                              </button>\n                            </>\n                          )}\n\n                          {/* Available Projects */}\n                          {availableProjects.map((project) => (\n                            <button\n                              key={project.id}\n                              onClick={() => handleProjectSwitch(project)}\n                              className=\"w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left\"\n                            >\n                              <div className=\"w-8 h-8 bg-muted rounded-sm flex items-center justify-center\">\n                                <Icon name=\"Folder\" size={16} className=\"text-text-primary\" />\n                              </div>\n                              <div className=\"flex-1\">\n                                <div className=\"text-sm font-medium text-text-primary\">{project.name}</div>\n                                <div className=\"text-xs text-text-secondary truncate\">{project.description}</div>\n                              </div>\n                              <div className=\"flex flex-col items-end\">\n                                <span className={`text-xs px-2 py-1 rounded ${\n                                  project.status === 'active' ? 'bg-success/10 text-success' :\n                                  project.status === 'planning' ? 'bg-warning/10 text-warning' :\n                                  'bg-muted text-text-secondary'\n                                }`}>\n                                  {project.status}\n                                </span>\n                                {userRole === 'member' && (\n                                  <span className=\"text-xs text-text-secondary mt-1\">Assigned</span>\n                                )}\n                              </div>\n                              {project.id === currentProject.id && (\n                                <Icon name=\"Check\" size={16} className=\"text-success\" />\n                              )}\n                            </button>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                );\n              }\n\n              // Regular navigation items\n              return (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${\n                    isActivePath(item.path)\n                      ? 'bg-primary text-primary-foreground'\n                      : 'text-text-secondary hover:text-text-primary hover:bg-muted'\n                  }`}\n                >\n                  <Icon name={item.icon} size={16} />\n                  <span>{item.label}</span>\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* User Profile & Mobile Menu */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Notifications */}\n          <div className=\"relative\" ref={notificationDropdownRef}>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsNotificationDropdownOpen(!isNotificationDropdownOpen)}\n              className=\"relative h-9 w-9\"\n            >\n              <Icon name=\"Bell\" size={18} className=\"text-text-secondary\" />\n              {unreadCount > 0 && (\n                <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-destructive text-destructive-foreground text-xs font-medium rounded-full flex items-center justify-center\">\n                  {unreadCount > 9 ? '9+' : unreadCount}\n                </span>\n              )}\n            </Button>\n\n            {isNotificationDropdownOpen && (\n              <div className=\"absolute top-full right-0 mt-1 w-96 bg-popover border border-border rounded-md shadow-elevated z-1010 md:w-80\">\n                {/* Header with filters */}\n                <div className=\"p-4 border-b border-border\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <h3 className=\"font-semibold text-text-primary flex items-center\">\n                      Notifications\n                      {unreadCount > 0 && (\n                        <span className=\"ml-2 bg-destructive text-destructive-foreground text-xs px-2 py-1 rounded-full\">\n                          {unreadCount}\n                        </span>\n                      )}\n                    </h3>\n                    {unreadCount > 0 && (\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={markAllAsRead}\n                        className=\"text-xs text-primary hover:text-primary\"\n                      >\n                        Mark all read\n                      </Button>\n                    )}\n                  </div>\n\n                  {/* Filter tabs */}\n                  <div className=\"flex space-x-1\">\n                    <button\n                      onClick={() => setNotificationFilter('all')}\n                      className={`px-3 py-1 text-xs rounded-full transition-colors ${\n                        notificationFilter === 'all'\n                          ? 'bg-primary text-primary-foreground'\n                          : 'bg-muted text-text-secondary hover:bg-muted/80'\n                      }`}\n                    >\n                      All {notifications.length > 0 && <span className=\"ml-1\">{notifications.length}</span>}\n                    </button>\n                    <button\n                      onClick={() => setNotificationFilter('unread')}\n                      className={`px-3 py-1 text-xs rounded-full transition-colors ${\n                        notificationFilter === 'unread'\n                          ? 'bg-primary text-primary-foreground'\n                          : 'bg-muted text-text-secondary hover:bg-muted/80'\n                      }`}\n                    >\n                      Unread {unreadCount > 0 && <span className=\"ml-1\">{unreadCount}</span>}\n                    </button>\n                    <button\n                      onClick={() => setNotificationFilter('high_priority')}\n                      className={`px-3 py-1 text-xs rounded-full transition-colors ${\n                        notificationFilter === 'high_priority'\n                          ? 'bg-primary text-primary-foreground'\n                          : 'bg-muted text-text-secondary hover:bg-muted/80'\n                      }`}\n                    >\n                      High Priority {highPriorityCount > 0 && <span className=\"ml-1\">{highPriorityCount}</span>}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Notifications list */}\n                <div className=\"max-h-96 overflow-y-auto\">\n                  {filteredNotifications.length === 0 ? (\n                    <div className=\"p-6 text-center text-text-secondary\">\n                      <Icon name=\"Bell\" size={32} className=\"mx-auto mb-3 opacity-50\" />\n                      <p className=\"font-medium\">No notifications</p>\n                      <p className=\"text-sm mt-1\">\n                        {notificationFilter === 'unread' ? 'All caught up!' :\n                         notificationFilter === 'high_priority' ? 'No high priority items' :\n                         'You\\'re all set'}\n                      </p>\n                    </div>\n                  ) : (\n                    getRoleBasedNotifications(filteredNotifications, userRole).map((notification) => (\n                      <div\n                        key={notification.id}\n                        className={`p-4 border-b border-border last:border-b-0 hover:bg-muted/30 transition-colors ${\n                          !notification.isRead ? 'bg-primary/5 border-l-4 border-l-primary' : ''\n                        }`}\n                      >\n                        <div className=\"flex items-start space-x-3\">\n                          {/* Notification icon */}\n                          <div className={`p-2 rounded-full flex-shrink-0 ${\n                            notification.type === 'task_assignment' ? 'bg-blue-100 text-blue-600' :\n                            notification.type === 'meeting_invite' ? 'bg-green-100 text-green-600' :\n                            notification.type === 'meeting_reminder' ? 'bg-orange-100 text-orange-600' :\n                            notification.type === 'ai_suggestion' ? 'bg-purple-100 text-purple-600' :\n                            notification.type === 'project_update' ? 'bg-red-100 text-red-600' :\n                            'bg-gray-100 text-gray-600'\n                          }`}>\n                            <Icon name={getNotificationIcon(notification.type)} size={14} />\n                          </div>\n\n                          <div className=\"flex-1 min-w-0\">\n                            {/* Header */}\n                            <div className=\"flex items-start justify-between mb-2\">\n                              <div className=\"flex-1\">\n                                <div className=\"flex items-center space-x-2\">\n                                  <p className={`text-sm truncate ${!notification.isRead ? 'font-semibold text-text-primary' : 'font-medium text-text-primary'}`}>\n                                    {notification.title}\n                                  </p>\n                                  <span className={`text-xs px-1.5 py-0.5 rounded ${getPriorityColor(notification.priority)} bg-muted`}>\n                                    {notification.priority}\n                                  </span>\n                                </div>\n                                <p className=\"text-text-secondary text-xs mt-1\">\n                                  {formatTimestamp(notification.timestamp)}\n                                </p>\n                              </div>\n                              {!notification.isRead && (\n                                <div className=\"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2 mt-1\"></div>\n                              )}\n                            </div>\n\n                            {/* Message */}\n                            <p className=\"text-text-secondary text-sm mb-3 line-clamp-2\">\n                              {notification.message}\n                            </p>\n\n                            {/* Type-specific content */}\n                            {notification.type === 'task_assignment' && notification.data && (\n                              <div className=\"mb-3 p-2 bg-muted/50 rounded-md\">\n                                <div className=\"flex items-center space-x-2 mb-2\">\n                                  <img\n                                    src={notification.data.assignerAvatar}\n                                    alt={notification.data.assignerName}\n                                    className=\"w-5 h-5 rounded-full\"\n                                  />\n                                  <span className=\"text-xs text-text-secondary\">\n                                    Assigned by {notification.data.assignerName}\n                                  </span>\n                                </div>\n                                <p className=\"text-xs text-text-secondary\">\n                                  Project: {notification.data.projectName}\n                                </p>\n                                {notification.data.dueDate && (\n                                  <p className=\"text-xs text-text-secondary\">\n                                    Due: {notification.data.dueDate.toLocaleDateString()}\n                                  </p>\n                                )}\n                              </div>\n                            )}\n\n                            {notification.type === 'meeting_invite' && notification.data && (\n                              <div className=\"mb-3 p-2 bg-muted/50 rounded-md\">\n                                <div className=\"flex items-center space-x-2 mb-1\">\n                                  <img\n                                    src={notification.data.organizerAvatar}\n                                    alt={notification.data.organizer}\n                                    className=\"w-5 h-5 rounded-full\"\n                                  />\n                                  <span className=\"text-xs text-text-secondary\">\n                                    Organized by {notification.data.organizer}\n                                  </span>\n                                </div>\n                                <p className=\"text-xs text-text-secondary\">\n                                  {notification.data.startTime.toLocaleDateString()} at {notification.data.startTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}\n                                </p>\n                                <p className=\"text-xs text-text-secondary\">\n                                  Duration: {notification.data.duration} minutes\n                                </p>\n                              </div>\n                            )}\n\n                            {/* Action buttons */}\n                            {notification.actions && notification.actions.length > 0 && (\n                              <div className=\"flex flex-wrap gap-2\">\n                                {notification.actions.map((action, index) => (\n                                  <Button\n                                    key={index}\n                                    variant={action.variant === 'primary' ? 'default' : action.variant === 'danger' ? 'destructive' : 'outline'}\n                                    size=\"sm\"\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      handleNotificationAction(notification.id, action.action, notification.data);\n                                    }}\n                                    disabled={isLoading}\n                                    className=\"text-xs h-7\"\n                                  >\n                                    {action.label}\n                                  </Button>\n                                ))}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  )}\n                </div>\n\n                {/* Footer */}\n                <div className=\"p-3 border-t border-border\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"w-full text-primary hover:text-primary\"\n                    onClick={() => {\n                      setIsNotificationDropdownOpen(false);\n                      // Navigate to full notifications page if it exists\n                      console.log('Navigate to full notifications page');\n                    }}\n                  >\n                    View all notifications\n                  </Button>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* User Profile Dropdown */}\n          <div className=\"relative\" ref={userDropdownRef}>\n            <Button\n              variant=\"ghost\"\n              onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}\n              className=\"flex items-center space-x-2 px-2 py-2\"\n            >\n              <div className=\"w-8 h-8 bg-primary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-primary-foreground\">\n                  {user?.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'}\n                </span>\n              </div>\n              <div className=\"hidden md:block text-left\">\n                <div className=\"text-sm font-medium text-text-primary\">{user?.name || 'User'}</div>\n                <div className=\"text-xs text-text-secondary\">{user?.role || 'Member'}</div>\n              </div>\n              <Icon name=\"ChevronDown\" size={16} className=\"text-text-secondary\" />\n            </Button>\n\n            {isUserDropdownOpen && (\n              <div className=\"absolute top-full right-0 mt-1 w-56 bg-popover border border-border rounded-md shadow-elevated z-1010\">\n                <div className=\"p-2\">\n                  <div className=\"px-2 py-2 border-b border-border\">\n                    <div className=\"font-medium text-text-primary\">{user?.name || 'User'}</div>\n                    <div className=\"text-sm text-text-secondary\">{user?.email || '<EMAIL>'}</div>\n                    <span className={`inline-block text-xs px-2 py-1 rounded mt-1 ${getRoleBadgeColor(userRole)}`}>\n                      {userRole.charAt(0).toUpperCase() + userRole.slice(1)}\n                    </span>\n                  </div>\n                  <div className=\"py-1\">\n                    <Link \n                      to=\"/user-profile-settings\"\n                      className=\"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\"\n                    >\n                      <Icon name=\"User\" size={16} />\n                      <span>Profile Settings</span>\n                    </Link>\n                    <button className=\"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\">\n                      <Icon name=\"Bell\" size={16} />\n                      <span>Notifications</span>\n                    </button>\n                    <button className=\"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\">\n                      <Icon name=\"HelpCircle\" size={16} />\n                      <span>Help & Support</span>\n                    </button>\n                    <div className=\"border-t border-border my-1\"></div>\n                    <button \n                      onClick={handleLogout}\n                      className=\"w-full flex items-center space-x-2 px-2 py-2 text-sm text-destructive hover:bg-muted rounded-sm transition-micro\"\n                    >\n                      <Icon name=\"LogOut\" size={16} />\n                      <span>Sign Out</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile Menu Toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden\"\n          >\n            <Icon name={isMobileMenuOpen ? \"X\" : \"Menu\"} size={20} />\n          </Button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMobileMenuOpen && (\n        <div ref={mobileMenuRef} className=\"lg:hidden border-t border-border bg-surface\">\n          <div className=\"px-4 py-2 space-y-1\">\n            {/* Create Organization Button for Mobile - Only for Owners */}\n            {userRole === 'owner' && (\n              <button\n                onClick={() => {\n                  handleCreateOrganization();\n                  setIsMobileMenuOpen(false);\n                }}\n                className=\"w-full flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-micro mb-2\"\n              >\n                <Icon name=\"Building2\" size={16} />\n                <span>New Organization</span>\n              </button>\n            )}\n\n            {/* Create Project Button for Mobile */}\n            {roleFeatures.canCreateProjects && (\n              <button\n                onClick={() => {\n                  navigate('/project-management');\n                  setIsMobileMenuOpen(false);\n                }}\n                className=\"w-full flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-micro\"\n              >\n                <Icon name=\"Plus\" size={16} />\n                <span>New Project</span>\n              </button>\n            )}\n\n            {navigationItems.map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                onClick={() => setIsMobileMenuOpen(false)}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${\n                  isActivePath(item.path)\n                    ? 'bg-primary text-primary-foreground'\n                    : 'text-text-secondary hover:text-text-primary hover:bg-muted'\n                }`}\n              >\n                <Icon name={item.icon} size={16} />\n                <span>{item.label}</span>\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Create Project Modal */}\n      <CreateProjectModal\n        isOpen={isCreateProjectModalOpen}\n        onClose={() => setIsCreateProjectModalOpen(false)}\n        onCreateProject={handleCreateProjectSubmit}\n        organizationId={organization.id || 1}\n        organizationName={organization.name}\n      />\n\n      {/* Create Organization Modal */}\n      <CreateOrganizationModal\n        isOpen={isCreateOrganizationModalOpen}\n        onClose={() => setIsCreateOrganizationModalOpen(false)}\n        onCreateOrganization={handleCreateOrganizationSubmit}\n      />\n    </header>\n  );\n};\n\nexport default RoleBasedHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAO,KAAKC,mBAAmB,MAAM,iCAAiC;AACtE,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,OAAOC,uBAAuB,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExE,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ,GAAG,QAAQ;EAAEC,WAAW,EAAEC,eAAe;EAAEC,mBAAmB,EAAEC;AAAwB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA;EAC/H,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC6B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiC,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACmC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACqC,6BAA6B,EAAEC,gCAAgC,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACzF,MAAMuC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAE9B,MAAMoC,cAAc,GAAGxC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyC,kBAAkB,GAAGzC,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM0C,eAAe,GAAG1C,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM2C,aAAa,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM4C,uBAAuB,GAAG5C,MAAM,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACiB,WAAW,EAAE4B,cAAc,CAAC,GAAG9C,QAAQ,CAACmB,eAAe,IAAI;IAChE4B,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,2BAA2B;IACnCC,IAAI,EAAEjC;EACR,CAAC,CAAC;EAEF,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAACqB,uBAAuB,IAAI;IAC1E0B,IAAI,EAAE,YAAY;IAClBM,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;;EAExE;EACAE,SAAS,CAAC,MAAM;IACd,MAAMuD,2BAA2B,GAAG,MAAAA,CAAA,KAAY;MAC9C,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMlD,WAAW,CAACmD,cAAc,CAAC,CAAC;QACjDC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,MAAM,CAAC,CAAC,CAAC;;QAE7C,IAAIA,MAAM,CAACI,IAAI,IAAIJ,MAAM,CAACI,IAAI,CAACC,IAAI,IAAIL,MAAM,CAACI,IAAI,CAACC,IAAI,CAACf,KAAK,EAAE;UAC7D,MAAMgB,QAAQ,GAAGN,MAAM,CAACI,IAAI,CAACC,IAAI;;UAEjC;UACA,IAAIE,QAAQ,GAAG,EAAE;UACjB,IAAID,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACG,QAAQ,EAAE;YAC3CF,QAAQ,GAAG,GAAGD,QAAQ,CAACE,SAAS,IAAI,EAAE,IAAIF,QAAQ,CAACG,QAAQ,IAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC;UAC5E;UACA,IAAI,CAACH,QAAQ,IAAID,QAAQ,CAAChB,KAAK,EAAE;YAC/BiB,QAAQ,GAAGD,QAAQ,CAAChB,KAAK,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACzC;UACA,IAAI,CAACJ,QAAQ,EAAE;YACbA,QAAQ,GAAG,MAAM;UACnB;UAEAL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;YAAEG,QAAQ;YAAEC;UAAS,CAAC,CAAC,CAAC,CAAC;;UAE1DnB,cAAc,CAAC;YACbC,IAAI,EAAEkB,QAAQ;YACdjB,KAAK,EAAEgB,QAAQ,CAAChB,KAAK,IAAI,EAAE;YAC3BC,MAAM,EAAEe,QAAQ,CAACf,MAAM,IAAIe,QAAQ,CAACM,cAAc,IAAI,2BAA2B;YACjFpB,IAAI,EAAEc,QAAQ,CAACd,IAAI,IAAIjC;UACzB,CAAC,CAAC;;UAEF;UACA,IAAIyC,MAAM,CAACI,IAAI,CAACS,aAAa,IAAIb,MAAM,CAACI,IAAI,CAACS,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;YACrE,MAAMC,QAAQ,GAAGf,MAAM,CAACI,IAAI,CAACS,aAAa,CAACG,GAAG,CAACC,GAAG;cAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;cAAA,OAAK;gBACrDC,EAAE,EAAEJ,GAAG,CAACI,EAAE,MAAAH,iBAAA,GAAID,GAAG,CAACxB,YAAY,cAAAyB,iBAAA,uBAAhBA,iBAAA,CAAkBG,EAAE;gBAClChC,IAAI,EAAE4B,GAAG,CAAC5B,IAAI,MAAA8B,kBAAA,GAAIF,GAAG,CAACxB,YAAY,cAAA0B,kBAAA,uBAAhBA,kBAAA,CAAkB9B,IAAI;gBACxCM,MAAM,EAAEsB,GAAG,CAACtB,MAAM,MAAAyB,kBAAA,GAAIH,GAAG,CAACxB,YAAY,cAAA2B,kBAAA,uBAAhBA,kBAAA,CAAkBzB,MAAM;gBAC9CH,IAAI,EAAEyB,GAAG,CAACzB;cACZ,CAAC;YAAA,CAAC,CAAC;YAEHM,yBAAyB,CAACiB,QAAQ,CAAC;;YAEnC;YACA,MAAMO,YAAY,GAAGxE,WAAW,CAACyE,iBAAiB,CAAC,CAAC;YACpD,MAAMC,UAAU,GAAGT,QAAQ,CAACU,IAAI,CAACR,GAAG,IAAIA,GAAG,CAACI,EAAE,KAAKC,YAAY,CAAC,IAAIP,QAAQ,CAAC,CAAC,CAAC;YAE/E,IAAIS,UAAU,EAAE;cACd9B,eAAe,CAAC;gBACd2B,EAAE,EAAEG,UAAU,CAACH,EAAE;gBACjBhC,IAAI,EAAEmC,UAAU,CAACnC,IAAI,IAAI,cAAc;gBACvCM,MAAM,EAAE6B,UAAU,CAAC7B,MAAM,IAAI,EAAE;gBAC/BC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ;UACF;QACF,CAAC,MAAM;UACLM,OAAO,CAACwB,IAAI,CAAC,gCAAgC,CAAC;UAC9CxB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,MAAM,CAAC;UACvC;QACF;MACF,CAAC,CAAC,OAAO2B,KAAK,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACd3B,OAAO,CAACyB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;;QAElE;QACA;QACA,IAAI,CAAAC,cAAA,GAAAD,KAAK,CAACG,OAAO,cAAAF,cAAA,eAAbA,cAAA,CAAeG,QAAQ,CAAC,KAAK,CAAC,KAAAF,eAAA,GAAIF,KAAK,CAACG,OAAO,cAAAD,eAAA,eAAbA,eAAA,CAAeE,QAAQ,CAAC,cAAc,CAAC,EAAE;UAC7E7B,OAAO,CAACwB,IAAI,CAAC,6CAA6C,CAAC;;UAE3D;UACAtC,cAAc,CAAC;YACbC,IAAI,EAAE,MAAM;YACZC,KAAK,EAAE,kBAAkB;YACzBC,MAAM,EAAE,2BAA2B;YACnCC,IAAI,EAAEjC;UACR,CAAC,CAAC;;UAEF;UACAmC,eAAe,CAAC;YACd2B,EAAE,EAAE,cAAc;YAClBhC,IAAI,EAAE,cAAc;YACpBM,MAAM,EAAE,aAAa;YACrBC,IAAI,EAAE;UACR,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAM,OAAO,CAACyB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAC3D;MACF;IACF,CAAC;;IAED;IACA,IAAI,CAAClE,eAAe,IAAI,CAACE,uBAAuB,EAAE;MAChDoC,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACtC,eAAe,EAAEE,uBAAuB,EAAEJ,QAAQ,CAAC,CAAC;;EAExD;EACAf,SAAS,CAAC,MAAM;IACd,IAAIiB,eAAe,EAAE;MACnB2B,cAAc,CAAC3B,eAAe,CAAC;IACjC;EACF,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErBjB,SAAS,CAAC,MAAM;IACd,IAAImB,uBAAuB,EAAE;MAC3B+B,eAAe,CAAC/B,uBAAuB,CAAC;IAC1C;EACF,CAAC,EAAE,CAACA,uBAAuB,CAAC,CAAC;;EAE7B;EACA,MAAM0C,IAAI,GAAG7C,WAAW;;EAExB;EACA,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACAE,SAAS,CAAC,MAAM;IACd,MAAM4F,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,EAAC3C,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE4B,EAAE,GAAE;MAEvB,IAAI;QACF;QACA,MAAM;UAAEgB,OAAO,EAAEC;QAAW,CAAC,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC;QACtE,MAAMC,QAAQ,GAAG,MAAMD,UAAU,CAACC,QAAQ,CAACC,MAAM,CAAC/C,YAAY,CAAC4B,EAAE,CAAC;QAElE,MAAMoB,mBAAmB,GAAG,CAACF,QAAQ,IAAI,EAAE,EAAEvB,GAAG,CAAC0B,OAAO,KAAK;UAC3DrB,EAAE,EAAEqB,OAAO,CAACrB,EAAE;UACdhC,IAAI,EAAEqD,OAAO,CAACrD,IAAI,IAAI,kBAAkB;UACxCsD,WAAW,EAAED,OAAO,CAACC,WAAW,IAAI,EAAE;UACtCC,MAAM,EAAEF,OAAO,CAACE,MAAM,IAAI;QAC5B,CAAC,CAAC,CAAC;QAEHT,oBAAoB,CAACM,mBAAmB,CAAC;;QAEzC;QACA,MAAMI,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;QAC/D,IAAIF,cAAc,EAAE;UAClB,MAAMG,YAAY,GAAGP,mBAAmB,CAAChB,IAAI,CAACwB,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKwB,cAAc,CAAC;UAC3E,IAAIG,YAAY,EAAE;YAChBf,iBAAiB,CAACe,YAAY,CAAC;UACjC;QACF,CAAC,MAAM,IAAIP,mBAAmB,CAAC3B,MAAM,GAAG,CAAC,EAAE;UACzCmB,iBAAiB,CAACQ,mBAAmB,CAAC,CAAC,CAAC,CAAC;UACzCK,YAAY,CAACI,OAAO,CAAC,kBAAkB,EAAET,mBAAmB,CAAC,CAAC,CAAC,CAACpB,EAAE,CAAC;QACrE;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDQ,oBAAoB,CAAC,EAAE,CAAC;MAC1B;IACF,CAAC;IAEDC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC3C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE4B,EAAE,CAAC,CAAC;;EAEtB;EACA,MAAM8B,kBAAkB,GAAI3D,IAAI,IAAK;IACnC,MAAM4D,SAAS,GAAG,CAChB;MAAEC,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE,uBAAuB;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;IAAE,CAAC,EAClH;MAAEH,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;IAAE,CAAC,EAC3G;MAAEH,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO;IAAE,CAAC,CACrG;IAED,MAAMC,UAAU,GAAG;MACjB;IAAA,CACD;IAED,MAAMC,UAAU,GAAG,CACjB;MAAEL,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE,wBAAwB;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,EAC7F;MAAEH,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,EAC/E;MAAEH,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,CAC7E;;IAED;IACA,MAAMG,QAAQ,GAAG,CAAC,GAAGP,SAAS,EAAE,GAAGK,UAAU,EAAE,GAAGC,UAAU,CAAC;IAC7D,OAAOC,QAAQ,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACL,KAAK,CAACzB,QAAQ,CAACvC,IAAI,CAACsE,WAAW,CAAC,CAAC,CAAC,CAAC;EACzE,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIvE,IAAI,IAAK;IAChC,MAAMwE,QAAQ,GAAG;MACfC,MAAM,EAAE;QACNC,iBAAiB,EAAE,KAAK;QACxBC,gBAAgB,EAAE,KAAK;QACvBC,iBAAiB,EAAE,KAAK;QACxBC,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,sBAAsB,EAAE;MAC1B,CAAC;MACDC,MAAM,EAAE;QACNN,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,iBAAiB,EAAE,KAAK;QACxBC,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,sBAAsB,EAAE;MAC1B,CAAC;MACDE,KAAK,EAAE;QACLP,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,sBAAsB,EAAE;MAC1B,CAAC;MACDG,KAAK,EAAE;QACLR,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,gBAAgB,EAAE,IAAI;QACtBC,sBAAsB,EAAE;MAC1B;IACF,CAAC;IACD,OAAOP,QAAQ,CAACxE,IAAI,CAACsE,WAAW,CAAC,CAAC,CAAC,IAAIE,QAAQ,CAACC,MAAM;EACxD,CAAC;EAED,MAAMU,eAAe,GAAGxB,kBAAkB,CAAC5F,QAAQ,CAAC;EACpD,MAAMqH,YAAY,GAAGb,eAAe,CAACxG,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAACsH,aAAa,EAAEC,gBAAgB,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAExD;;EAEA;EACAE,SAAS,CAAC,MAAM;IACd,MAAMuI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF,MAAMC,iBAAiB,GAAG,MAAMjI,mBAAmB,CAACkI,gBAAgB,CAAC;UAAEC,KAAK,EAAE;QAAG,CAAC,CAAC;QACnFJ,gBAAgB,CAACE,iBAAiB,IAAI,EAAE,CAAC;MAC3C,CAAC,CAAC,OAAOrD,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDmD,gBAAgB,CAAC,EAAE,CAAC;MACtB;IACF,CAAC;IAEDC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC+I,SAAS,EAAEC,YAAY,CAAC,GAAGhJ,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMiJ,WAAW,GAAGV,aAAa,CAACjB,MAAM,CAAC4B,CAAC,IAAI,CAACA,CAAC,CAACC,MAAM,CAAC,CAAC3E,MAAM;EAC/D,MAAM4E,iBAAiB,GAAGb,aAAa,CAACjB,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAACG,QAAQ,KAAK,MAAM,IAAI,CAACH,CAAC,CAACC,MAAM,CAAC,CAAC3E,MAAM;;EAE9F;EACA,MAAM8E,qBAAqB,GAAGf,aAAa,CAACjB,MAAM,CAACiC,YAAY,IAAI;IACjE,QAAQV,kBAAkB;MACxB,KAAK,QAAQ;QACX,OAAO,CAACU,YAAY,CAACJ,MAAM;MAC7B,KAAK,eAAe;QAClB,OAAOI,YAAY,CAACF,QAAQ,KAAK,MAAM;MACzC;QACE,OAAO,IAAI;IACf;EACF,CAAC,CAAC;EAEF,MAAMG,UAAU,GAAIC,cAAc,IAAK;IACrCjB,gBAAgB,CAACkB,IAAI,IACnBA,IAAI,CAAChF,GAAG,CAAC6E,YAAY,IACnBA,YAAY,CAACxE,EAAE,KAAK0E,cAAc,GAC9B;MAAE,GAAGF,YAAY;MAAEJ,MAAM,EAAE;IAAK,CAAC,GACjCI,YACN,CACF,CAAC;EACH,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1BnB,gBAAgB,CAACkB,IAAI,IACnBA,IAAI,CAAChF,GAAG,CAAC6E,YAAY,KAAK;MAAE,GAAGA,YAAY;MAAEJ,MAAM,EAAE;IAAK,CAAC,CAAC,CAC9D,CAAC;EACH,CAAC;EAED,MAAMS,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,iBAAiB;QAAE,OAAO,UAAU;MACzC,KAAK,gBAAgB;QAAE,OAAO,UAAU;MACxC,KAAK,kBAAkB;QAAE,OAAO,OAAO;MACvC,KAAK,eAAe;QAAE,OAAO,KAAK;MAClC,KAAK,gBAAgB;QAAE,OAAO,eAAe;MAC7C;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIT,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,kBAAkB;MACtC,KAAK,QAAQ;QAAE,OAAO,cAAc;MACpC,KAAK,KAAK;QAAE,OAAO,uBAAuB;MAC1C;QAAS,OAAO,uBAAuB;IACzC;EACF,CAAC;EAED,MAAMU,eAAe,GAAIC,SAAS,IAAK;IACrC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAGF,GAAG,GAAGD,SAAS;IAC5B,MAAMI,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAC9C,MAAMI,KAAK,GAAGF,IAAI,CAACC,KAAK,CAACH,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACjD,MAAMK,IAAI,GAAGH,IAAI,CAACC,KAAK,CAACH,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAErD,IAAIC,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;IAClC,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,GAAGA,OAAO,OAAO;IAC1C,IAAIG,KAAK,GAAG,EAAE,EAAE,OAAO,GAAGA,KAAK,OAAO;IACtC,IAAIC,IAAI,GAAG,CAAC,EAAE,OAAO,GAAGA,IAAI,OAAO;IACnC,OAAOR,SAAS,CAACS,kBAAkB,CAAC,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAACnC,aAAa,EAAEtH,QAAQ,KAAK;IAC7D,OAAOsH,aAAa,CAACjB,MAAM,CAACiC,YAAY,IAAI;MAC1C,QAAQA,YAAY,CAACM,IAAI;QACvB,KAAK,gBAAgB;UACnB;UACA,OAAO,IAAI;QACb,KAAK,eAAe;UAClB;UACA,OAAO,IAAI;QACb,KAAK,iBAAiB;UACpB;UACA;UACA,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACpE,QAAQ,CAACxE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuG,WAAW,CAAC,CAAC,CAAC;QACvE,KAAK,gBAAgB;UACnB;UACA,OAAO,IAAI;QACb;UACE,OAAO,IAAI;MACf;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMmD,wBAAwB,GAAG,MAAAA,CAAOlB,cAAc,EAAEmB,MAAM,EAAEC,gBAAgB,KAAK;IACnF7B,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,QAAQ4B,MAAM;QACZ,KAAK,aAAa;UAChB;UACAhH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgH,gBAAgB,CAACC,MAAM,CAAC;UACvD;UACAtI,QAAQ,CAAC,eAAe,EAAE;YACxBuI,KAAK,EAAE;cACLC,SAAS,EAAEH,gBAAgB,CAACG,SAAS;cACrCC,eAAe,EAAEJ,gBAAgB,CAACC,MAAM;cACxCI,YAAY,EAAE;YAChB;UACF,CAAC,CAAC;UACF;UACA1C,gBAAgB,CAACkB,IAAI,IAAIA,IAAI,CAACpC,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAACnE,EAAE,KAAK0E,cAAc,CAAC,CAAC;UACnE;QAEF,KAAK,WAAW;UACd;UACAjH,QAAQ,CAAC,eAAe,EAAE;YACxBuI,KAAK,EAAE;cACLC,SAAS,EAAEH,gBAAgB,CAACG,SAAS;cACrCC,eAAe,EAAEJ,gBAAgB,CAACC;YACpC;UACF,CAAC,CAAC;UACFtB,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF,KAAK,cAAc;UACjB;UACA,MAAM0B,MAAM,GAAGC,MAAM,CAAC,6DAA6D,CAAC;UACpFxH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgH,gBAAgB,CAACC,MAAM,EAAE,SAAS,EAAEK,MAAM,CAAC;UAC1E;UACA3C,gBAAgB,CAACkB,IAAI,IAAIA,IAAI,CAACpC,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAACnE,EAAE,KAAK0E,cAAc,CAAC,CAAC;UACnE;QAEF,KAAK,gBAAgB;UACnB;UACA7F,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgH,gBAAgB,CAACQ,SAAS,CAAC;UAC7D;UACA7C,gBAAgB,CAACkB,IAAI,IAAIA,IAAI,CAACpC,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAACnE,EAAE,KAAK0E,cAAc,CAAC,CAAC;UACnE;QAEF,KAAK,iBAAiB;UACpB;UACA7F,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgH,gBAAgB,CAACQ,SAAS,CAAC;UAC7D7C,gBAAgB,CAACkB,IAAI,IAAIA,IAAI,CAACpC,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAACnE,EAAE,KAAK0E,cAAc,CAAC,CAAC;UACnE;QAEF,KAAK,cAAc;UACjB;UACA7F,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgH,gBAAgB,CAACQ,SAAS,CAAC;UACnE7B,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF,KAAK,cAAc;UACjB;UACA7F,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgH,gBAAgB,CAACQ,SAAS,CAAC;UAC3DC,MAAM,CAACC,IAAI,CAAC,gCAAgC,EAAE,QAAQ,CAAC;UACvD/B,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF,KAAK,qBAAqB;UACxB;UACA7F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgH,gBAAgB,CAACW,cAAc,CAAC;UACvEhC,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF,KAAK,cAAc;UACjB;UACAjH,QAAQ,CAAC,mBAAmB,EAAE;YAC5BuI,KAAK,EAAE;cACLC,SAAS,EAAEH,gBAAgB,CAACG;YAC9B;UACF,CAAC,CAAC;UACFxB,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF;UACE7F,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+G,MAAM,CAAC;UACtCpB,UAAU,CAACC,cAAc,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,SAAS;MACR2D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMyC,YAAY,GAAIzE,IAAI,IAAKzE,QAAQ,CAACmJ,QAAQ,KAAK1E,IAAI;;EAEzD;EACA9G,SAAS,CAAC,MAAM;IACd,MAAMyL,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAInJ,cAAc,CAACoJ,OAAO,IAAI,CAACpJ,cAAc,CAACoJ,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC5ErK,oBAAoB,CAAC,KAAK,CAAC;MAC7B;MACA,IAAIgB,kBAAkB,CAACmJ,OAAO,IAAI,CAACnJ,kBAAkB,CAACmJ,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACpFnK,wBAAwB,CAAC,KAAK,CAAC;MACjC;MACA,IAAIe,eAAe,CAACkJ,OAAO,IAAI,CAAClJ,eAAe,CAACkJ,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC9EjK,qBAAqB,CAAC,KAAK,CAAC;MAC9B;MACA,IAAIc,aAAa,CAACiJ,OAAO,IAAI,CAACjJ,aAAa,CAACiJ,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC1E/J,mBAAmB,CAAC,KAAK,CAAC;MAC5B;MACA,IAAIa,uBAAuB,CAACgJ,OAAO,IAAI,CAAChJ,uBAAuB,CAACgJ,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC9F7J,6BAA6B,CAAC,KAAK,CAAC;MACtC;IACF,CAAC;IAED8J,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,wBAAwB,GAAG,MAAOC,KAAK,IAAK;IAChD,IAAI;MACFxI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuI,KAAK,CAAC;;MAEhD;MACA,MAAMC,WAAW,GAAG9I,sBAAsB,CAAC4B,IAAI,CAACR,GAAG,IAAIA,GAAG,CAACI,EAAE,KAAKqH,KAAK,CAAC;MAExE,IAAIC,WAAW,EAAE;QACf;QACA7F,YAAY,CAACI,OAAO,CAAC,gBAAgB,EAAEwF,KAAK,CAAC;QAC7C5F,YAAY,CAACI,OAAO,CAAC,UAAU,EAAEyF,WAAW,CAACnJ,IAAI,CAAC;;QAElD;QACAE,eAAe,CAAC;UACd2B,EAAE,EAAEsH,WAAW,CAACtH,EAAE;UAClBhC,IAAI,EAAEsJ,WAAW,CAACtJ,IAAI;UACtBM,MAAM,EAAEgJ,WAAW,CAAChJ,MAAM;UAC1BC,IAAI,EAAE;QACR,CAAC,CAAC;;QAEF;QACAgI,MAAM,CAAC/I,QAAQ,CAAC+J,MAAM,CAAC,CAAC;MAC1B;MAEA5K,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD3D,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAM6K,mBAAmB,GAAInG,OAAO,IAAK;IACvCxC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuC,OAAO,CAAC;IAC7CT,iBAAiB,CAACS,OAAO,CAAC;IAC1BxE,wBAAwB,CAAC,KAAK,CAAC;;IAE/B;IACA4E,YAAY,CAACI,OAAO,CAAC,kBAAkB,EAAER,OAAO,CAACrB,EAAE,CAAC;;IAEpD;IACAvC,QAAQ,CAAC,wBAAwB4D,OAAO,CAACrB,EAAE,EAAE,EAAE;MAC7CgG,KAAK,EAAE;QACLC,SAAS,EAAE5E,OAAO,CAACrB,EAAE;QACrBqB,OAAO,EAAEA;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMoG,mBAAmB,GAAGA,CAAA,KAAM;IAChC5I,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCjC,wBAAwB,CAAC,KAAK,CAAC;IAC/BQ,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMqK,yBAAyB,GAAG,MAAOC,WAAW,IAAK;IACvD,IAAI;MACF9I,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6I,WAAW,CAAC;MAC7C;MACA;MACA,MAAMC,UAAU,GAAG;QACjB5H,EAAE,EAAEmF,IAAI,CAACD,GAAG,CAAC,CAAC;QACdlH,IAAI,EAAE2J,WAAW,CAAC3J,IAAI;QACtBsD,WAAW,EAAEqG,WAAW,CAACrG,WAAW;QACpCC,MAAM,EAAE,QAAQ;QAChBsG,UAAU,EAAE;MACd,CAAC;;MAED;MACAjH,iBAAiB,CAACgH,UAAU,CAAC;MAC7BvK,2BAA2B,CAAC,KAAK,CAAC;;MAElC;MACAI,QAAQ,CAAC,wBAAwBmK,UAAU,CAAC5H,EAAE,EAAE,EAAE;QAChDgG,KAAK,EAAE;UACLC,SAAS,EAAE2B,UAAU,CAAC5H,EAAE;UACxBqB,OAAO,EAAEuG;QACX;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOtH,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMwH,wBAAwB,GAAGA,CAAA,KAAM;IACrCjJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CnC,oBAAoB,CAAC,KAAK,CAAC;IAC3BY,gCAAgC,CAAC,IAAI,CAAC;EACxC,CAAC;EAED,MAAMwK,8BAA8B,GAAG,MAAAA,CAAOC,gBAAgB,EAAEC,QAAQ,KAAK;IAC3E,IAAI;MACFpJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEkJ,gBAAgB,CAAC;MACvD;MACA;MACA,MAAME,eAAe,GAAG;QACtBlI,EAAE,EAAEmF,IAAI,CAACD,GAAG,CAAC,CAAC;QACdlH,IAAI,EAAEgK,gBAAgB,CAAChK,IAAI;QAC3BsD,WAAW,EAAE0G,gBAAgB,CAAC1G,WAAW;QACzC6G,QAAQ,EAAEF,QAAQ,GAAGG,GAAG,CAACC,eAAe,CAACJ,QAAQ,CAAC,GAAG,IAAI;QACzD,GAAGD;MACL,CAAC;MAEDzK,gCAAgC,CAAC,KAAK,CAAC;;MAEvC;MACAsB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEoJ,eAAe,CAAC;MAClE;MACAzK,QAAQ,CAAC,wBAAwB,EAAE;QACjCuI,KAAK,EAAE;UACLvF,OAAO,EAAE,iBAAiBuH,gBAAgB,CAAChK,IAAI,yBAAyB;UACxE8G,IAAI,EAAE;QACR;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOxE,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMgI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFzJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;;MAE7B;MACA,MAAMrD,WAAW,CAAC8M,MAAM,CAAC,CAAC;;MAE1B;MACAxL,qBAAqB,CAAC,KAAK,CAAC;MAC5BJ,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,wBAAwB,CAAC,KAAK,CAAC;MAC/BI,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACAQ,QAAQ,CAAC,QAAQ,EAAE;QAAE+K,OAAO,EAAE;MAAK,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOlI,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACA7C,QAAQ,CAAC,QAAQ,EAAE;QAAE+K,OAAO,EAAE;MAAK,CAAC,CAAC;IACvC;EACF,CAAC;;EAED;EACA,IAAIhL,QAAQ,CAACmJ,QAAQ,KAAK,QAAQ,IAAInJ,QAAQ,CAACmJ,QAAQ,KAAK,WAAW,EAAE;IACvE,OAAO,IAAI;EACb;;EAEA;EACA,MAAM8B,iBAAiB,GAAItK,IAAI,IAAK;IAClC,MAAMuK,MAAM,GAAG;MACb9F,MAAM,EAAE,2BAA2B;MACnCO,MAAM,EAAE,2BAA2B;MACnCC,KAAK,EAAE,+BAA+B;MACtCC,KAAK,EAAE;IACT,CAAC;IACD,OAAOqF,MAAM,CAACvK,IAAI,CAACsE,WAAW,CAAC,CAAC,CAAC,IAAIiG,MAAM,CAAC9F,MAAM;EACpD,CAAC;EAED,oBACE9G,OAAA;IAAQ6M,SAAS,EAAC,uFAAuF;IAAAC,QAAA,gBACvG9M,OAAA;MAAK6M,SAAS,EAAC,qDAAqD;MAAAC,QAAA,gBAElE9M,OAAA;QAAK6M,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChC9M,OAAA,CAACV,IAAI;UAACyN,EAAE,EAAC,uBAAuB;UAACF,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACjF9M,OAAA;YAAK6M,SAAS,EAAC,gEAAgE;YAAAC,QAAA,eAC7E9M,OAAA;cAAKgN,OAAO,EAAC,WAAW;cAACH,SAAS,EAAC,iCAAiC;cAACI,IAAI,EAAC,cAAc;cAAAH,QAAA,eACtF9M,OAAA;gBAAMkN,CAAC,EAAC;cAA8D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtN,OAAA;YAAM6M,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNtN,OAAA;QAAK6M,SAAS,EAAC,uCAAuC;QAAAC,QAAA,GACnDrF,YAAY,CAACL,sBAAsB,gBAClCpH,OAAA;UAAK6M,SAAS,EAAC,UAAU;UAACU,GAAG,EAAE3L,cAAe;UAAAkL,QAAA,gBAC5C9M,OAAA,CAACN,MAAM;YACL8N,OAAO,EAAC,OAAO;YACfC,OAAO,EAAEA,CAAA,KAAM5M,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;YACxDiM,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEjD9M,OAAA;cAAK6M,SAAS,EAAC,8DAA8D;cAAAC,QAAA,eAC3E9M,OAAA;gBAAM6M,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EACpD,CAAAxK,YAAY,aAAZA,YAAY,wBAAA5B,kBAAA,GAAZ4B,YAAY,CAAEJ,IAAI,cAAAxB,kBAAA,uBAAlBA,kBAAA,CAAoBgN,MAAM,CAAC,CAAC,CAAC,KAAI;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtN,OAAA;cAAM6M,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAE,CAAAxK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEJ,IAAI,KAAI;YAAc;cAAAiL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7FtN,OAAA,CAACP,IAAI;cAACyC,IAAI,EAAC,aAAa;cAACyL,IAAI,EAAE,EAAG;cAACd,SAAS,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EAER1M,iBAAiB,iBAChBZ,OAAA;YAAK6M,SAAS,EAAC,sGAAsG;YAAAC,QAAA,eACnH9M,OAAA;cAAK6M,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB9M,OAAA;gBAAK6M,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,EAAC;cAE3F;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAGLlN,QAAQ,KAAK,OAAO,iBACnBJ,OAAA,CAAAE,SAAA;gBAAA4M,QAAA,eACE9M,OAAA;kBACEyN,OAAO,EAAEzB,wBAAyB;kBAClCa,SAAS,EAAC,+HAA+H;kBAAAC,QAAA,gBAEzI9M,OAAA;oBAAK6M,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,eAC7E9M,OAAA,CAACP,IAAI;sBAACyC,IAAI,EAAC,MAAM;sBAACyL,IAAI,EAAE,EAAG;sBAACd,SAAS,EAAC;oBAAyB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACNtN,OAAA;oBAAK6M,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrB9M,OAAA;sBAAK6M,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/EtN,OAAA;sBAAK6M,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAwB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC,gBACT,CACH,EAEA5K,sBAAsB,CAACmB,GAAG,CAAEC,GAAG,iBAC9B9D,OAAA;gBAEEyN,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAACxH,GAAG,CAACI,EAAE,CAAE;gBAChD2I,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,gBAE7G9M,OAAA;kBAAK6M,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,eAC3E9M,OAAA;oBAAM6M,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACpDhJ,GAAG,CAAC5B,IAAI,CAACwL,MAAM,CAAC,CAAC;kBAAC;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtN,OAAA;kBAAK6M,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrB9M,OAAA;oBAAK6M,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAEhJ,GAAG,CAAC5B;kBAAI;oBAAAiL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEtN,OAAA;oBAAK6M,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEhJ,GAAG,CAACtB;kBAAM;oBAAA2K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACNtN,OAAA;kBAAM6M,SAAS,EAAE,6BAA6BF,iBAAiB,CAAC7I,GAAG,CAACzB,IAAI,CAAC,EAAG;kBAAAyK,QAAA,EACzEhJ,GAAG,CAACzB;gBAAI;kBAAA8K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACNxJ,GAAG,CAACI,EAAE,KAAK,CAAC,iBACXlE,OAAA,CAACP,IAAI;kBAACyC,IAAI,EAAC,OAAO;kBAACyL,IAAI,EAAE,EAAG;kBAACd,SAAS,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACxD;cAAA,GAlBMxJ,GAAG,CAACI,EAAE;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBP,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;QAAA;QAEN;QACAtN,OAAA;UAAK6M,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD9M,OAAA;YAAK6M,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3E9M,OAAA;cAAM6M,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACpD,CAAAxK,YAAY,aAAZA,YAAY,wBAAA3B,mBAAA,GAAZ2B,YAAY,CAAEJ,IAAI,cAAAvB,mBAAA,uBAAlBA,mBAAA,CAAoB+M,MAAM,CAAC,CAAC,CAAC,KAAI;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNtN,OAAA;YAAM6M,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAE,CAAAxK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEJ,IAAI,KAAI;UAAc;YAAAiL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CACN,EAGAzI,cAAc,iBACb7E,OAAA;UAAK6M,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3E9M,OAAA;YAAK6M,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF9M,OAAA,CAACP,IAAI;cAACyC,IAAI,EAAC,QAAQ;cAACyL,IAAI,EAAE,EAAG;cAACd,SAAS,EAAC;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNtN,OAAA;YAAK6M,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B9M,OAAA;cAAM6M,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5DtN,OAAA;cAAM6M,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAEjI,cAAc,CAAC3C;YAAI;cAAAiL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDtN,OAAA;UAAK6M,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCtF,eAAe,CAAC3D,GAAG,CAAE6C,IAAI,IAAK;YAC7B;YACA,IAAIA,IAAI,CAACR,KAAK,KAAK,UAAU,EAAE;cAC7B,oBACElG,OAAA;gBAAqB6M,SAAS,EAAC,UAAU;gBAACU,GAAG,EAAE1L,kBAAmB;gBAAAiL,QAAA,gBAChE9M,OAAA,CAACN,MAAM;kBACL8N,OAAO,EAAC,OAAO;kBACfC,OAAO,EAAEA,CAAA,KAAM1M,wBAAwB,CAAC,CAACD,qBAAqB,CAAE;kBAChE+L,SAAS,EAAE,yFACTjC,YAAY,CAAClE,IAAI,CAACP,IAAI,CAAC,GACnB,oCAAoC,GACpC,4DAA4D,EAC/D;kBAAA2G,QAAA,gBAEH9M,OAAA,CAACP,IAAI;oBAACyC,IAAI,EAAEwE,IAAI,CAACN,IAAK;oBAACuH,IAAI,EAAE;kBAAG;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnCtN,OAAA;oBAAA8M,QAAA,EAAOpG,IAAI,CAACR;kBAAK;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzBtN,OAAA,CAACP,IAAI;oBAACyC,IAAI,EAAC,aAAa;oBAACyL,IAAI,EAAE,EAAG;oBAACd,SAAS,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,EAERxM,qBAAqB,iBACpBd,OAAA;kBAAK6M,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,eACnH9M,OAAA;oBAAK6M,SAAS,EAAC,KAAK;oBAAAC,QAAA,gBAClB9M,OAAA;sBAAK6M,SAAS,EAAC,2EAA2E;sBAAAC,QAAA,EAAC;oBAE3F;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAGL,CAAClN,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,OAAO,kBAC5CJ,OAAA,CAAAE,SAAA;sBAAA4M,QAAA,eACE9M,OAAA;wBACEyN,OAAO,EAAE9B,mBAAoB;wBAC7BkB,SAAS,EAAC,+HAA+H;wBAAAC,QAAA,gBAEzI9M,OAAA;0BAAK6M,SAAS,EAAC,gEAAgE;0BAAAC,QAAA,eAC7E9M,OAAA,CAACP,IAAI;4BAACyC,IAAI,EAAC,MAAM;4BAACyL,IAAI,EAAE,EAAG;4BAACd,SAAS,EAAC;0BAAyB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D,CAAC,eACNtN,OAAA;0BAAK6M,SAAS,EAAC,QAAQ;0BAAAC,QAAA,gBACrB9M,OAAA;4BAAK6M,SAAS,EAAC,kCAAkC;4BAAAC,QAAA,EAAC;0BAAkB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1EtN,OAAA;4BAAK6M,SAAS,EAAC,6BAA6B;4BAAAC,QAAA,EAAC;0BAAmB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA;oBAAC,gBACT,CACH,EAGAvI,iBAAiB,CAAClB,GAAG,CAAE0B,OAAO,iBAC7BvF,OAAA;sBAEEyN,OAAO,EAAEA,CAAA,KAAM/B,mBAAmB,CAACnG,OAAO,CAAE;sBAC5CsH,SAAS,EAAC,mGAAmG;sBAAAC,QAAA,gBAE7G9M,OAAA;wBAAK6M,SAAS,EAAC,8DAA8D;wBAAAC,QAAA,eAC3E9M,OAAA,CAACP,IAAI;0BAACyC,IAAI,EAAC,QAAQ;0BAACyL,IAAI,EAAE,EAAG;0BAACd,SAAS,EAAC;wBAAmB;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3D,CAAC,eACNtN,OAAA;wBAAK6M,SAAS,EAAC,QAAQ;wBAAAC,QAAA,gBACrB9M,OAAA;0BAAK6M,SAAS,EAAC,uCAAuC;0BAAAC,QAAA,EAAEvH,OAAO,CAACrD;wBAAI;0BAAAiL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC3EtN,OAAA;0BAAK6M,SAAS,EAAC,sCAAsC;0BAAAC,QAAA,EAAEvH,OAAO,CAACC;wBAAW;0BAAA2H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC,eACNtN,OAAA;wBAAK6M,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC9M,OAAA;0BAAM6M,SAAS,EAAE,6BACftH,OAAO,CAACE,MAAM,KAAK,QAAQ,GAAG,4BAA4B,GAC1DF,OAAO,CAACE,MAAM,KAAK,UAAU,GAAG,4BAA4B,GAC5D,8BAA8B,EAC7B;0BAAAqH,QAAA,EACAvH,OAAO,CAACE;wBAAM;0BAAA0H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC,EACNlN,QAAQ,KAAK,QAAQ,iBACpBJ,OAAA;0BAAM6M,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAClE;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EACL/H,OAAO,CAACrB,EAAE,KAAKW,cAAc,CAACX,EAAE,iBAC/BlE,OAAA,CAACP,IAAI;wBAACyC,IAAI,EAAC,OAAO;wBAACyL,IAAI,EAAE,EAAG;wBAACd,SAAS,EAAC;sBAAc;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CACxD;oBAAA,GAzBI/H,OAAO,CAACrB,EAAE;sBAAAiJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0BT,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,GAzEO5G,IAAI,CAACP,IAAI;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0Ed,CAAC;YAEV;;YAEA;YACA,oBACEtN,OAAA,CAACV,IAAI;cAEHyN,EAAE,EAAErG,IAAI,CAACP,IAAK;cACd0G,SAAS,EAAE,yFACTjC,YAAY,CAAClE,IAAI,CAACP,IAAI,CAAC,GACnB,oCAAoC,GACpC,4DAA4D,EAC/D;cAAA2G,QAAA,gBAEH9M,OAAA,CAACP,IAAI;gBAACyC,IAAI,EAAEwE,IAAI,CAACN,IAAK;gBAACuH,IAAI,EAAE;cAAG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCtN,OAAA;gBAAA8M,QAAA,EAAOpG,IAAI,CAACR;cAAK;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GATpB5G,IAAI,CAACP,IAAI;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CAAC;UAEX,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtN,OAAA;QAAK6M,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1C9M,OAAA;UAAK6M,SAAS,EAAC,UAAU;UAACU,GAAG,EAAEvL,uBAAwB;UAAA8K,QAAA,gBACrD9M,OAAA,CAACN,MAAM;YACL8N,OAAO,EAAC,OAAO;YACfG,IAAI,EAAC,MAAM;YACXF,OAAO,EAAEA,CAAA,KAAMpM,6BAA6B,CAAC,CAACD,0BAA0B,CAAE;YAC1EyL,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAE5B9M,OAAA,CAACP,IAAI;cAACyC,IAAI,EAAC,MAAM;cAACyL,IAAI,EAAE,EAAG;cAACd,SAAS,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7DlF,WAAW,GAAG,CAAC,iBACdpI,OAAA;cAAM6M,SAAS,EAAC,+IAA+I;cAAAC,QAAA,EAC5J1E,WAAW,GAAG,CAAC,GAAG,IAAI,GAAGA;YAAW;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,EAERlM,0BAA0B,iBACzBpB,OAAA;YAAK6M,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBAE5H9M,OAAA;cAAK6M,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC9M,OAAA;gBAAK6M,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD9M,OAAA;kBAAI6M,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAAC,eAEhE,EAAC1E,WAAW,GAAG,CAAC,iBACdpI,OAAA;oBAAM6M,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAC7F1E;kBAAW;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EACJlF,WAAW,GAAG,CAAC,iBACdpI,OAAA,CAACN,MAAM;kBACL8N,OAAO,EAAC,OAAO;kBACfG,IAAI,EAAC,IAAI;kBACTF,OAAO,EAAE3E,aAAc;kBACvB+D,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EACpD;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNtN,OAAA;gBAAK6M,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B9M,OAAA;kBACEyN,OAAO,EAAEA,CAAA,KAAMxF,qBAAqB,CAAC,KAAK,CAAE;kBAC5C4E,SAAS,EAAE,oDACT7E,kBAAkB,KAAK,KAAK,GACxB,oCAAoC,GACpC,gDAAgD,EACnD;kBAAA8E,QAAA,GACJ,MACK,EAACpF,aAAa,CAAC/D,MAAM,GAAG,CAAC,iBAAI3D,OAAA;oBAAM6M,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAEpF,aAAa,CAAC/D;kBAAM;oBAAAwJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACTtN,OAAA;kBACEyN,OAAO,EAAEA,CAAA,KAAMxF,qBAAqB,CAAC,QAAQ,CAAE;kBAC/C4E,SAAS,EAAE,oDACT7E,kBAAkB,KAAK,QAAQ,GAC3B,oCAAoC,GACpC,gDAAgD,EACnD;kBAAA8E,QAAA,GACJ,SACQ,EAAC1E,WAAW,GAAG,CAAC,iBAAIpI,OAAA;oBAAM6M,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAE1E;kBAAW;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACTtN,OAAA;kBACEyN,OAAO,EAAEA,CAAA,KAAMxF,qBAAqB,CAAC,eAAe,CAAE;kBACtD4E,SAAS,EAAE,oDACT7E,kBAAkB,KAAK,eAAe,GAClC,oCAAoC,GACpC,gDAAgD,EACnD;kBAAA8E,QAAA,GACJ,gBACe,EAACvE,iBAAiB,GAAG,CAAC,iBAAIvI,OAAA;oBAAM6M,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAEvE;kBAAiB;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtN,OAAA;cAAK6M,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtCrE,qBAAqB,CAAC9E,MAAM,KAAK,CAAC,gBACjC3D,OAAA;gBAAK6M,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClD9M,OAAA,CAACP,IAAI;kBAACyC,IAAI,EAAC,MAAM;kBAACyL,IAAI,EAAE,EAAG;kBAACd,SAAS,EAAC;gBAAyB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEtN,OAAA;kBAAG6M,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/CtN,OAAA;kBAAG6M,SAAS,EAAC,cAAc;kBAAAC,QAAA,EACxB9E,kBAAkB,KAAK,QAAQ,GAAG,gBAAgB,GAClDA,kBAAkB,KAAK,eAAe,GAAG,wBAAwB,GACjE;gBAAiB;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,GAENzD,yBAAyB,CAACpB,qBAAqB,EAAErI,QAAQ,CAAC,CAACyD,GAAG,CAAE6E,YAAY,iBAC1E1I,OAAA;gBAEE6M,SAAS,EAAE,kFACT,CAACnE,YAAY,CAACJ,MAAM,GAAG,0CAA0C,GAAG,EAAE,EACrE;gBAAAwE,QAAA,eAEH9M,OAAA;kBAAK6M,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAEzC9M,OAAA;oBAAK6M,SAAS,EAAE,kCACdnE,YAAY,CAACM,IAAI,KAAK,iBAAiB,GAAG,2BAA2B,GACrEN,YAAY,CAACM,IAAI,KAAK,gBAAgB,GAAG,6BAA6B,GACtEN,YAAY,CAACM,IAAI,KAAK,kBAAkB,GAAG,+BAA+B,GAC1EN,YAAY,CAACM,IAAI,KAAK,eAAe,GAAG,+BAA+B,GACvEN,YAAY,CAACM,IAAI,KAAK,gBAAgB,GAAG,yBAAyB,GAClE,2BAA2B,EAC1B;oBAAA8D,QAAA,eACD9M,OAAA,CAACP,IAAI;sBAACyC,IAAI,EAAE6G,mBAAmB,CAACL,YAAY,CAACM,IAAI,CAAE;sBAAC2E,IAAI,EAAE;oBAAG;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eAENtN,OAAA;oBAAK6M,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAE7B9M,OAAA;sBAAK6M,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpD9M,OAAA;wBAAK6M,SAAS,EAAC,QAAQ;wBAAAC,QAAA,gBACrB9M,OAAA;0BAAK6M,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1C9M,OAAA;4BAAG6M,SAAS,EAAE,oBAAoB,CAACnE,YAAY,CAACJ,MAAM,GAAG,iCAAiC,GAAG,+BAA+B,EAAG;4BAAAwE,QAAA,EAC5HpE,YAAY,CAACkF;0BAAK;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClB,CAAC,eACJtN,OAAA;4BAAM6M,SAAS,EAAE,iCAAiC5D,gBAAgB,CAACP,YAAY,CAACF,QAAQ,CAAC,WAAY;4BAAAsE,QAAA,EAClGpE,YAAY,CAACF;0BAAQ;4BAAA2E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNtN,OAAA;0BAAG6M,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,EAC5C5D,eAAe,CAACR,YAAY,CAACS,SAAS;wBAAC;0BAAAgE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,EACL,CAAC5E,YAAY,CAACJ,MAAM,iBACnBtI,OAAA;wBAAK6M,SAAS,EAAC;sBAAyD;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC/E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGNtN,OAAA;sBAAG6M,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EACzDpE,YAAY,CAAC/D;oBAAO;sBAAAwI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,EAGH5E,YAAY,CAACM,IAAI,KAAK,iBAAiB,IAAIN,YAAY,CAACzF,IAAI,iBAC3DjD,OAAA;sBAAK6M,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9C9M,OAAA;wBAAK6M,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/C9M,OAAA;0BACE6N,GAAG,EAAEnF,YAAY,CAACzF,IAAI,CAAC6K,cAAe;0BACtCC,GAAG,EAAErF,YAAY,CAACzF,IAAI,CAAC+K,YAAa;0BACpCnB,SAAS,EAAC;wBAAsB;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjC,CAAC,eACFtN,OAAA;0BAAM6M,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,GAAC,cAChC,EAACpE,YAAY,CAACzF,IAAI,CAAC+K,YAAY;wBAAA;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNtN,OAAA;wBAAG6M,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GAAC,WAChC,EAACpE,YAAY,CAACzF,IAAI,CAACgL,WAAW;sBAAA;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,EACH5E,YAAY,CAACzF,IAAI,CAACiL,OAAO,iBACxBlO,OAAA;wBAAG6M,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GAAC,OACpC,EAACpE,YAAY,CAACzF,IAAI,CAACiL,OAAO,CAACtE,kBAAkB,CAAC,CAAC;sBAAA;wBAAAuD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN,EAEA5E,YAAY,CAACM,IAAI,KAAK,gBAAgB,IAAIN,YAAY,CAACzF,IAAI,iBAC1DjD,OAAA;sBAAK6M,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9C9M,OAAA;wBAAK6M,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/C9M,OAAA;0BACE6N,GAAG,EAAEnF,YAAY,CAACzF,IAAI,CAACkL,eAAgB;0BACvCJ,GAAG,EAAErF,YAAY,CAACzF,IAAI,CAACmL,SAAU;0BACjCvB,SAAS,EAAC;wBAAsB;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjC,CAAC,eACFtN,OAAA;0BAAM6M,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,GAAC,eAC/B,EAACpE,YAAY,CAACzF,IAAI,CAACmL,SAAS;wBAAA;0BAAAjB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNtN,OAAA;wBAAG6M,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GACvCpE,YAAY,CAACzF,IAAI,CAACoL,SAAS,CAACzE,kBAAkB,CAAC,CAAC,EAAC,MAAI,EAAClB,YAAY,CAACzF,IAAI,CAACoL,SAAS,CAACC,kBAAkB,CAAC,EAAE,EAAE;0BAACC,IAAI,EAAE,SAAS;0BAAEC,MAAM,EAAC;wBAAS,CAAC,CAAC;sBAAA;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7I,CAAC,eACJtN,OAAA;wBAAG6M,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GAAC,YAC/B,EAACpE,YAAY,CAACzF,IAAI,CAACwL,QAAQ,EAAC,UACxC;sBAAA;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN,EAGA5E,YAAY,CAACgG,OAAO,IAAIhG,YAAY,CAACgG,OAAO,CAAC/K,MAAM,GAAG,CAAC,iBACtD3D,OAAA;sBAAK6M,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAClCpE,YAAY,CAACgG,OAAO,CAAC7K,GAAG,CAAC,CAACkG,MAAM,EAAE4E,KAAK,kBACtC3O,OAAA,CAACN,MAAM;wBAEL8N,OAAO,EAAEzD,MAAM,CAACyD,OAAO,KAAK,SAAS,GAAG,SAAS,GAAGzD,MAAM,CAACyD,OAAO,KAAK,QAAQ,GAAG,aAAa,GAAG,SAAU;wBAC5GG,IAAI,EAAC,IAAI;wBACTF,OAAO,EAAGmB,CAAC,IAAK;0BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;0BACnB/E,wBAAwB,CAACpB,YAAY,CAACxE,EAAE,EAAE6F,MAAM,CAACA,MAAM,EAAErB,YAAY,CAACzF,IAAI,CAAC;wBAC7E,CAAE;wBACF6L,QAAQ,EAAE5G,SAAU;wBACpB2E,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAEtB/C,MAAM,CAAC7D;sBAAK,GAVRyI,KAAK;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAWJ,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA9GD5E,YAAY,CAACxE,EAAE;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA+GjB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNtN,OAAA;cAAK6M,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzC9M,OAAA,CAACN,MAAM;gBACL8N,OAAO,EAAC,OAAO;gBACfG,IAAI,EAAC,IAAI;gBACTd,SAAS,EAAC,wCAAwC;gBAClDY,OAAO,EAAEA,CAAA,KAAM;kBACbpM,6BAA6B,CAAC,KAAK,CAAC;kBACpC;kBACA0B,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;gBACpD,CAAE;gBAAA8J,QAAA,EACH;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNtN,OAAA;UAAK6M,SAAS,EAAC,UAAU;UAACU,GAAG,EAAEzL,eAAgB;UAAAgL,QAAA,gBAC7C9M,OAAA,CAACN,MAAM;YACL8N,OAAO,EAAC,OAAO;YACfC,OAAO,EAAEA,CAAA,KAAMxM,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;YAC1D6L,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEjD9M,OAAA;cAAK6M,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAC/E9M,OAAA;gBAAM6M,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EAC1D5J,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEhB,IAAI,GAAGgB,IAAI,CAAChB,IAAI,CAACsB,KAAK,CAAC,GAAG,CAAC,CAACK,GAAG,CAACwE,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC0G,IAAI,CAAC,EAAE,CAAC,GAAG;cAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtN,OAAA;cAAK6M,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC9M,OAAA;gBAAK6M,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAE,CAAA5J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhB,IAAI,KAAI;cAAM;gBAAAiL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnFtN,OAAA;gBAAK6M,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAE,CAAA5J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEb,IAAI,KAAI;cAAQ;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACNtN,OAAA,CAACP,IAAI;cAACyC,IAAI,EAAC,aAAa;cAACyL,IAAI,EAAE,EAAG;cAACd,SAAS,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EAERtM,kBAAkB,iBACjBhB,OAAA;YAAK6M,SAAS,EAAC,uGAAuG;YAAAC,QAAA,eACpH9M,OAAA;cAAK6M,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB9M,OAAA;gBAAK6M,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C9M,OAAA;kBAAK6M,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAAE,CAAA5J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhB,IAAI,KAAI;gBAAM;kBAAAiL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3EtN,OAAA;kBAAK6M,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAE,CAAA5J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEf,KAAK,KAAI;gBAAkB;kBAAAgL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtFtN,OAAA;kBAAM6M,SAAS,EAAE,+CAA+CF,iBAAiB,CAACvM,QAAQ,CAAC,EAAG;kBAAA0M,QAAA,EAC3F1M,QAAQ,CAACsN,MAAM,CAAC,CAAC,CAAC,CAACsB,WAAW,CAAC,CAAC,GAAG5O,QAAQ,CAAC6O,KAAK,CAAC,CAAC;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNtN,OAAA;gBAAK6M,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB9M,OAAA,CAACV,IAAI;kBACHyN,EAAE,EAAC,wBAAwB;kBAC3BF,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAE7H9M,OAAA,CAACP,IAAI;oBAACyC,IAAI,EAAC,MAAM;oBAACyL,IAAI,EAAE;kBAAG;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9BtN,OAAA;oBAAA8M,QAAA,EAAM;kBAAgB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACPtN,OAAA;kBAAQ6M,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBACnI9M,OAAA,CAACP,IAAI;oBAACyC,IAAI,EAAC,MAAM;oBAACyL,IAAI,EAAE;kBAAG;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9BtN,OAAA;oBAAA8M,QAAA,EAAM;kBAAa;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACTtN,OAAA;kBAAQ6M,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBACnI9M,OAAA,CAACP,IAAI;oBAACyC,IAAI,EAAC,YAAY;oBAACyL,IAAI,EAAE;kBAAG;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpCtN,OAAA;oBAAA8M,QAAA,EAAM;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACTtN,OAAA;kBAAK6M,SAAS,EAAC;gBAA6B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDtN,OAAA;kBACEyN,OAAO,EAAEjB,YAAa;kBACtBK,SAAS,EAAC,kHAAkH;kBAAAC,QAAA,gBAE5H9M,OAAA,CAACP,IAAI;oBAACyC,IAAI,EAAC,QAAQ;oBAACyL,IAAI,EAAE;kBAAG;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChCtN,OAAA;oBAAA8M,QAAA,EAAM;kBAAQ;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNtN,OAAA,CAACN,MAAM;UACL8N,OAAO,EAAC,OAAO;UACfG,IAAI,EAAC,MAAM;UACXF,OAAO,EAAEA,CAAA,KAAMtM,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UACtD2L,SAAS,EAAC,WAAW;UAAAC,QAAA,eAErB9M,OAAA,CAACP,IAAI;YAACyC,IAAI,EAAEhB,gBAAgB,GAAG,GAAG,GAAG,MAAO;YAACyM,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpM,gBAAgB,iBACflB,OAAA;MAAKuN,GAAG,EAAExL,aAAc;MAAC8K,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC9E9M,OAAA;QAAK6M,SAAS,EAAC,qBAAqB;QAAAC,QAAA,GAEjC1M,QAAQ,KAAK,OAAO,iBACnBJ,OAAA;UACEyN,OAAO,EAAEA,CAAA,KAAM;YACbzB,wBAAwB,CAAC,CAAC;YAC1B7K,mBAAmB,CAAC,KAAK,CAAC;UAC5B,CAAE;UACF0L,SAAS,EAAC,0JAA0J;UAAAC,QAAA,gBAEpK9M,OAAA,CAACP,IAAI;YAACyC,IAAI,EAAC,WAAW;YAACyL,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCtN,OAAA;YAAA8M,QAAA,EAAM;UAAgB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACT,EAGA7F,YAAY,CAACV,iBAAiB,iBAC7B/G,OAAA;UACEyN,OAAO,EAAEA,CAAA,KAAM;YACb9L,QAAQ,CAAC,qBAAqB,CAAC;YAC/BR,mBAAmB,CAAC,KAAK,CAAC;UAC5B,CAAE;UACF0L,SAAS,EAAC,qJAAqJ;UAAAC,QAAA,gBAE/J9M,OAAA,CAACP,IAAI;YAACyC,IAAI,EAAC,MAAM;YAACyL,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BtN,OAAA;YAAA8M,QAAA,EAAM;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACT,EAEA9F,eAAe,CAAC3D,GAAG,CAAE6C,IAAI,iBACxB1G,OAAA,CAACV,IAAI;UAEHyN,EAAE,EAAErG,IAAI,CAACP,IAAK;UACdsH,OAAO,EAAEA,CAAA,KAAMtM,mBAAmB,CAAC,KAAK,CAAE;UAC1C0L,SAAS,EAAE,yFACTjC,YAAY,CAAClE,IAAI,CAACP,IAAI,CAAC,GACnB,oCAAoC,GACpC,4DAA4D,EAC/D;UAAA2G,QAAA,gBAEH9M,OAAA,CAACP,IAAI;YAACyC,IAAI,EAAEwE,IAAI,CAACN,IAAK;YAACuH,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCtN,OAAA;YAAA8M,QAAA,EAAOpG,IAAI,CAACR;UAAK;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAVpB5G,IAAI,CAACP,IAAI;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDtN,OAAA,CAACH,kBAAkB;MACjBqP,MAAM,EAAE5N,wBAAyB;MACjC6N,OAAO,EAAEA,CAAA,KAAM5N,2BAA2B,CAAC,KAAK,CAAE;MAClD6N,eAAe,EAAExD,yBAA0B;MAC3CyD,cAAc,EAAE/M,YAAY,CAAC4B,EAAE,IAAI,CAAE;MACrCoL,gBAAgB,EAAEhN,YAAY,CAACJ;IAAK;MAAAiL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAGFtN,OAAA,CAACF,uBAAuB;MACtBoP,MAAM,EAAE1N,6BAA8B;MACtC2N,OAAO,EAAEA,CAAA,KAAM1N,gCAAgC,CAAC,KAAK,CAAE;MACvD8N,oBAAoB,EAAEtD;IAA+B;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;AAAC7M,EAAA,CAtsCIN,eAAe;EAAA,QAQFZ,WAAW,EACXC,WAAW;AAAA;AAAAgQ,EAAA,GATxBrP,eAAe;AAwsCrB,eAAeA,eAAe;AAAC,IAAAqP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}