{"ast": null, "code": "// src/utils/apiService.js - Real Backend Integration\n\n// Simulate API delay\nconst delay = ms => new Promise(resolve => setTimeout(resolve, ms));\n\n// Helper function to get headers with authentication (not used in mock mode)\n// const getAuthHeaders = (organizationId = null) => {\n//   const token = localStorage.getItem('accessToken');\n//   const headers = {\n//     'Content-Type': 'application/json',\n//     ...(organizationId && { 'X-Organization-ID': organizationId })\n//   };\n\n//   if (token) {\n//     headers['Authorization'] = `Bearer ${token}`;\n//   }\n\n//   return headers;\n// };\n\n// Helper function to handle API responses (not used in mock mode)\n// const handleResponse = async (response) => {\n//   const result = await response.json();\n\n//   if (!response.ok) {\n//     throw new Error(result.error?.message || result.message || 'API request failed');\n//   }\n\n//   return result;\n// };\n\nconst apiService = {\n  // Organizations (Use organizationService.js instead)\n  // organizations: {\n  //   // Commented out - use organizationService.js for organization operations\n  // },\n\n  // All organization methods commented out - use organizationService.js instead\n\n  // Projects (Real Backend Integration)\n  projects: {\n    getAll: async organizationId => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        const result = await realApiService.projects.getAll(organizationId);\n        console.log('Projects loaded successfully:', result);\n        return result || [];\n      } catch (error) {\n        console.error('Failed to fetch projects:', error);\n        return []; // Return empty array instead of mock data\n      }\n    },\n    create: async (organizationId, projectData) => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        const result = await realApiService.projects.create(organizationId, projectData);\n        console.log('Project created successfully:', result);\n        return result;\n      } catch (error) {\n        console.error('Failed to create project:', error);\n        throw error; // Don't use mock fallback, let the error bubble up\n      }\n    },\n    getById: async id => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        return await realApiService.projects.getById(id);\n      } catch (error) {\n        console.error('Failed to fetch project:', error);\n        throw error; // Don't use mock fallback\n      }\n    },\n    update: async (id, updateData) => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        return await realApiService.projects.update(id, updateData);\n      } catch (error) {\n        console.error('Failed to update project:', error);\n        throw error; // Don't use mock fallback\n      }\n    },\n    delete: async id => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        return await realApiService.projects.delete(id);\n      } catch (error) {\n        console.error('Failed to delete project:', error);\n        throw error; // Don't use mock fallback\n      }\n    }\n  },\n  // Boards (MOCK - Temporarily disabled)\n  boards: {\n    getByProject: async projectId => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Return mock boards data\n        return [{\n          id: 'board-1',\n          name: 'Project Board',\n          project_id: projectId,\n          columns: []\n        }];\n      } catch (error) {\n        console.error('Failed to fetch boards:', error);\n        throw error;\n      }\n    },\n    getById: async id => {\n      try {\n        // Simulate API delay\n        await delay(200);\n\n        // Return mock board data\n        return {\n          id: id,\n          name: 'Mock Board',\n          project_id: 'proj-1',\n          columns: []\n        };\n      } catch (error) {\n        console.error('Failed to fetch board:', error);\n        throw error;\n      }\n    },\n    create: async (projectId, boardData) => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Return mock created board\n        return {\n          id: `board-${Date.now()}`,\n          ...boardData,\n          project_id: projectId,\n          created_at: new Date().toISOString()\n        };\n      } catch (error) {\n        console.error('Failed to create board:', error);\n        throw error;\n      }\n    },\n    update: async (id, updateData) => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Return mock updated board\n        return {\n          id: id,\n          ...updateData,\n          updated_at: new Date().toISOString()\n        };\n      } catch (error) {\n        console.error('Failed to update board:', error);\n        throw error;\n      }\n    },\n    delete: async id => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Return success response\n        return {\n          success: true,\n          id: id\n        };\n      } catch (error) {\n        console.error('Failed to delete board:', error);\n        throw error;\n      }\n    }\n  },\n  // Users (MOCK - Temporarily disabled)\n  users: {\n    getProfile: async () => {\n      try {\n        // Simulate API delay\n        await delay(200);\n\n        // Return mock user profile\n        const currentUser = localStorage.getItem('currentUser');\n        if (currentUser) {\n          return JSON.parse(currentUser);\n        }\n        return {\n          id: 'user-1',\n          email: '<EMAIL>',\n          first_name: 'Demo',\n          last_name: 'User'\n        };\n      } catch (error) {\n        console.error('Failed to fetch user profile:', error);\n        throw error;\n      }\n    },\n    updateProfile: async updateData => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Update localStorage\n        const currentUser = localStorage.getItem('currentUser');\n        if (currentUser) {\n          const userData = JSON.parse(currentUser);\n          const updatedUser = {\n            ...userData,\n            ...updateData\n          };\n          localStorage.setItem('currentUser', JSON.stringify(updatedUser));\n          return updatedUser;\n        }\n        return updateData;\n      } catch (error) {\n        console.error('Failed to update user profile:', error);\n        throw error;\n      }\n    },\n    uploadAvatar: async file => {\n      try {\n        // Simulate API delay\n        await delay(500);\n\n        // Mock avatar upload - return a fake URL\n        const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`;\n        return {\n          avatar_url: avatarUrl,\n          success: true\n        };\n      } catch (error) {\n        console.error('Failed to upload avatar:', error);\n        throw error;\n      }\n    }\n  },\n  // Organization API methods\n  organizations: {\n    // Create new organization\n    create: async (orgData, logoFile = null) => {\n      try {\n        // Import organizationService dynamically to avoid circular imports\n        const {\n          createOrganization\n        } = await import('./organizationService');\n        return await createOrganization(orgData, logoFile);\n      } catch (error) {\n        console.error('Failed to create organization:', error);\n        throw error;\n      }\n    },\n    // Get all organizations\n    getAll: async () => {\n      try {\n        const {\n          getOrganizations\n        } = await import('./organizationService');\n        return await getOrganizations();\n      } catch (error) {\n        console.error('Failed to get organizations:', error);\n        throw error;\n      }\n    },\n    // Get organization by ID\n    getById: async id => {\n      try {\n        const {\n          getOrganizationById\n        } = await import('./organizationService');\n        return await getOrganizationById(id);\n      } catch (error) {\n        console.error('Failed to get organization:', error);\n        throw error;\n      }\n    },\n    // Update organization\n    update: async (id, orgData, logoFile = null) => {\n      try {\n        const {\n          updateOrganization\n        } = await import('./organizationService');\n        return await updateOrganization(id, orgData, logoFile);\n      } catch (error) {\n        console.error('Failed to update organization:', error);\n        throw error;\n      }\n    },\n    // Upload organization logo\n    uploadLogo: async (id, logoFile) => {\n      try {\n        const {\n          uploadOrganizationLogo\n        } = await import('./organizationService');\n        return await uploadOrganizationLogo(id, logoFile);\n      } catch (error) {\n        console.error('Failed to upload organization logo:', error);\n        throw error;\n      }\n    },\n    // Delete organization logo\n    deleteLogo: async id => {\n      try {\n        const {\n          deleteOrganizationLogo\n        } = await import('./organizationService');\n        return await deleteOrganizationLogo(id);\n      } catch (error) {\n        console.error('Failed to delete organization logo:', error);\n        throw error;\n      }\n    }\n  },\n  // Analytics methods\n  getUserActivityAnalytics: async (period = '30d') => {\n    await delay(500);\n    return {\n      totalUsers: 24,\n      activeUsers: 18,\n      newUsers: 3,\n      userGrowth: 12.5,\n      dailyActiveUsers: [{\n        date: '2024-01-01',\n        users: 15\n      }, {\n        date: '2024-01-02',\n        users: 18\n      }, {\n        date: '2024-01-03',\n        users: 16\n      }, {\n        date: '2024-01-04',\n        users: 20\n      }, {\n        date: '2024-01-05',\n        users: 22\n      }, {\n        date: '2024-01-06',\n        users: 19\n      }, {\n        date: '2024-01-07',\n        users: 24\n      }]\n    };\n  },\n  getOrganizationPerformance: async (period = '30d') => {\n    await delay(500);\n    return {\n      totalProjects: 12,\n      completedProjects: 8,\n      activeProjects: 4,\n      completionRate: 66.7,\n      teamProductivity: 78,\n      performanceMetrics: [{\n        metric: 'Task Completion Rate',\n        value: 85,\n        trend: 'up'\n      }, {\n        metric: 'Team Collaboration',\n        value: 92,\n        trend: 'up'\n      }, {\n        metric: 'Project Delivery',\n        value: 78,\n        trend: 'down'\n      }, {\n        metric: 'Resource Utilization',\n        value: 88,\n        trend: 'up'\n      }]\n    };\n  },\n  getProjectStatistics: async (period = '30d') => {\n    await delay(500);\n    return {\n      totalTasks: 156,\n      completedTasks: 124,\n      inProgressTasks: 24,\n      overdueTasks: 8,\n      taskCompletionRate: 79.5,\n      averageTaskDuration: 3.2\n    };\n  },\n  getUsageAnalytics: async (period = '30d') => {\n    await delay(500);\n    return {\n      totalSessions: 342,\n      averageSessionDuration: 28,\n      pageViews: 1456,\n      bounceRate: 23,\n      mostUsedFeatures: [{\n        feature: 'Kanban Board',\n        usage: 89\n      }, {\n        feature: 'Team Chat',\n        usage: 76\n      }, {\n        feature: 'File Sharing',\n        usage: 65\n      }, {\n        feature: 'Time Tracking',\n        usage: 54\n      }]\n    };\n  },\n  // Billing methods\n  getSubscriptionDetails: async () => {\n    await delay(500);\n    return {\n      plan: 'Professional',\n      status: 'active',\n      price: 29.99,\n      currency: 'USD',\n      billingCycle: 'monthly',\n      nextBillingDate: '2024-02-15',\n      seats: 25,\n      usedSeats: 18\n    };\n  },\n  getPaymentHistory: async () => {\n    await delay(500);\n    return [{\n      id: 'inv_001',\n      date: '2024-01-15',\n      amount: 29.99,\n      status: 'paid',\n      description: 'Professional Plan - Monthly',\n      downloadUrl: '#'\n    }, {\n      id: 'inv_002',\n      date: '2023-12-15',\n      amount: 29.99,\n      status: 'paid',\n      description: 'Professional Plan - Monthly',\n      downloadUrl: '#'\n    }];\n  },\n  getUsageBilling: async () => {\n    await delay(500);\n    return {\n      currentPeriod: {\n        start: '2024-01-15',\n        end: '2024-02-15'\n      },\n      metrics: [{\n        name: 'Active Users',\n        current: 18,\n        limit: 25,\n        unit: 'users'\n      }, {\n        name: 'Projects',\n        current: 12,\n        limit: 'unlimited',\n        unit: 'projects'\n      }, {\n        name: 'Storage',\n        current: 2.4,\n        limit: 100,\n        unit: 'GB'\n      }, {\n        name: 'API Calls',\n        current: 1250,\n        limit: 10000,\n        unit: 'calls'\n      }]\n    };\n  },\n  getAvailablePlans: async () => {\n    await delay(500);\n    return [{\n      id: 'starter',\n      name: 'Starter',\n      price: 9.99,\n      currency: 'USD',\n      billingCycle: 'monthly',\n      features: ['Up to 5 users', '10 projects', '5GB storage'],\n      current: false\n    }, {\n      id: 'professional',\n      name: 'Professional',\n      price: 29.99,\n      currency: 'USD',\n      billingCycle: 'monthly',\n      features: ['Up to 25 users', 'Unlimited projects', '100GB storage'],\n      current: true,\n      popular: true\n    }, {\n      id: 'enterprise',\n      name: 'Enterprise',\n      price: 99.99,\n      currency: 'USD',\n      billingCycle: 'monthly',\n      features: ['Unlimited users', 'Unlimited projects', '1TB storage'],\n      current: false\n    }];\n  }\n};\nexport default apiService;", "map": {"version": 3, "names": ["delay", "ms", "Promise", "resolve", "setTimeout", "apiService", "projects", "getAll", "organizationId", "realApiService", "default", "result", "console", "log", "error", "create", "projectData", "getById", "id", "update", "updateData", "delete", "boards", "getByProject", "projectId", "name", "project_id", "columns", "boardData", "Date", "now", "created_at", "toISOString", "updated_at", "success", "users", "getProfile", "currentUser", "localStorage", "getItem", "JSON", "parse", "email", "first_name", "last_name", "updateProfile", "userData", "updatedUser", "setItem", "stringify", "uploadAvatar", "file", "avatarUrl", "avatar_url", "organizations", "orgData", "logoFile", "createOrganization", "getOrganizations", "getOrganizationById", "updateOrganization", "uploadLogo", "uploadOrganizationLogo", "deleteLogo", "deleteOrganizationLogo", "getUserActivityAnalytics", "period", "totalUsers", "activeUsers", "newUsers", "userGrowth", "dailyActiveUsers", "date", "getOrganizationPerformance", "totalProjects", "completedProjects", "activeProjects", "completionRate", "teamProductivity", "performanceMetrics", "metric", "value", "trend", "getProjectStatistics", "totalTasks", "completedTasks", "inProgressTasks", "overdueTasks", "taskCompletionRate", "averageTaskDuration", "getUsageAnalytics", "totalSessions", "averageSessionDuration", "pageViews", "bounceRate", "mostUsedFeatures", "feature", "usage", "getSubscriptionDetails", "plan", "status", "price", "currency", "billingCycle", "nextBillingDate", "seats", "usedSeats", "getPaymentHistory", "amount", "description", "downloadUrl", "getUsageBilling", "currentPeriod", "start", "end", "metrics", "current", "limit", "unit", "getAvailablePlans", "features", "popular"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/utils/apiService.js"], "sourcesContent": ["// src/utils/apiService.js - Real Backend Integration\n\n// Simulate API delay\nconst delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));\n\n// Helper function to get headers with authentication (not used in mock mode)\n// const getAuthHeaders = (organizationId = null) => {\n//   const token = localStorage.getItem('accessToken');\n//   const headers = {\n//     'Content-Type': 'application/json',\n//     ...(organizationId && { 'X-Organization-ID': organizationId })\n//   };\n\n//   if (token) {\n//     headers['Authorization'] = `Bearer ${token}`;\n//   }\n\n//   return headers;\n// };\n\n// Helper function to handle API responses (not used in mock mode)\n// const handleResponse = async (response) => {\n//   const result = await response.json();\n\n//   if (!response.ok) {\n//     throw new Error(result.error?.message || result.message || 'API request failed');\n//   }\n\n//   return result;\n// };\n\nconst apiService = {\n  // Organizations (Use organizationService.js instead)\n  // organizations: {\n  //   // Commented out - use organizationService.js for organization operations\n  // },\n\n    // All organization methods commented out - use organizationService.js instead\n\n  // Projects (Real Backend Integration)\n  projects: {\n    getAll: async (organizationId) => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        const result = await realApiService.projects.getAll(organizationId);\n        console.log('Projects loaded successfully:', result);\n        return result || [];\n      } catch (error) {\n        console.error('Failed to fetch projects:', error);\n        return []; // Return empty array instead of mock data\n      }\n    },\n\n    create: async (organizationId, projectData) => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        const result = await realApiService.projects.create(organizationId, projectData);\n        console.log('Project created successfully:', result);\n        return result;\n      } catch (error) {\n        console.error('Failed to create project:', error);\n        throw error; // Don't use mock fallback, let the error bubble up\n      }\n    },\n\n    getById: async (id) => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        return await realApiService.projects.getById(id);\n      } catch (error) {\n        console.error('Failed to fetch project:', error);\n        throw error; // Don't use mock fallback\n      }\n    },\n\n    update: async (id, updateData) => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        return await realApiService.projects.update(id, updateData);\n      } catch (error) {\n        console.error('Failed to update project:', error);\n        throw error; // Don't use mock fallback\n      }\n    },\n\n    delete: async (id) => {\n      try {\n        // Use real API service\n        const realApiService = (await import('./realApiService')).default;\n        return await realApiService.projects.delete(id);\n      } catch (error) {\n        console.error('Failed to delete project:', error);\n        throw error; // Don't use mock fallback\n      }\n    }\n  },\n\n  // Boards (MOCK - Temporarily disabled)\n  boards: {\n    getByProject: async (projectId) => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Return mock boards data\n        return [\n          {\n            id: 'board-1',\n            name: 'Project Board',\n            project_id: projectId,\n            columns: []\n          }\n        ];\n      } catch (error) {\n        console.error('Failed to fetch boards:', error);\n        throw error;\n      }\n    },\n\n    getById: async (id) => {\n      try {\n        // Simulate API delay\n        await delay(200);\n\n        // Return mock board data\n        return {\n          id: id,\n          name: 'Mock Board',\n          project_id: 'proj-1',\n          columns: []\n        };\n      } catch (error) {\n        console.error('Failed to fetch board:', error);\n        throw error;\n      }\n    },\n\n    create: async (projectId, boardData) => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Return mock created board\n        return {\n          id: `board-${Date.now()}`,\n          ...boardData,\n          project_id: projectId,\n          created_at: new Date().toISOString()\n        };\n      } catch (error) {\n        console.error('Failed to create board:', error);\n        throw error;\n      }\n    },\n\n    update: async (id, updateData) => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Return mock updated board\n        return {\n          id: id,\n          ...updateData,\n          updated_at: new Date().toISOString()\n        };\n      } catch (error) {\n        console.error('Failed to update board:', error);\n        throw error;\n      }\n    },\n\n    delete: async (id) => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Return success response\n        return { success: true, id: id };\n      } catch (error) {\n        console.error('Failed to delete board:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Users (MOCK - Temporarily disabled)\n  users: {\n    getProfile: async () => {\n      try {\n        // Simulate API delay\n        await delay(200);\n\n        // Return mock user profile\n        const currentUser = localStorage.getItem('currentUser');\n        if (currentUser) {\n          return JSON.parse(currentUser);\n        }\n\n        return {\n          id: 'user-1',\n          email: '<EMAIL>',\n          first_name: 'Demo',\n          last_name: 'User'\n        };\n      } catch (error) {\n        console.error('Failed to fetch user profile:', error);\n        throw error;\n      }\n    },\n\n    updateProfile: async (updateData) => {\n      try {\n        // Simulate API delay\n        await delay(300);\n\n        // Update localStorage\n        const currentUser = localStorage.getItem('currentUser');\n        if (currentUser) {\n          const userData = JSON.parse(currentUser);\n          const updatedUser = { ...userData, ...updateData };\n          localStorage.setItem('currentUser', JSON.stringify(updatedUser));\n          return updatedUser;\n        }\n\n        return updateData;\n      } catch (error) {\n        console.error('Failed to update user profile:', error);\n        throw error;\n      }\n    },\n\n    uploadAvatar: async (file) => {\n      try {\n        // Simulate API delay\n        await delay(500);\n\n        // Mock avatar upload - return a fake URL\n        const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`;\n\n        return {\n          avatar_url: avatarUrl,\n          success: true\n        };\n      } catch (error) {\n        console.error('Failed to upload avatar:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Organization API methods\n  organizations: {\n    // Create new organization\n    create: async (orgData, logoFile = null) => {\n      try {\n        // Import organizationService dynamically to avoid circular imports\n        const { createOrganization } = await import('./organizationService');\n        return await createOrganization(orgData, logoFile);\n      } catch (error) {\n        console.error('Failed to create organization:', error);\n        throw error;\n      }\n    },\n\n    // Get all organizations\n    getAll: async () => {\n      try {\n        const { getOrganizations } = await import('./organizationService');\n        return await getOrganizations();\n      } catch (error) {\n        console.error('Failed to get organizations:', error);\n        throw error;\n      }\n    },\n\n    // Get organization by ID\n    getById: async (id) => {\n      try {\n        const { getOrganizationById } = await import('./organizationService');\n        return await getOrganizationById(id);\n      } catch (error) {\n        console.error('Failed to get organization:', error);\n        throw error;\n      }\n    },\n\n    // Update organization\n    update: async (id, orgData, logoFile = null) => {\n      try {\n        const { updateOrganization } = await import('./organizationService');\n        return await updateOrganization(id, orgData, logoFile);\n      } catch (error) {\n        console.error('Failed to update organization:', error);\n        throw error;\n      }\n    },\n\n    // Upload organization logo\n    uploadLogo: async (id, logoFile) => {\n      try {\n        const { uploadOrganizationLogo } = await import('./organizationService');\n        return await uploadOrganizationLogo(id, logoFile);\n      } catch (error) {\n        console.error('Failed to upload organization logo:', error);\n        throw error;\n      }\n    },\n\n    // Delete organization logo\n    deleteLogo: async (id) => {\n      try {\n        const { deleteOrganizationLogo } = await import('./organizationService');\n        return await deleteOrganizationLogo(id);\n      } catch (error) {\n        console.error('Failed to delete organization logo:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Analytics methods\n  getUserActivityAnalytics: async (period = '30d') => {\n    await delay(500);\n    return {\n      totalUsers: 24,\n      activeUsers: 18,\n      newUsers: 3,\n      userGrowth: 12.5,\n      dailyActiveUsers: [\n        { date: '2024-01-01', users: 15 },\n        { date: '2024-01-02', users: 18 },\n        { date: '2024-01-03', users: 16 },\n        { date: '2024-01-04', users: 20 },\n        { date: '2024-01-05', users: 22 },\n        { date: '2024-01-06', users: 19 },\n        { date: '2024-01-07', users: 24 }\n      ]\n    };\n  },\n\n  getOrganizationPerformance: async (period = '30d') => {\n    await delay(500);\n    return {\n      totalProjects: 12,\n      completedProjects: 8,\n      activeProjects: 4,\n      completionRate: 66.7,\n      teamProductivity: 78,\n      performanceMetrics: [\n        { metric: 'Task Completion Rate', value: 85, trend: 'up' },\n        { metric: 'Team Collaboration', value: 92, trend: 'up' },\n        { metric: 'Project Delivery', value: 78, trend: 'down' },\n        { metric: 'Resource Utilization', value: 88, trend: 'up' }\n      ]\n    };\n  },\n\n  getProjectStatistics: async (period = '30d') => {\n    await delay(500);\n    return {\n      totalTasks: 156,\n      completedTasks: 124,\n      inProgressTasks: 24,\n      overdueTasks: 8,\n      taskCompletionRate: 79.5,\n      averageTaskDuration: 3.2\n    };\n  },\n\n  getUsageAnalytics: async (period = '30d') => {\n    await delay(500);\n    return {\n      totalSessions: 342,\n      averageSessionDuration: 28,\n      pageViews: 1456,\n      bounceRate: 23,\n      mostUsedFeatures: [\n        { feature: 'Kanban Board', usage: 89 },\n        { feature: 'Team Chat', usage: 76 },\n        { feature: 'File Sharing', usage: 65 },\n        { feature: 'Time Tracking', usage: 54 }\n      ]\n    };\n  },\n\n  // Billing methods\n  getSubscriptionDetails: async () => {\n    await delay(500);\n    return {\n      plan: 'Professional',\n      status: 'active',\n      price: 29.99,\n      currency: 'USD',\n      billingCycle: 'monthly',\n      nextBillingDate: '2024-02-15',\n      seats: 25,\n      usedSeats: 18\n    };\n  },\n\n  getPaymentHistory: async () => {\n    await delay(500);\n    return [\n      {\n        id: 'inv_001',\n        date: '2024-01-15',\n        amount: 29.99,\n        status: 'paid',\n        description: 'Professional Plan - Monthly',\n        downloadUrl: '#'\n      },\n      {\n        id: 'inv_002',\n        date: '2023-12-15',\n        amount: 29.99,\n        status: 'paid',\n        description: 'Professional Plan - Monthly',\n        downloadUrl: '#'\n      }\n    ];\n  },\n\n  getUsageBilling: async () => {\n    await delay(500);\n    return {\n      currentPeriod: {\n        start: '2024-01-15',\n        end: '2024-02-15'\n      },\n      metrics: [\n        { name: 'Active Users', current: 18, limit: 25, unit: 'users' },\n        { name: 'Projects', current: 12, limit: 'unlimited', unit: 'projects' },\n        { name: 'Storage', current: 2.4, limit: 100, unit: 'GB' },\n        { name: 'API Calls', current: 1250, limit: 10000, unit: 'calls' }\n      ]\n    };\n  },\n\n  getAvailablePlans: async () => {\n    await delay(500);\n    return [\n      {\n        id: 'starter',\n        name: 'Starter',\n        price: 9.99,\n        currency: 'USD',\n        billingCycle: 'monthly',\n        features: ['Up to 5 users', '10 projects', '5GB storage'],\n        current: false\n      },\n      {\n        id: 'professional',\n        name: 'Professional',\n        price: 29.99,\n        currency: 'USD',\n        billingCycle: 'monthly',\n        features: ['Up to 25 users', 'Unlimited projects', '100GB storage'],\n        current: true,\n        popular: true\n      },\n      {\n        id: 'enterprise',\n        name: 'Enterprise',\n        price: 99.99,\n        currency: 'USD',\n        billingCycle: 'monthly',\n        features: ['Unlimited users', 'Unlimited projects', '1TB storage'],\n        current: false\n      }\n    ];\n  }\n};\n\nexport default apiService;\n"], "mappings": "AAAA;;AAEA;AACA,MAAMA,KAAK,GAAIC,EAAE,IAAK,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,MAAMI,UAAU,GAAG;EACjB;EACA;EACA;EACA;;EAEE;;EAEF;EACAC,QAAQ,EAAE;IACRC,MAAM,EAAE,MAAOC,cAAc,IAAK;MAChC,IAAI;QACF;QACA,MAAMC,cAAc,GAAG,CAAC,MAAM,MAAM,CAAC,kBAAkB,CAAC,EAAEC,OAAO;QACjE,MAAMC,MAAM,GAAG,MAAMF,cAAc,CAACH,QAAQ,CAACC,MAAM,CAACC,cAAc,CAAC;QACnEI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,MAAM,CAAC;QACpD,OAAOA,MAAM,IAAI,EAAE;MACrB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,OAAO,EAAE,CAAC,CAAC;MACb;IACF,CAAC;IAEDC,MAAM,EAAE,MAAAA,CAAOP,cAAc,EAAEQ,WAAW,KAAK;MAC7C,IAAI;QACF;QACA,MAAMP,cAAc,GAAG,CAAC,MAAM,MAAM,CAAC,kBAAkB,CAAC,EAAEC,OAAO;QACjE,MAAMC,MAAM,GAAG,MAAMF,cAAc,CAACH,QAAQ,CAACS,MAAM,CAACP,cAAc,EAAEQ,WAAW,CAAC;QAChFJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,MAAM,CAAC;QACpD,OAAOA,MAAM;MACf,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK,CAAC,CAAC;MACf;IACF,CAAC;IAEDG,OAAO,EAAE,MAAOC,EAAE,IAAK;MACrB,IAAI;QACF;QACA,MAAMT,cAAc,GAAG,CAAC,MAAM,MAAM,CAAC,kBAAkB,CAAC,EAAEC,OAAO;QACjE,OAAO,MAAMD,cAAc,CAACH,QAAQ,CAACW,OAAO,CAACC,EAAE,CAAC;MAClD,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK,CAAC,CAAC;MACf;IACF,CAAC;IAEDK,MAAM,EAAE,MAAAA,CAAOD,EAAE,EAAEE,UAAU,KAAK;MAChC,IAAI;QACF;QACA,MAAMX,cAAc,GAAG,CAAC,MAAM,MAAM,CAAC,kBAAkB,CAAC,EAAEC,OAAO;QACjE,OAAO,MAAMD,cAAc,CAACH,QAAQ,CAACa,MAAM,CAACD,EAAE,EAAEE,UAAU,CAAC;MAC7D,CAAC,CAAC,OAAON,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK,CAAC,CAAC;MACf;IACF,CAAC;IAEDO,MAAM,EAAE,MAAOH,EAAE,IAAK;MACpB,IAAI;QACF;QACA,MAAMT,cAAc,GAAG,CAAC,MAAM,MAAM,CAAC,kBAAkB,CAAC,EAAEC,OAAO;QACjE,OAAO,MAAMD,cAAc,CAACH,QAAQ,CAACe,MAAM,CAACH,EAAE,CAAC;MACjD,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK,CAAC,CAAC;MACf;IACF;EACF,CAAC;EAED;EACAQ,MAAM,EAAE;IACNC,YAAY,EAAE,MAAOC,SAAS,IAAK;MACjC,IAAI;QACF;QACA,MAAMxB,KAAK,CAAC,GAAG,CAAC;;QAEhB;QACA,OAAO,CACL;UACEkB,EAAE,EAAE,SAAS;UACbO,IAAI,EAAE,eAAe;UACrBC,UAAU,EAAEF,SAAS;UACrBG,OAAO,EAAE;QACX,CAAC,CACF;MACH,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF,CAAC;IAEDG,OAAO,EAAE,MAAOC,EAAE,IAAK;MACrB,IAAI;QACF;QACA,MAAMlB,KAAK,CAAC,GAAG,CAAC;;QAEhB;QACA,OAAO;UACLkB,EAAE,EAAEA,EAAE;UACNO,IAAI,EAAE,YAAY;UAClBC,UAAU,EAAE,QAAQ;UACpBC,OAAO,EAAE;QACX,CAAC;MACH,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMA,KAAK;MACb;IACF,CAAC;IAEDC,MAAM,EAAE,MAAAA,CAAOS,SAAS,EAAEI,SAAS,KAAK;MACtC,IAAI;QACF;QACA,MAAM5B,KAAK,CAAC,GAAG,CAAC;;QAEhB;QACA,OAAO;UACLkB,EAAE,EAAE,SAASW,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UACzB,GAAGF,SAAS;UACZF,UAAU,EAAEF,SAAS;UACrBO,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;QACrC,CAAC;MACH,CAAC,CAAC,OAAOlB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF,CAAC;IAEDK,MAAM,EAAE,MAAAA,CAAOD,EAAE,EAAEE,UAAU,KAAK;MAChC,IAAI;QACF;QACA,MAAMpB,KAAK,CAAC,GAAG,CAAC;;QAEhB;QACA,OAAO;UACLkB,EAAE,EAAEA,EAAE;UACN,GAAGE,UAAU;UACba,UAAU,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;QACrC,CAAC;MACH,CAAC,CAAC,OAAOlB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF,CAAC;IAEDO,MAAM,EAAE,MAAOH,EAAE,IAAK;MACpB,IAAI;QACF;QACA,MAAMlB,KAAK,CAAC,GAAG,CAAC;;QAEhB;QACA,OAAO;UAAEkC,OAAO,EAAE,IAAI;UAAEhB,EAAE,EAAEA;QAAG,CAAC;MAClC,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAqB,KAAK,EAAE;IACLC,UAAU,EAAE,MAAAA,CAAA,KAAY;MACtB,IAAI;QACF;QACA,MAAMpC,KAAK,CAAC,GAAG,CAAC;;QAEhB;QACA,MAAMqC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QACvD,IAAIF,WAAW,EAAE;UACf,OAAOG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;QAChC;QAEA,OAAO;UACLnB,EAAE,EAAE,QAAQ;UACZwB,KAAK,EAAE,kBAAkB;UACzBC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE;QACb,CAAC;MACH,CAAC,CAAC,OAAO9B,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,MAAMA,KAAK;MACb;IACF,CAAC;IAED+B,aAAa,EAAE,MAAOzB,UAAU,IAAK;MACnC,IAAI;QACF;QACA,MAAMpB,KAAK,CAAC,GAAG,CAAC;;QAEhB;QACA,MAAMqC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QACvD,IAAIF,WAAW,EAAE;UACf,MAAMS,QAAQ,GAAGN,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;UACxC,MAAMU,WAAW,GAAG;YAAE,GAAGD,QAAQ;YAAE,GAAG1B;UAAW,CAAC;UAClDkB,YAAY,CAACU,OAAO,CAAC,aAAa,EAAER,IAAI,CAACS,SAAS,CAACF,WAAW,CAAC,CAAC;UAChE,OAAOA,WAAW;QACpB;QAEA,OAAO3B,UAAU;MACnB,CAAC,CAAC,OAAON,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDoC,YAAY,EAAE,MAAOC,IAAI,IAAK;MAC5B,IAAI;QACF;QACA,MAAMnD,KAAK,CAAC,GAAG,CAAC;;QAEhB;QACA,MAAMoD,SAAS,GAAG,mDAAmDvB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAEjF,OAAO;UACLuB,UAAU,EAAED,SAAS;UACrBlB,OAAO,EAAE;QACX,CAAC;MACH,CAAC,CAAC,OAAOpB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAwC,aAAa,EAAE;IACb;IACAvC,MAAM,EAAE,MAAAA,CAAOwC,OAAO,EAAEC,QAAQ,GAAG,IAAI,KAAK;MAC1C,IAAI;QACF;QACA,MAAM;UAAEC;QAAmB,CAAC,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC;QACpE,OAAO,MAAMA,kBAAkB,CAACF,OAAO,EAAEC,QAAQ,CAAC;MACpD,CAAC,CAAC,OAAO1C,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAP,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF,MAAM;UAAEmD;QAAiB,CAAC,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC;QAClE,OAAO,MAAMA,gBAAgB,CAAC,CAAC;MACjC,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAG,OAAO,EAAE,MAAOC,EAAE,IAAK;MACrB,IAAI;QACF,MAAM;UAAEyC;QAAoB,CAAC,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC;QACrE,OAAO,MAAMA,mBAAmB,CAACzC,EAAE,CAAC;MACtC,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAK,MAAM,EAAE,MAAAA,CAAOD,EAAE,EAAEqC,OAAO,EAAEC,QAAQ,GAAG,IAAI,KAAK;MAC9C,IAAI;QACF,MAAM;UAAEI;QAAmB,CAAC,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC;QACpE,OAAO,MAAMA,kBAAkB,CAAC1C,EAAE,EAAEqC,OAAO,EAAEC,QAAQ,CAAC;MACxD,CAAC,CAAC,OAAO1C,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA+C,UAAU,EAAE,MAAAA,CAAO3C,EAAE,EAAEsC,QAAQ,KAAK;MAClC,IAAI;QACF,MAAM;UAAEM;QAAuB,CAAC,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC;QACxE,OAAO,MAAMA,sBAAsB,CAAC5C,EAAE,EAAEsC,QAAQ,CAAC;MACnD,CAAC,CAAC,OAAO1C,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAiD,UAAU,EAAE,MAAO7C,EAAE,IAAK;MACxB,IAAI;QACF,MAAM;UAAE8C;QAAuB,CAAC,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC;QACxE,OAAO,MAAMA,sBAAsB,CAAC9C,EAAE,CAAC;MACzC,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAmD,wBAAwB,EAAE,MAAAA,CAAOC,MAAM,GAAG,KAAK,KAAK;IAClD,MAAMlE,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MACLmE,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,IAAI;MAChBC,gBAAgB,EAAE,CAChB;QAAEC,IAAI,EAAE,YAAY;QAAErC,KAAK,EAAE;MAAG,CAAC,EACjC;QAAEqC,IAAI,EAAE,YAAY;QAAErC,KAAK,EAAE;MAAG,CAAC,EACjC;QAAEqC,IAAI,EAAE,YAAY;QAAErC,KAAK,EAAE;MAAG,CAAC,EACjC;QAAEqC,IAAI,EAAE,YAAY;QAAErC,KAAK,EAAE;MAAG,CAAC,EACjC;QAAEqC,IAAI,EAAE,YAAY;QAAErC,KAAK,EAAE;MAAG,CAAC,EACjC;QAAEqC,IAAI,EAAE,YAAY;QAAErC,KAAK,EAAE;MAAG,CAAC,EACjC;QAAEqC,IAAI,EAAE,YAAY;QAAErC,KAAK,EAAE;MAAG,CAAC;IAErC,CAAC;EACH,CAAC;EAEDsC,0BAA0B,EAAE,MAAAA,CAAOP,MAAM,GAAG,KAAK,KAAK;IACpD,MAAMlE,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MACL0E,aAAa,EAAE,EAAE;MACjBC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC;MACjBC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,kBAAkB,EAAE,CAClB;QAAEC,MAAM,EAAE,sBAAsB;QAAEC,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAC,EAC1D;QAAEF,MAAM,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAC,EACxD;QAAEF,MAAM,EAAE,kBAAkB;QAAEC,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAC,EACxD;QAAEF,MAAM,EAAE,sBAAsB;QAAEC,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAC;IAE9D,CAAC;EACH,CAAC;EAEDC,oBAAoB,EAAE,MAAAA,CAAOjB,MAAM,GAAG,KAAK,KAAK;IAC9C,MAAMlE,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MACLoF,UAAU,EAAE,GAAG;MACfC,cAAc,EAAE,GAAG;MACnBC,eAAe,EAAE,EAAE;MACnBC,YAAY,EAAE,CAAC;MACfC,kBAAkB,EAAE,IAAI;MACxBC,mBAAmB,EAAE;IACvB,CAAC;EACH,CAAC;EAEDC,iBAAiB,EAAE,MAAAA,CAAOxB,MAAM,GAAG,KAAK,KAAK;IAC3C,MAAMlE,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MACL2F,aAAa,EAAE,GAAG;MAClBC,sBAAsB,EAAE,EAAE;MAC1BC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,CAChB;QAAEC,OAAO,EAAE,cAAc;QAAEC,KAAK,EAAE;MAAG,CAAC,EACtC;QAAED,OAAO,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAG,CAAC,EACnC;QAAED,OAAO,EAAE,cAAc;QAAEC,KAAK,EAAE;MAAG,CAAC,EACtC;QAAED,OAAO,EAAE,eAAe;QAAEC,KAAK,EAAE;MAAG,CAAC;IAE3C,CAAC;EACH,CAAC;EAED;EACAC,sBAAsB,EAAE,MAAAA,CAAA,KAAY;IAClC,MAAMlG,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MACLmG,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,SAAS;MACvBC,eAAe,EAAE,YAAY;MAC7BC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE;IACb,CAAC;EACH,CAAC;EAEDC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,MAAM3G,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO,CACL;MACEkB,EAAE,EAAE,SAAS;MACbsD,IAAI,EAAE,YAAY;MAClBoC,MAAM,EAAE,KAAK;MACbR,MAAM,EAAE,MAAM;MACdS,WAAW,EAAE,6BAA6B;MAC1CC,WAAW,EAAE;IACf,CAAC,EACD;MACE5F,EAAE,EAAE,SAAS;MACbsD,IAAI,EAAE,YAAY;MAClBoC,MAAM,EAAE,KAAK;MACbR,MAAM,EAAE,MAAM;MACdS,WAAW,EAAE,6BAA6B;MAC1CC,WAAW,EAAE;IACf,CAAC,CACF;EACH,CAAC;EAEDC,eAAe,EAAE,MAAAA,CAAA,KAAY;IAC3B,MAAM/G,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MACLgH,aAAa,EAAE;QACbC,KAAK,EAAE,YAAY;QACnBC,GAAG,EAAE;MACP,CAAC;MACDC,OAAO,EAAE,CACP;QAAE1F,IAAI,EAAE,cAAc;QAAE2F,OAAO,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAQ,CAAC,EAC/D;QAAE7F,IAAI,EAAE,UAAU;QAAE2F,OAAO,EAAE,EAAE;QAAEC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAW,CAAC,EACvE;QAAE7F,IAAI,EAAE,SAAS;QAAE2F,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAK,CAAC,EACzD;QAAE7F,IAAI,EAAE,WAAW;QAAE2F,OAAO,EAAE,IAAI;QAAEC,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAQ,CAAC;IAErE,CAAC;EACH,CAAC;EAEDC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,MAAMvH,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO,CACL;MACEkB,EAAE,EAAE,SAAS;MACbO,IAAI,EAAE,SAAS;MACf4E,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,SAAS;MACvBiB,QAAQ,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,aAAa,CAAC;MACzDJ,OAAO,EAAE;IACX,CAAC,EACD;MACElG,EAAE,EAAE,cAAc;MAClBO,IAAI,EAAE,cAAc;MACpB4E,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,SAAS;MACvBiB,QAAQ,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,EAAE,eAAe,CAAC;MACnEJ,OAAO,EAAE,IAAI;MACbK,OAAO,EAAE;IACX,CAAC,EACD;MACEvG,EAAE,EAAE,YAAY;MAChBO,IAAI,EAAE,YAAY;MAClB4E,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,SAAS;MACvBiB,QAAQ,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,aAAa,CAAC;MAClEJ,OAAO,EAAE;IACX,CAAC,CACF;EACH;AACF,CAAC;AAED,eAAe/G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}