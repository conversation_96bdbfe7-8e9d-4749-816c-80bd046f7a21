{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\project-overview\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Button from '../../components/ui/Button';\nimport Icon from '../../components/AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectOverview = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    projectId: urlProjectId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n\n  // Real project data state\n  const [currentProject, setCurrentProject] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Load real project data\n  useEffect(() => {\n    const loadProjectData = async () => {\n      try {\n        var _location$state, _projectData$budget, _projectData$budget2, _projectData$budget3, _projectData$budget4, _projectData$team, _projectData$team2, _projectData$team3;\n        setLoading(true);\n\n        // Get project ID from URL params, route params, or location state\n        const urlParams = new URLSearchParams(location.search);\n        const projectId = urlProjectId || urlParams.get('id') || ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.projectId);\n        if (!projectId) {\n          setError('No project ID provided');\n          setLoading(false);\n          return;\n        }\n        console.log('Loading project data for ID:', projectId);\n\n        // Import apiService dynamically to avoid circular imports\n        const {\n          default: apiService\n        } = await import('../../utils/apiService');\n        const projectData = await apiService.projects.getById(projectId);\n        console.log('Loaded project data:', projectData);\n\n        // Transform the data to match the expected format\n        const transformedProject = {\n          id: projectData.id,\n          name: projectData.name || 'Untitled Project',\n          description: projectData.description || 'No description available',\n          status: projectData.status || 'active',\n          priority: projectData.priority || 'medium',\n          startDate: projectData.start_date || new Date().toISOString().split('T')[0],\n          endDate: projectData.due_date || new Date().toISOString().split('T')[0],\n          progress: projectData.progress || 0,\n          progressChange: '+0% this week',\n          // This would come from analytics\n          budget: {\n            allocated: ((_projectData$budget = projectData.budget) === null || _projectData$budget === void 0 ? void 0 : _projectData$budget.allocated) || 0,\n            spent: ((_projectData$budget2 = projectData.budget) === null || _projectData$budget2 === void 0 ? void 0 : _projectData$budget2.spent) || 0,\n            remaining: (((_projectData$budget3 = projectData.budget) === null || _projectData$budget3 === void 0 ? void 0 : _projectData$budget3.allocated) || 0) - (((_projectData$budget4 = projectData.budget) === null || _projectData$budget4 === void 0 ? void 0 : _projectData$budget4.spent) || 0)\n          },\n          team: {\n            totalMembers: ((_projectData$team = projectData.team) === null || _projectData$team === void 0 ? void 0 : _projectData$team.totalMembers) || 0,\n            activeMembers: ((_projectData$team2 = projectData.team) === null || _projectData$team2 === void 0 ? void 0 : _projectData$team2.activeMembers) || 0,\n            tasksCompleted: ((_projectData$team3 = projectData.team) === null || _projectData$team3 === void 0 ? void 0 : _projectData$team3.tasksCompleted) || 0\n          }\n        };\n        setCurrentProject(transformedProject);\n      } catch (error) {\n        var _location$state2, _location$state2$proj;\n        console.error('Failed to load project data:', error);\n        setError('Failed to load project data');\n\n        // Set fallback project data if API fails\n        const fallbackProject = {\n          id: 'fallback',\n          name: ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : (_location$state2$proj = _location$state2.project) === null || _location$state2$proj === void 0 ? void 0 : _location$state2$proj.name) || 'Project',\n          description: 'Unable to load project details',\n          status: 'active',\n          priority: 'medium',\n          startDate: new Date().toISOString().split('T')[0],\n          endDate: new Date().toISOString().split('T')[0],\n          progress: 0,\n          progressChange: '+0% this week',\n          budget: {\n            allocated: 0,\n            spent: 0,\n            remaining: 0\n          },\n          team: {\n            totalMembers: 0,\n            activeMembers: 0,\n            tasksCompleted: 0\n          }\n        };\n        setCurrentProject(fallbackProject);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProjectData();\n  }, [location.search, location.state, urlProjectId]);\n  const [userRole, setUserRole] = useState('member');\n  const [activeTab, setActiveTab] = useState('overview');\n\n  // Real team members data - will be empty for now since this is project-specific\n  const [teamMembers] = useState([]);\n  useEffect(() => {\n    // Load real user role from auth context or API\n    const loadUserData = async () => {\n      try {\n        // Import authService dynamically to avoid circular imports\n        const {\n          default: authService\n        } = await import('../../utils/authService');\n        const result = await authService.getCurrentUser();\n        if (result.data && result.data.user) {\n          const userData = result.data.user;\n          // Get user role from the user data\n          const role = userData.role || 'member';\n          console.log('User role loaded:', role);\n          setUserRole(role.toLowerCase());\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Fallback to member role\n        setUserRole('member');\n      }\n    };\n    loadUserData();\n  }, []);\n  const tabs = [{\n    id: 'overview',\n    label: 'Overview',\n    icon: 'BarChart3',\n    description: 'Project summary and key metrics',\n    roles: ['viewer', 'member', 'admin', 'owner']\n  }, {\n    id: 'tasks',\n    label: 'Tasks',\n    icon: 'CheckSquare',\n    description: 'Task management and tracking',\n    roles: ['viewer', 'member', 'admin', 'owner']\n  }, {\n    id: 'settings',\n    label: 'Settings',\n    icon: 'Settings',\n    description: 'Project configuration and permissions',\n    roles: ['admin', 'owner']\n  }];\n  const visibleTabs = tabs.filter(tab => tab.roles.includes(userRole));\n  const handleTabChange = tabId => {\n    setActiveTab(tabId);\n  };\n  const handleGoToBoard = () => {\n    navigate('/kanban-board', {\n      state: {\n        projectId: currentProject.id,\n        project: currentProject\n      }\n    });\n  };\n  const handleAddTask = () => {\n    // Navigate to kanban board with add task modal open\n    navigate('/kanban-board', {\n      state: {\n        projectId: currentProject.id,\n        project: currentProject,\n        openAddTask: true\n      }\n    });\n  };\n  const handleInviteMembers = () => {\n    // Open invite members modal\n    console.log('Opening invite members modal...');\n  };\n  const getStatusColor = status => {\n    if (!status) return 'bg-muted text-text-secondary';\n    switch (status.toLowerCase()) {\n      case 'good':\n        return 'bg-success/10 text-success';\n      case 'at risk':\n        return 'bg-warning/10 text-warning';\n      case 'delayed':\n        return 'bg-destructive/10 text-destructive';\n      default:\n        return 'bg-muted text-text-secondary';\n    }\n  };\n  const getPriorityColor = priority => {\n    if (!priority) return 'bg-muted text-text-secondary';\n    switch (priority.toLowerCase()) {\n      case 'high priority':\n        return 'bg-destructive/10 text-destructive';\n      case 'medium priority':\n        return 'bg-warning/10 text-warning';\n      case 'low priority':\n        return 'bg-success/10 text-success';\n      default:\n        return 'bg-muted text-text-secondary';\n    }\n  };\n\n  // Show loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        currentUser: user,\n        userRole: userRole\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"pt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Loader2\",\n                size: 32,\n                className: \"animate-spin mx-auto mb-4 text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary\",\n                children: \"Loading project data...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        currentUser: user,\n        userRole: userRole\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"pt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"AlertCircle\",\n                size: 32,\n                className: \"mx-auto mb-4 text-destructive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-text-primary mb-2\",\n                children: \"Error Loading Project\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary mb-4\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => window.location.reload(),\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show main content when project data is loaded\n  if (!currentProject) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        currentUser: user,\n        userRole: userRole\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"pt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"FolderX\",\n                size: 32,\n                className: \"mx-auto mb-4 text-text-secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-text-primary mb-2\",\n                children: \"Project Not Found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary mb-4\",\n                children: \"The requested project could not be found.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => navigate('/role-based-dashboard'),\n                children: \"Back to Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      currentUser: user,\n      userRole: userRole\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-border bg-card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4\",\n          children: /*#__PURE__*/_jsxDEV(Breadcrumb, {\n            items: breadcrumbItems\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-border bg-card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-2xl font-bold text-text-primary\",\n                  children: currentProject.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-md text-xs font-medium ${getStatusColor(currentProject.status)}`,\n                  children: currentProject.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary mb-4 max-w-3xl\",\n                children: currentProject.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-6 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Calendar\",\n                    size: 16,\n                    className: \"text-text-secondary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-text-secondary\",\n                    children: [currentProject.startDate, \" - \", currentProject.endDate]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Flag\",\n                    size: 16,\n                    className: \"text-text-secondary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded text-xs font-medium ${getPriorityColor(currentProject.priority)}`,\n                    children: currentProject.priority\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                onClick: () => navigate('/project-management'),\n                iconName: \"Settings\",\n                iconPosition: \"left\",\n                children: \"Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"default\",\n                onClick: handleAddTask,\n                iconName: \"Plus\",\n                iconPosition: \"left\",\n                children: \"Add Task\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-border bg-card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6\",\n          children: /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"flex space-x-8\",\n            children: visibleTabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleTabChange(tab.id),\n              className: `flex items-center space-x-2 px-1 py-4 border-b-2 font-medium text-sm transition-colors ${activeTab === tab.id ? 'border-primary text-primary' : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border'}`,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: tab.icon,\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: tab.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this)]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-6\",\n        children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card border border-border rounded-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-text-primary flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"TrendingUp\",\n                    size: 20,\n                    className: \"mr-2 text-primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this), \"Progress\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-bold text-text-primary\",\n                      children: [currentProject.progress, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-success font-medium\",\n                      children: currentProject.progressChange\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-muted rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-primary h-2 rounded-full transition-all duration-300\",\n                      style: {\n                        width: `${currentProject.progress}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-text-secondary mt-2\",\n                    children: \"On track for completion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card border border-border rounded-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-text-primary flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"DollarSign\",\n                    size: 20,\n                    className: \"mr-2 text-primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 23\n                  }, this), \"Budget\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-text-secondary\",\n                    children: \"Allocated\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: [\"$\", currentProject.budget.allocated.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-text-secondary\",\n                    children: \"Spent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: [\"$\", currentProject.budget.spent.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-text-secondary\",\n                    children: \"Remaining\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-success\",\n                    children: [\"$\", currentProject.budget.remaining.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-muted rounded-full h-2 mt-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-warning h-2 rounded-full\",\n                    style: {\n                      width: `${currentProject.budget.spent / currentProject.budget.allocated * 100}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-card border border-border rounded-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-text-primary flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Users\",\n                    size: 20,\n                    className: \"mr-2 text-primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this), \"Team\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-text-secondary\",\n                    children: \"Total Members\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: currentProject.team.totalMembers\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-text-secondary\",\n                    children: \"Active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: currentProject.team.activeMembers\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-text-secondary\",\n                    children: \"Tasks Completed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: currentProject.team.tasksCompleted\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline\",\n                  size: \"sm\",\n                  onClick: handleInviteMembers,\n                  iconName: \"UserPlus\",\n                  iconPosition: \"left\",\n                  className: \"w-full mt-4\",\n                  children: \"Invite Members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-card border border-border rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-text-primary\",\n                children: \"Team Members\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                size: \"sm\",\n                onClick: () => navigate('/team-members'),\n                children: \"View All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n              children: teamMembers.map(member => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 p-3 rounded-lg border border-border\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: member.avatar,\n                  alt: member.name,\n                  className: \"w-10 h-10 rounded-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-text-primary truncate\",\n                    children: member.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-text-secondary truncate\",\n                    children: member.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `w-2 h-2 rounded-full ${member.status === 'active' ? 'bg-success' : 'bg-muted'}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-text-secondary\",\n                      children: [member.tasksCompleted, \"/\", member.tasksAssigned, \" tasks\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 23\n                }, this)]\n              }, member.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-card border border-border rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-text-primary mb-4\",\n              children: \"Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                onClick: handleGoToBoard,\n                iconName: \"Kanban\",\n                iconPosition: \"left\",\n                className: \"justify-start\",\n                children: \"Go to Board\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                onClick: handleAddTask,\n                iconName: \"Plus\",\n                iconPosition: \"left\",\n                className: \"justify-start\",\n                children: \"Add New Task\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                onClick: () => navigate('/team-members'),\n                iconName: \"Users\",\n                iconPosition: \"left\",\n                className: \"justify-start\",\n                children: \"Manage Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this), activeTab === 'tasks' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"CheckSquare\",\n            size: 48,\n            className: \"mx-auto text-text-secondary mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-text-primary mb-2\",\n            children: \"Task Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-text-secondary mb-6\",\n            children: \"Manage and track project tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"default\",\n            onClick: handleGoToBoard,\n            iconName: \"Kanban\",\n            iconPosition: \"left\",\n            children: \"Go to Kanban Board\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this), activeTab === 'settings' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Settings\",\n            size: 48,\n            className: \"mx-auto text-text-secondary mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-text-primary mb-2\",\n            children: \"Project Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-text-secondary mb-6\",\n            children: \"Configure project settings and permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"default\",\n            onClick: () => navigate('/project-management'),\n            iconName: \"Settings\",\n            iconPosition: \"left\",\n            children: \"Open Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 288,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectOverview, \"n7S2HQcC7d9OcgzHy++O1bkoA6M=\", false, function () {\n  return [useNavigate, useLocation, useParams, useAuth];\n});\n_c = ProjectOverview;\nexport default ProjectOverview;\nvar _c;\n$RefreshReg$(_c, \"ProjectOverview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "useNavigate", "useParams", "useAuth", "RoleBasedHeader", "Breadcrumb", "<PERSON><PERSON>", "Icon", "jsxDEV", "_jsxDEV", "ProjectOverview", "_s", "navigate", "location", "projectId", "urlProjectId", "user", "currentProject", "setCurrentProject", "loading", "setLoading", "error", "setError", "loadProjectData", "_location$state", "_projectData$budget", "_projectData$budget2", "_projectData$budget3", "_projectData$budget4", "_projectData$team", "_projectData$team2", "_projectData$team3", "urlParams", "URLSearchParams", "search", "get", "state", "console", "log", "default", "apiService", "projectData", "projects", "getById", "transformedProject", "id", "name", "description", "status", "priority", "startDate", "start_date", "Date", "toISOString", "split", "endDate", "due_date", "progress", "progressChange", "budget", "allocated", "spent", "remaining", "team", "totalMembers", "activeMembers", "tasksCompleted", "_location$state2", "_location$state2$proj", "fallbackProject", "project", "userRole", "setUserRole", "activeTab", "setActiveTab", "teamMembers", "loadUserData", "authService", "result", "getCurrentUser", "data", "userData", "role", "toLowerCase", "tabs", "label", "icon", "roles", "visibleTabs", "filter", "tab", "includes", "handleTabChange", "tabId", "handleGoToBoard", "handleAddTask", "openAddTask", "handleInviteMembers", "getStatusColor", "getPriorityColor", "className", "children", "currentUser", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "window", "reload", "items", "breadcrumbItems", "variant", "iconName", "iconPosition", "map", "style", "width", "toLocaleString", "member", "src", "avatar", "alt", "tasksAssigned", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/project-overview/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Button from '../../components/ui/Button';\nimport Icon from '../../components/AppIcon';\n\nconst ProjectOverview = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { projectId: urlProjectId } = useParams();\n  const { user } = useAuth();\n\n  // Real project data state\n  const [currentProject, setCurrentProject] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Load real project data\n  useEffect(() => {\n    const loadProjectData = async () => {\n      try {\n        setLoading(true);\n\n        // Get project ID from URL params, route params, or location state\n        const urlParams = new URLSearchParams(location.search);\n        const projectId = urlProjectId || urlParams.get('id') || location.state?.projectId;\n\n        if (!projectId) {\n          setError('No project ID provided');\n          setLoading(false);\n          return;\n        }\n\n        console.log('Loading project data for ID:', projectId);\n\n        // Import apiService dynamically to avoid circular imports\n        const { default: apiService } = await import('../../utils/apiService');\n        const projectData = await apiService.projects.getById(projectId);\n\n        console.log('Loaded project data:', projectData);\n\n        // Transform the data to match the expected format\n        const transformedProject = {\n          id: projectData.id,\n          name: projectData.name || 'Untitled Project',\n          description: projectData.description || 'No description available',\n          status: projectData.status || 'active',\n          priority: projectData.priority || 'medium',\n          startDate: projectData.start_date || new Date().toISOString().split('T')[0],\n          endDate: projectData.due_date || new Date().toISOString().split('T')[0],\n          progress: projectData.progress || 0,\n          progressChange: '+0% this week', // This would come from analytics\n          budget: {\n            allocated: projectData.budget?.allocated || 0,\n            spent: projectData.budget?.spent || 0,\n            remaining: (projectData.budget?.allocated || 0) - (projectData.budget?.spent || 0)\n          },\n          team: {\n            totalMembers: projectData.team?.totalMembers || 0,\n            activeMembers: projectData.team?.activeMembers || 0,\n            tasksCompleted: projectData.team?.tasksCompleted || 0\n          }\n        };\n\n        setCurrentProject(transformedProject);\n\n      } catch (error) {\n        console.error('Failed to load project data:', error);\n        setError('Failed to load project data');\n\n        // Set fallback project data if API fails\n        const fallbackProject = {\n          id: 'fallback',\n          name: location.state?.project?.name || 'Project',\n          description: 'Unable to load project details',\n          status: 'active',\n          priority: 'medium',\n          startDate: new Date().toISOString().split('T')[0],\n          endDate: new Date().toISOString().split('T')[0],\n          progress: 0,\n          progressChange: '+0% this week',\n          budget: { allocated: 0, spent: 0, remaining: 0 },\n          team: { totalMembers: 0, activeMembers: 0, tasksCompleted: 0 }\n        };\n\n        setCurrentProject(fallbackProject);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadProjectData();\n  }, [location.search, location.state, urlProjectId]);\n\n  const [userRole, setUserRole] = useState('member');\n  const [activeTab, setActiveTab] = useState('overview');\n\n  // Real team members data - will be empty for now since this is project-specific\n  const [teamMembers] = useState([]);\n\n  useEffect(() => {\n    // Load real user role from auth context or API\n    const loadUserData = async () => {\n      try {\n        // Import authService dynamically to avoid circular imports\n        const { default: authService } = await import('../../utils/authService');\n        const result = await authService.getCurrentUser();\n\n        if (result.data && result.data.user) {\n          const userData = result.data.user;\n          // Get user role from the user data\n          const role = userData.role || 'member';\n          console.log('User role loaded:', role);\n          setUserRole(role.toLowerCase());\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Fallback to member role\n        setUserRole('member');\n      }\n    };\n\n    loadUserData();\n  }, []);\n\n  const tabs = [\n    {\n      id: 'overview',\n      label: 'Overview',\n      icon: 'BarChart3',\n      description: 'Project summary and key metrics',\n      roles: ['viewer', 'member', 'admin', 'owner']\n    },\n    {\n      id: 'tasks',\n      label: 'Tasks',\n      icon: 'CheckSquare',\n      description: 'Task management and tracking',\n      roles: ['viewer', 'member', 'admin', 'owner']\n    },\n    {\n      id: 'settings',\n      label: 'Settings',\n      icon: 'Settings',\n      description: 'Project configuration and permissions',\n      roles: ['admin', 'owner']\n    }\n  ];\n\n  const visibleTabs = tabs.filter(tab => tab.roles.includes(userRole));\n\n  const handleTabChange = (tabId) => {\n    setActiveTab(tabId);\n  };\n\n  const handleGoToBoard = () => {\n    navigate('/kanban-board', { \n      state: { \n        projectId: currentProject.id,\n        project: currentProject \n      } \n    });\n  };\n\n  const handleAddTask = () => {\n    // Navigate to kanban board with add task modal open\n    navigate('/kanban-board', { \n      state: { \n        projectId: currentProject.id,\n        project: currentProject,\n        openAddTask: true\n      } \n    });\n  };\n\n  const handleInviteMembers = () => {\n    // Open invite members modal\n    console.log('Opening invite members modal...');\n  };\n\n  const getStatusColor = (status) => {\n    if (!status) return 'bg-muted text-text-secondary';\n\n    switch (status.toLowerCase()) {\n      case 'good':\n        return 'bg-success/10 text-success';\n      case 'at risk':\n        return 'bg-warning/10 text-warning';\n      case 'delayed':\n        return 'bg-destructive/10 text-destructive';\n      default:\n        return 'bg-muted text-text-secondary';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    if (!priority) return 'bg-muted text-text-secondary';\n\n    switch (priority.toLowerCase()) {\n      case 'high priority':\n        return 'bg-destructive/10 text-destructive';\n      case 'medium priority':\n        return 'bg-warning/10 text-warning';\n      case 'low priority':\n        return 'bg-success/10 text-success';\n      default:\n        return 'bg-muted text-text-secondary';\n    }\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          currentUser={user}\n          userRole={userRole}\n        />\n        <main className=\"pt-16\">\n          <div className=\"max-w-7xl mx-auto p-6\">\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <Icon name=\"Loader2\" size={32} className=\"animate-spin mx-auto mb-4 text-primary\" />\n                <p className=\"text-text-secondary\">Loading project data...</p>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          currentUser={user}\n          userRole={userRole}\n        />\n        <main className=\"pt-16\">\n          <div className=\"max-w-7xl mx-auto p-6\">\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <Icon name=\"AlertCircle\" size={32} className=\"mx-auto mb-4 text-destructive\" />\n                <h3 className=\"text-lg font-medium text-text-primary mb-2\">Error Loading Project</h3>\n                <p className=\"text-text-secondary mb-4\">{error}</p>\n                <Button onClick={() => window.location.reload()}>\n                  Try Again\n                </Button>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    );\n  }\n\n  // Show main content when project data is loaded\n  if (!currentProject) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          currentUser={user}\n          userRole={userRole}\n        />\n        <main className=\"pt-16\">\n          <div className=\"max-w-7xl mx-auto p-6\">\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <Icon name=\"FolderX\" size={32} className=\"mx-auto mb-4 text-text-secondary\" />\n                <h3 className=\"text-lg font-medium text-text-primary mb-2\">Project Not Found</h3>\n                <p className=\"text-text-secondary mb-4\">The requested project could not be found.</p>\n                <Button onClick={() => navigate('/role-based-dashboard')}>\n                  Back to Dashboard\n                </Button>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <RoleBasedHeader\n        currentUser={user}\n        userRole={userRole}\n      />\n\n      <div className=\"flex-1 flex flex-col\">\n        {/* Breadcrumb */}\n        <div className=\"border-b border-border bg-card\">\n          <div className=\"px-6 py-4\">\n            <Breadcrumb items={breadcrumbItems} />\n          </div>\n        </div>\n\n        {/* Project Header */}\n        <div className=\"border-b border-border bg-card\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <div className=\"flex items-center space-x-3 mb-2\">\n                  <h1 className=\"text-2xl font-bold text-text-primary\">{currentProject.name}</h1>\n                  <span className={`px-2 py-1 rounded-md text-xs font-medium ${getStatusColor(currentProject.status)}`}>\n                    {currentProject.status}\n                  </span>\n                </div>\n                <p className=\"text-text-secondary mb-4 max-w-3xl\">\n                  {currentProject.description}\n                </p>\n                <div className=\"flex items-center space-x-6 text-sm\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Icon name=\"Calendar\" size={16} className=\"text-text-secondary\" />\n                    <span className=\"text-text-secondary\">\n                      {currentProject.startDate} - {currentProject.endDate}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Icon name=\"Flag\" size={16} className=\"text-text-secondary\" />\n                    <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(currentProject.priority)}`}>\n                      {currentProject.priority}\n                    </span>\n                  </div>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => navigate('/project-management')}\n                  iconName=\"Settings\"\n                  iconPosition=\"left\"\n                >\n                  Settings\n                </Button>\n                <Button\n                  variant=\"default\"\n                  onClick={handleAddTask}\n                  iconName=\"Plus\"\n                  iconPosition=\"left\"\n                >\n                  Add Task\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Tabs Navigation */}\n        <div className=\"border-b border-border bg-card\">\n          <div className=\"px-6\">\n            <nav className=\"flex space-x-8\">\n              {visibleTabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => handleTabChange(tab.id)}\n                  className={`flex items-center space-x-2 px-1 py-4 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-primary text-primary'\n                      : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border'\n                  }`}\n                >\n                  <Icon name={tab.icon} size={16} />\n                  <span>{tab.label}</span>\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"flex-1 p-6\">\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              {/* Progress, Budget, Team Cards */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                {/* Progress Card */}\n                <div className=\"bg-card border border-border rounded-lg p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-semibold text-text-primary flex items-center\">\n                      <Icon name=\"TrendingUp\" size={20} className=\"mr-2 text-primary\" />\n                      Progress\n                    </h3>\n                  </div>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <span className=\"text-3xl font-bold text-text-primary\">{currentProject.progress}%</span>\n                        <span className=\"text-sm text-success font-medium\">{currentProject.progressChange}</span>\n                      </div>\n                      <div className=\"w-full bg-muted rounded-full h-2\">\n                        <div \n                          className=\"bg-primary h-2 rounded-full transition-all duration-300\"\n                          style={{ width: `${currentProject.progress}%` }}\n                        ></div>\n                      </div>\n                      <p className=\"text-sm text-text-secondary mt-2\">On track for completion</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Budget Card */}\n                <div className=\"bg-card border border-border rounded-lg p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-semibold text-text-primary flex items-center\">\n                      <Icon name=\"DollarSign\" size={20} className=\"mr-2 text-primary\" />\n                      Budget\n                    </h3>\n                  </div>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Allocated</span>\n                      <span className=\"font-medium\">${currentProject.budget.allocated.toLocaleString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Spent</span>\n                      <span className=\"font-medium\">${currentProject.budget.spent.toLocaleString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Remaining</span>\n                      <span className=\"font-medium text-success\">${currentProject.budget.remaining.toLocaleString()}</span>\n                    </div>\n                    <div className=\"w-full bg-muted rounded-full h-2 mt-4\">\n                      <div \n                        className=\"bg-warning h-2 rounded-full\"\n                        style={{ width: `${(currentProject.budget.spent / currentProject.budget.allocated) * 100}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Team Card */}\n                <div className=\"bg-card border border-border rounded-lg p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-semibold text-text-primary flex items-center\">\n                      <Icon name=\"Users\" size={20} className=\"mr-2 text-primary\" />\n                      Team\n                    </h3>\n                  </div>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Total Members</span>\n                      <span className=\"font-medium\">{currentProject.team.totalMembers}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Active</span>\n                      <span className=\"font-medium\">{currentProject.team.activeMembers}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Tasks Completed</span>\n                      <span className=\"font-medium\">{currentProject.team.tasksCompleted}</span>\n                    </div>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={handleInviteMembers}\n                      iconName=\"UserPlus\"\n                      iconPosition=\"left\"\n                      className=\"w-full mt-4\"\n                    >\n                      Invite Members\n                    </Button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Team Members Section */}\n              <div className=\"bg-card border border-border rounded-lg p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h3 className=\"text-lg font-semibold text-text-primary\">Team Members</h3>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => navigate('/team-members')}\n                  >\n                    View All\n                  </Button>\n                </div>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                  {teamMembers.map((member) => (\n                    <div key={member.id} className=\"flex items-center space-x-3 p-3 rounded-lg border border-border\">\n                      <img\n                        src={member.avatar}\n                        alt={member.name}\n                        className=\"w-10 h-10 rounded-full object-cover\"\n                      />\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-text-primary truncate\">{member.name}</p>\n                        <p className=\"text-xs text-text-secondary truncate\">{member.role}</p>\n                        <div className=\"flex items-center space-x-2 mt-1\">\n                          <span className={`w-2 h-2 rounded-full ${\n                            member.status === 'active' ? 'bg-success' : 'bg-muted'\n                          }`}></span>\n                          <span className=\"text-xs text-text-secondary\">\n                            {member.tasksCompleted}/{member.tasksAssigned} tasks\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Quick Actions */}\n              <div className=\"bg-card border border-border rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-text-primary mb-4\">Quick Actions</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <Button\n                    variant=\"outline\"\n                    onClick={handleGoToBoard}\n                    iconName=\"Kanban\"\n                    iconPosition=\"left\"\n                    className=\"justify-start\"\n                  >\n                    Go to Board\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    onClick={handleAddTask}\n                    iconName=\"Plus\"\n                    iconPosition=\"left\"\n                    className=\"justify-start\"\n                  >\n                    Add New Task\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => navigate('/team-members')}\n                    iconName=\"Users\"\n                    iconPosition=\"left\"\n                    className=\"justify-start\"\n                  >\n                    Manage Team\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'tasks' && (\n            <div className=\"text-center py-12\">\n              <Icon name=\"CheckSquare\" size={48} className=\"mx-auto text-text-secondary mb-4\" />\n              <h3 className=\"text-lg font-semibold text-text-primary mb-2\">Task Management</h3>\n              <p className=\"text-text-secondary mb-6\">Manage and track project tasks</p>\n              <Button\n                variant=\"default\"\n                onClick={handleGoToBoard}\n                iconName=\"Kanban\"\n                iconPosition=\"left\"\n              >\n                Go to Kanban Board\n              </Button>\n            </div>\n          )}\n\n          {activeTab === 'settings' && (\n            <div className=\"text-center py-12\">\n              <Icon name=\"Settings\" size={48} className=\"mx-auto text-text-secondary mb-4\" />\n              <h3 className=\"text-lg font-semibold text-text-primary mb-2\">Project Settings</h3>\n              <p className=\"text-text-secondary mb-6\">Configure project settings and permissions</p>\n              <Button\n                variant=\"default\"\n                onClick={() => navigate('/project-management')}\n                iconName=\"Settings\"\n                iconPosition=\"left\"\n              >\n                Open Settings\n              </Button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProjectOverview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,SAAS,EAAEC;EAAa,CAAC,GAAGb,SAAS,CAAC,CAAC;EAC/C,MAAM;IAAEc;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QAAA,IAAAC,eAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QACFX,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMY,SAAS,GAAG,IAAIC,eAAe,CAACpB,QAAQ,CAACqB,MAAM,CAAC;QACtD,MAAMpB,SAAS,GAAGC,YAAY,IAAIiB,SAAS,CAACG,GAAG,CAAC,IAAI,CAAC,MAAAX,eAAA,GAAIX,QAAQ,CAACuB,KAAK,cAAAZ,eAAA,uBAAdA,eAAA,CAAgBV,SAAS;QAElF,IAAI,CAACA,SAAS,EAAE;UACdQ,QAAQ,CAAC,wBAAwB,CAAC;UAClCF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAExB,SAAS,CAAC;;QAEtD;QACA,MAAM;UAAEyB,OAAO,EAAEC;QAAW,CAAC,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC;QACtE,MAAMC,WAAW,GAAG,MAAMD,UAAU,CAACE,QAAQ,CAACC,OAAO,CAAC7B,SAAS,CAAC;QAEhEuB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEG,WAAW,CAAC;;QAEhD;QACA,MAAMG,kBAAkB,GAAG;UACzBC,EAAE,EAAEJ,WAAW,CAACI,EAAE;UAClBC,IAAI,EAAEL,WAAW,CAACK,IAAI,IAAI,kBAAkB;UAC5CC,WAAW,EAAEN,WAAW,CAACM,WAAW,IAAI,0BAA0B;UAClEC,MAAM,EAAEP,WAAW,CAACO,MAAM,IAAI,QAAQ;UACtCC,QAAQ,EAAER,WAAW,CAACQ,QAAQ,IAAI,QAAQ;UAC1CC,SAAS,EAAET,WAAW,CAACU,UAAU,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC3EC,OAAO,EAAEd,WAAW,CAACe,QAAQ,IAAI,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACvEG,QAAQ,EAAEhB,WAAW,CAACgB,QAAQ,IAAI,CAAC;UACnCC,cAAc,EAAE,eAAe;UAAE;UACjCC,MAAM,EAAE;YACNC,SAAS,EAAE,EAAAnC,mBAAA,GAAAgB,WAAW,CAACkB,MAAM,cAAAlC,mBAAA,uBAAlBA,mBAAA,CAAoBmC,SAAS,KAAI,CAAC;YAC7CC,KAAK,EAAE,EAAAnC,oBAAA,GAAAe,WAAW,CAACkB,MAAM,cAAAjC,oBAAA,uBAAlBA,oBAAA,CAAoBmC,KAAK,KAAI,CAAC;YACrCC,SAAS,EAAE,CAAC,EAAAnC,oBAAA,GAAAc,WAAW,CAACkB,MAAM,cAAAhC,oBAAA,uBAAlBA,oBAAA,CAAoBiC,SAAS,KAAI,CAAC,KAAK,EAAAhC,oBAAA,GAAAa,WAAW,CAACkB,MAAM,cAAA/B,oBAAA,uBAAlBA,oBAAA,CAAoBiC,KAAK,KAAI,CAAC;UACnF,CAAC;UACDE,IAAI,EAAE;YACJC,YAAY,EAAE,EAAAnC,iBAAA,GAAAY,WAAW,CAACsB,IAAI,cAAAlC,iBAAA,uBAAhBA,iBAAA,CAAkBmC,YAAY,KAAI,CAAC;YACjDC,aAAa,EAAE,EAAAnC,kBAAA,GAAAW,WAAW,CAACsB,IAAI,cAAAjC,kBAAA,uBAAhBA,kBAAA,CAAkBmC,aAAa,KAAI,CAAC;YACnDC,cAAc,EAAE,EAAAnC,kBAAA,GAAAU,WAAW,CAACsB,IAAI,cAAAhC,kBAAA,uBAAhBA,kBAAA,CAAkBmC,cAAc,KAAI;UACtD;QACF,CAAC;QAEDhD,iBAAiB,CAAC0B,kBAAkB,CAAC;MAEvC,CAAC,CAAC,OAAOvB,KAAK,EAAE;QAAA,IAAA8C,gBAAA,EAAAC,qBAAA;QACd/B,OAAO,CAAChB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDC,QAAQ,CAAC,6BAA6B,CAAC;;QAEvC;QACA,MAAM+C,eAAe,GAAG;UACtBxB,EAAE,EAAE,UAAU;UACdC,IAAI,EAAE,EAAAqB,gBAAA,GAAAtD,QAAQ,CAACuB,KAAK,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBG,OAAO,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBtB,IAAI,KAAI,SAAS;UAChDC,WAAW,EAAE,gCAAgC;UAC7CC,MAAM,EAAE,QAAQ;UAChBC,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjDC,OAAO,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC/CG,QAAQ,EAAE,CAAC;UACXC,cAAc,EAAE,eAAe;UAC/BC,MAAM,EAAE;YAAEC,SAAS,EAAE,CAAC;YAAEC,KAAK,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAE,CAAC;UAChDC,IAAI,EAAE;YAAEC,YAAY,EAAE,CAAC;YAAEC,aAAa,EAAE,CAAC;YAAEC,cAAc,EAAE;UAAE;QAC/D,CAAC;QAEDhD,iBAAiB,CAACmD,eAAe,CAAC;MACpC,CAAC,SAAS;QACRjD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACV,QAAQ,CAACqB,MAAM,EAAErB,QAAQ,CAACuB,KAAK,EAAErB,YAAY,CAAC,CAAC;EAEnD,MAAM,CAACwD,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACA,MAAM,CAAC6E,WAAW,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAElCC,SAAS,CAAC,MAAM;IACd;IACA,MAAM6E,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF;QACA,MAAM;UAAErC,OAAO,EAAEsC;QAAY,CAAC,GAAG,MAAM,MAAM,CAAC,yBAAyB,CAAC;QACxE,MAAMC,MAAM,GAAG,MAAMD,WAAW,CAACE,cAAc,CAAC,CAAC;QAEjD,IAAID,MAAM,CAACE,IAAI,IAAIF,MAAM,CAACE,IAAI,CAAChE,IAAI,EAAE;UACnC,MAAMiE,QAAQ,GAAGH,MAAM,CAACE,IAAI,CAAChE,IAAI;UACjC;UACA,MAAMkE,IAAI,GAAGD,QAAQ,CAACC,IAAI,IAAI,QAAQ;UACtC7C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE4C,IAAI,CAAC;UACtCV,WAAW,CAACU,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;QACjC;MACF,CAAC,CAAC,OAAO9D,KAAK,EAAE;QACdgB,OAAO,CAAChB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACAmD,WAAW,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC;IAEDI,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,IAAI,GAAG,CACX;IACEvC,EAAE,EAAE,UAAU;IACdwC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,WAAW;IACjBvC,WAAW,EAAE,iCAAiC;IAC9CwC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;EAC9C,CAAC,EACD;IACE1C,EAAE,EAAE,OAAO;IACXwC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,aAAa;IACnBvC,WAAW,EAAE,8BAA8B;IAC3CwC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;EAC9C,CAAC,EACD;IACE1C,EAAE,EAAE,UAAU;IACdwC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,UAAU;IAChBvC,WAAW,EAAE,uCAAuC;IACpDwC,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO;EAC1B,CAAC,CACF;EAED,MAAMC,WAAW,GAAGJ,IAAI,CAACK,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACH,KAAK,CAACI,QAAQ,CAACpB,QAAQ,CAAC,CAAC;EAEpE,MAAMqB,eAAe,GAAIC,KAAK,IAAK;IACjCnB,YAAY,CAACmB,KAAK,CAAC;EACrB,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BlF,QAAQ,CAAC,eAAe,EAAE;MACxBwB,KAAK,EAAE;QACLtB,SAAS,EAAEG,cAAc,CAAC4B,EAAE;QAC5ByB,OAAO,EAAErD;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8E,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACAnF,QAAQ,CAAC,eAAe,EAAE;MACxBwB,KAAK,EAAE;QACLtB,SAAS,EAAEG,cAAc,CAAC4B,EAAE;QAC5ByB,OAAO,EAAErD,cAAc;QACvB+E,WAAW,EAAE;MACf;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC;IACA5D,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAChD,CAAC;EAED,MAAM4D,cAAc,GAAIlD,MAAM,IAAK;IACjC,IAAI,CAACA,MAAM,EAAE,OAAO,8BAA8B;IAElD,QAAQA,MAAM,CAACmC,WAAW,CAAC,CAAC;MAC1B,KAAK,MAAM;QACT,OAAO,4BAA4B;MACrC,KAAK,SAAS;QACZ,OAAO,4BAA4B;MACrC,KAAK,SAAS;QACZ,OAAO,oCAAoC;MAC7C;QACE,OAAO,8BAA8B;IACzC;EACF,CAAC;EAED,MAAMgB,gBAAgB,GAAIlD,QAAQ,IAAK;IACrC,IAAI,CAACA,QAAQ,EAAE,OAAO,8BAA8B;IAEpD,QAAQA,QAAQ,CAACkC,WAAW,CAAC,CAAC;MAC5B,KAAK,eAAe;QAClB,OAAO,oCAAoC;MAC7C,KAAK,iBAAiB;QACpB,OAAO,4BAA4B;MACrC,KAAK,cAAc;QACjB,OAAO,4BAA4B;MACrC;QACE,OAAO,8BAA8B;IACzC;EACF,CAAC;;EAED;EACA,IAAIhE,OAAO,EAAE;IACX,oBACEV,OAAA;MAAK2F,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC5F,OAAA,CAACL,eAAe;QACdkG,WAAW,EAAEtF,IAAK;QAClBuD,QAAQ,EAAEA;MAAS;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACFjG,OAAA;QAAM2F,SAAS,EAAC,OAAO;QAAAC,QAAA,eACrB5F,OAAA;UAAK2F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC5F,OAAA;YAAK2F,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD5F,OAAA;cAAK2F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5F,OAAA,CAACF,IAAI;gBAACuC,IAAI,EAAC,SAAS;gBAAC6D,IAAI,EAAE,EAAG;gBAACP,SAAS,EAAC;cAAwC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpFjG,OAAA;gBAAG2F,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;;EAEA;EACA,IAAIrF,KAAK,EAAE;IACT,oBACEZ,OAAA;MAAK2F,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC5F,OAAA,CAACL,eAAe;QACdkG,WAAW,EAAEtF,IAAK;QAClBuD,QAAQ,EAAEA;MAAS;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACFjG,OAAA;QAAM2F,SAAS,EAAC,OAAO;QAAAC,QAAA,eACrB5F,OAAA;UAAK2F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC5F,OAAA;YAAK2F,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD5F,OAAA;cAAK2F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5F,OAAA,CAACF,IAAI;gBAACuC,IAAI,EAAC,aAAa;gBAAC6D,IAAI,EAAE,EAAG;gBAACP,SAAS,EAAC;cAA+B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/EjG,OAAA;gBAAI2F,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrFjG,OAAA;gBAAG2F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEhF;cAAK;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDjG,OAAA,CAACH,MAAM;gBAACsG,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAAChG,QAAQ,CAACiG,MAAM,CAAC,CAAE;gBAAAT,QAAA,EAAC;cAEjD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;;EAEA;EACA,IAAI,CAACzF,cAAc,EAAE;IACnB,oBACER,OAAA;MAAK2F,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC5F,OAAA,CAACL,eAAe;QACdkG,WAAW,EAAEtF,IAAK;QAClBuD,QAAQ,EAAEA;MAAS;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACFjG,OAAA;QAAM2F,SAAS,EAAC,OAAO;QAAAC,QAAA,eACrB5F,OAAA;UAAK2F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC5F,OAAA;YAAK2F,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD5F,OAAA;cAAK2F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5F,OAAA,CAACF,IAAI;gBAACuC,IAAI,EAAC,SAAS;gBAAC6D,IAAI,EAAE,EAAG;gBAACP,SAAS,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9EjG,OAAA;gBAAI2F,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFjG,OAAA;gBAAG2F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAyC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrFjG,OAAA,CAACH,MAAM;gBAACsG,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,uBAAuB,CAAE;gBAAAyF,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACEjG,OAAA;IAAK2F,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzC5F,OAAA,CAACL,eAAe;MACdkG,WAAW,EAAEtF,IAAK;MAClBuD,QAAQ,EAAEA;IAAS;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAEFjG,OAAA;MAAK2F,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnC5F,OAAA;QAAK2F,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5F,OAAA;UAAK2F,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB5F,OAAA,CAACJ,UAAU;YAAC0G,KAAK,EAAEC;UAAgB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAK2F,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5F,OAAA;UAAK2F,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB5F,OAAA;YAAK2F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C5F,OAAA;cAAK2F,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5F,OAAA;gBAAK2F,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C5F,OAAA;kBAAI2F,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAEpF,cAAc,CAAC6B;gBAAI;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/EjG,OAAA;kBAAM2F,SAAS,EAAE,4CAA4CF,cAAc,CAACjF,cAAc,CAAC+B,MAAM,CAAC,EAAG;kBAAAqD,QAAA,EAClGpF,cAAc,CAAC+B;gBAAM;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjG,OAAA;gBAAG2F,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAC9CpF,cAAc,CAAC8B;cAAW;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACJjG,OAAA;gBAAK2F,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClD5F,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA,CAACF,IAAI;oBAACuC,IAAI,EAAC,UAAU;oBAAC6D,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClEjG,OAAA;oBAAM2F,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,GAClCpF,cAAc,CAACiC,SAAS,EAAC,KAAG,EAACjC,cAAc,CAACsC,OAAO;kBAAA;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNjG,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA,CAACF,IAAI;oBAACuC,IAAI,EAAC,MAAM;oBAAC6D,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DjG,OAAA;oBAAM2F,SAAS,EAAE,yCAAyCD,gBAAgB,CAAClF,cAAc,CAACgC,QAAQ,CAAC,EAAG;oBAAAoD,QAAA,EACnGpF,cAAc,CAACgC;kBAAQ;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjG,OAAA;cAAK2F,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C5F,OAAA,CAACH,MAAM;gBACL2G,OAAO,EAAC,SAAS;gBACjBL,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,qBAAqB,CAAE;gBAC/CsG,QAAQ,EAAC,UAAU;gBACnBC,YAAY,EAAC,MAAM;gBAAAd,QAAA,EACpB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjG,OAAA,CAACH,MAAM;gBACL2G,OAAO,EAAC,SAAS;gBACjBL,OAAO,EAAEb,aAAc;gBACvBmB,QAAQ,EAAC,MAAM;gBACfC,YAAY,EAAC,MAAM;gBAAAd,QAAA,EACpB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAK2F,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5F,OAAA;UAAK2F,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5F,OAAA;YAAK2F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5Bb,WAAW,CAAC4B,GAAG,CAAE1B,GAAG,iBACnBjF,OAAA;cAEEmG,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAACF,GAAG,CAAC7C,EAAE,CAAE;cACvCuD,SAAS,EAAE,0FACT3B,SAAS,KAAKiB,GAAG,CAAC7C,EAAE,GAChB,6BAA6B,GAC7B,oFAAoF,EACvF;cAAAwD,QAAA,gBAEH5F,OAAA,CAACF,IAAI;gBAACuC,IAAI,EAAE4C,GAAG,CAACJ,IAAK;gBAACqB,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCjG,OAAA;gBAAA4F,QAAA,EAAOX,GAAG,CAACL;cAAK;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GATnBhB,GAAG,CAAC7C,EAAE;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUL,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAK2F,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxB5B,SAAS,KAAK,UAAU,iBACvBhE,OAAA;UAAK2F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB5F,OAAA;YAAK2F,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpD5F,OAAA;cAAK2F,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1D5F,OAAA;gBAAK2F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrD5F,OAAA;kBAAI2F,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBACvE5F,OAAA,CAACF,IAAI;oBAACuC,IAAI,EAAC,YAAY;oBAAC6D,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEpE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNjG,OAAA;gBAAK2F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxB5F,OAAA;kBAAA4F,QAAA,gBACE5F,OAAA;oBAAK2F,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD5F,OAAA;sBAAM2F,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,GAAEpF,cAAc,CAACwC,QAAQ,EAAC,GAAC;oBAAA;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxFjG,OAAA;sBAAM2F,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAEpF,cAAc,CAACyC;oBAAc;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eACNjG,OAAA;oBAAK2F,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,eAC/C5F,OAAA;sBACE2F,SAAS,EAAC,yDAAyD;sBACnEiB,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAGrG,cAAc,CAACwC,QAAQ;sBAAI;oBAAE;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNjG,OAAA;oBAAG2F,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjG,OAAA;cAAK2F,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1D5F,OAAA;gBAAK2F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrD5F,OAAA;kBAAI2F,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBACvE5F,OAAA,CAACF,IAAI;oBAACuC,IAAI,EAAC,YAAY;oBAAC6D,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEpE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNjG,OAAA;gBAAK2F,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5F,OAAA;kBAAK2F,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC5F,OAAA;oBAAM2F,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDjG,OAAA;oBAAM2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,GAAC,EAACpF,cAAc,CAAC0C,MAAM,CAACC,SAAS,CAAC2D,cAAc,CAAC,CAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACNjG,OAAA;kBAAK2F,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC5F,OAAA;oBAAM2F,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDjG,OAAA;oBAAM2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,GAAC,EAACpF,cAAc,CAAC0C,MAAM,CAACE,KAAK,CAAC0D,cAAc,CAAC,CAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACNjG,OAAA;kBAAK2F,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC5F,OAAA;oBAAM2F,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDjG,OAAA;oBAAM2F,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,GAAC,GAAC,EAACpF,cAAc,CAAC0C,MAAM,CAACG,SAAS,CAACyD,cAAc,CAAC,CAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC,eACNjG,OAAA;kBAAK2F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,eACpD5F,OAAA;oBACE2F,SAAS,EAAC,6BAA6B;oBACvCiB,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAIrG,cAAc,CAAC0C,MAAM,CAACE,KAAK,GAAG5C,cAAc,CAAC0C,MAAM,CAACC,SAAS,GAAI,GAAG;oBAAI;kBAAE;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjG,OAAA;cAAK2F,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1D5F,OAAA;gBAAK2F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrD5F,OAAA;kBAAI2F,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBACvE5F,OAAA,CAACF,IAAI;oBAACuC,IAAI,EAAC,OAAO;oBAAC6D,IAAI,EAAE,EAAG;oBAACP,SAAS,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,QAE/D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNjG,OAAA;gBAAK2F,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5F,OAAA;kBAAK2F,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC5F,OAAA;oBAAM2F,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1DjG,OAAA;oBAAM2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEpF,cAAc,CAAC8C,IAAI,CAACC;kBAAY;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACNjG,OAAA;kBAAK2F,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC5F,OAAA;oBAAM2F,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnDjG,OAAA;oBAAM2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEpF,cAAc,CAAC8C,IAAI,CAACE;kBAAa;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACNjG,OAAA;kBAAK2F,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC5F,OAAA;oBAAM2F,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DjG,OAAA;oBAAM2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEpF,cAAc,CAAC8C,IAAI,CAACG;kBAAc;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNjG,OAAA,CAACH,MAAM;kBACL2G,OAAO,EAAC,SAAS;kBACjBN,IAAI,EAAC,IAAI;kBACTC,OAAO,EAAEX,mBAAoB;kBAC7BiB,QAAQ,EAAC,UAAU;kBACnBC,YAAY,EAAC,MAAM;kBACnBf,SAAS,EAAC,aAAa;kBAAAC,QAAA,EACxB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjG,OAAA;YAAK2F,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAC1D5F,OAAA;cAAK2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD5F,OAAA;gBAAI2F,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzEjG,OAAA,CAACH,MAAM;gBACL2G,OAAO,EAAC,SAAS;gBACjBN,IAAI,EAAC,IAAI;gBACTC,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,eAAe,CAAE;gBAAAyF,QAAA,EAC1C;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNjG,OAAA;cAAK2F,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAClE1B,WAAW,CAACyC,GAAG,CAAEI,MAAM,iBACtB/G,OAAA;gBAAqB2F,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAC9F5F,OAAA;kBACEgH,GAAG,EAAED,MAAM,CAACE,MAAO;kBACnBC,GAAG,EAAEH,MAAM,CAAC1E,IAAK;kBACjBsD,SAAS,EAAC;gBAAqC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACFjG,OAAA;kBAAK2F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B5F,OAAA;oBAAG2F,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAEmB,MAAM,CAAC1E;kBAAI;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/EjG,OAAA;oBAAG2F,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAEmB,MAAM,CAACtC;kBAAI;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrEjG,OAAA;oBAAK2F,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/C5F,OAAA;sBAAM2F,SAAS,EAAE,wBACfoB,MAAM,CAACxE,MAAM,KAAK,QAAQ,GAAG,YAAY,GAAG,UAAU;oBACrD;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACXjG,OAAA;sBAAM2F,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAC1CmB,MAAM,CAACtD,cAAc,EAAC,GAAC,EAACsD,MAAM,CAACI,aAAa,EAAC,QAChD;oBAAA;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAjBEc,MAAM,CAAC3E,EAAE;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBd,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjG,OAAA;YAAK2F,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAC1D5F,OAAA;cAAI2F,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EjG,OAAA;cAAK2F,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5F,OAAA,CAACH,MAAM;gBACL2G,OAAO,EAAC,SAAS;gBACjBL,OAAO,EAAEd,eAAgB;gBACzBoB,QAAQ,EAAC,QAAQ;gBACjBC,YAAY,EAAC,MAAM;gBACnBf,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC1B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjG,OAAA,CAACH,MAAM;gBACL2G,OAAO,EAAC,SAAS;gBACjBL,OAAO,EAAEb,aAAc;gBACvBmB,QAAQ,EAAC,MAAM;gBACfC,YAAY,EAAC,MAAM;gBACnBf,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC1B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjG,OAAA,CAACH,MAAM;gBACL2G,OAAO,EAAC,SAAS;gBACjBL,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,eAAe,CAAE;gBACzCsG,QAAQ,EAAC,OAAO;gBAChBC,YAAY,EAAC,MAAM;gBACnBf,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC1B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAjC,SAAS,KAAK,OAAO,iBACpBhE,OAAA;UAAK2F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5F,OAAA,CAACF,IAAI;YAACuC,IAAI,EAAC,aAAa;YAAC6D,IAAI,EAAE,EAAG;YAACP,SAAS,EAAC;UAAkC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClFjG,OAAA;YAAI2F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFjG,OAAA;YAAG2F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1EjG,OAAA,CAACH,MAAM;YACL2G,OAAO,EAAC,SAAS;YACjBL,OAAO,EAAEd,eAAgB;YACzBoB,QAAQ,EAAC,QAAQ;YACjBC,YAAY,EAAC,MAAM;YAAAd,QAAA,EACpB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEAjC,SAAS,KAAK,UAAU,iBACvBhE,OAAA;UAAK2F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5F,OAAA,CAACF,IAAI;YAACuC,IAAI,EAAC,UAAU;YAAC6D,IAAI,EAAE,EAAG;YAACP,SAAS,EAAC;UAAkC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/EjG,OAAA;YAAI2F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFjG,OAAA;YAAG2F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAA0C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtFjG,OAAA,CAACH,MAAM;YACL2G,OAAO,EAAC,SAAS;YACjBL,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,qBAAqB,CAAE;YAC/CsG,QAAQ,EAAC,UAAU;YACnBC,YAAY,EAAC,MAAM;YAAAd,QAAA,EACpB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/F,EAAA,CA1jBID,eAAe;EAAA,QACFT,WAAW,EACXD,WAAW,EACQE,SAAS,EAC5BC,OAAO;AAAA;AAAA0H,EAAA,GAJpBnH,eAAe;AA4jBrB,eAAeA,eAAe;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}