{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\Routes.jsx\";\nimport React from \"react\";\nimport { Routes as RouterRoutes, Route } from \"react-router-dom\";\nimport ScrollToTop from \"./components/ScrollToTop\";\n\n// Page imports\nimport Login from \"./pages/login\";\nimport Register from \"./pages/register\";\nimport KanbanBoard from \"./pages/kanban-board\";\nimport CardDetails from \"./pages/card-details\";\nimport TeamMembers from \"./pages/team-members\";\nimport OrganizationSettings from \"./pages/organization-settings\";\nimport OrganizationDashboard from \"./pages/organization-dashboard\";\nimport UserProfileSettings from \"./pages/user-profile-settings\";\nimport ProjectManagement from \"./pages/project-management\";\nimport ProjectOverview from \"./pages/project-overview\";\nimport RoleBasedDashboard from \"./pages/role-based-dashboard\";\nimport Analytics from \"./pages/analytics\";\nimport Billing from \"./pages/billing\";\nimport NotFound from \"./pages/NotFound\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Routes = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RouterRoutes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(OrganizationDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        element: /*#__PURE__*/_jsxDEV(OrganizationDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/organization-dashboard\",\n        element: /*#__PURE__*/_jsxDEV(OrganizationDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/kanban-board\",\n        element: /*#__PURE__*/_jsxDEV(KanbanBoard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/card-details\",\n        element: /*#__PURE__*/_jsxDEV(CardDetails, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/team-members\",\n        element: /*#__PURE__*/_jsxDEV(TeamMembers, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/organization-settings\",\n        element: /*#__PURE__*/_jsxDEV(OrganizationSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/user-profile-settings\",\n        element: /*#__PURE__*/_jsxDEV(UserProfileSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/project-management\",\n        element: /*#__PURE__*/_jsxDEV(ProjectManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/project-overview\",\n        element: /*#__PURE__*/_jsxDEV(ProjectOverview, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 50\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/project-overview/:projectId\",\n        element: /*#__PURE__*/_jsxDEV(ProjectOverview, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 61\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/role-based-dashboard\",\n        element: /*#__PURE__*/_jsxDEV(RoleBasedDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/analytics\",\n        element: /*#__PURE__*/_jsxDEV(Analytics, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/billing\",\n        element: /*#__PURE__*/_jsxDEV(Billing, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "Routes", "RouterRoutes", "Route", "ScrollToTop", "<PERSON><PERSON>", "Register", "KanbanBoard", "CardDetails", "TeamMembers", "OrganizationSettings", "OrganizationDashboard", "UserProfileSettings", "ProjectManagement", "ProjectOverview", "RoleBasedDashboard", "Analytics", "Billing", "NotFound", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/Routes.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Routes as RouterRoutes, Route } from \"react-router-dom\";\nimport ScrollToTop from \"./components/ScrollToTop\";\n\n// Page imports\nimport Login from \"./pages/login\";\nimport Register from \"./pages/register\";\nimport KanbanBoard from \"./pages/kanban-board\";\nimport CardDetails from \"./pages/card-details\";\nimport TeamMembers from \"./pages/team-members\";\nimport OrganizationSettings from \"./pages/organization-settings\";\nimport OrganizationDashboard from \"./pages/organization-dashboard\";\nimport UserProfileSettings from \"./pages/user-profile-settings\";\nimport ProjectManagement from \"./pages/project-management\";\nimport ProjectOverview from \"./pages/project-overview\";\nimport RoleBasedDashboard from \"./pages/role-based-dashboard\";\nimport Analytics from \"./pages/analytics\";\nimport Billing from \"./pages/billing\";\nimport NotFound from \"./pages/NotFound\";\n\nconst Routes = () => {\n  return (\n    <>\n      <ScrollToTop />\n      <RouterRoutes>\n        <Route path=\"/\" element={<OrganizationDashboard />} />\n        <Route path=\"/dashboard\" element={<OrganizationDashboard />} />\n        <Route path=\"/organization-dashboard\" element={<OrganizationDashboard />} />\n        <Route path=\"/login\" element={<Login />} />\n        <Route path=\"/register\" element={<Register />} />\n        <Route path=\"/kanban-board\" element={<KanbanBoard />} />\n        <Route path=\"/card-details\" element={<CardDetails />} />\n        <Route path=\"/team-members\" element={<TeamMembers />} />\n        <Route path=\"/organization-settings\" element={<OrganizationSettings />} />\n        <Route path=\"/user-profile-settings\" element={<UserProfileSettings />} />\n        <Route path=\"/project-management\" element={<ProjectManagement />} />\n        <Route path=\"/project-overview\" element={<ProjectOverview />} />\n        <Route path=\"/project-overview/:projectId\" element={<ProjectOverview />} />\n        <Route path=\"/role-based-dashboard\" element={<RoleBasedDashboard />} />\n        <Route path=\"/analytics\" element={<Analytics />} />\n        <Route path=\"/billing\" element={<Billing />} />\n        <Route path=\"*\" element={<NotFound />} />\n      </RouterRoutes>\n    </>\n  );\n};\n\nexport default Routes;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,YAAY,EAAEC,KAAK,QAAQ,kBAAkB;AAChE,OAAOC,WAAW,MAAM,0BAA0B;;AAElD;AACA,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,OAAOC,qBAAqB,MAAM,gCAAgC;AAClE,OAAOC,mBAAmB,MAAM,+BAA+B;AAC/D,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMrB,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACEmB,OAAA,CAAAE,SAAA;IAAAC,QAAA,gBACEH,OAAA,CAAChB,WAAW;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfP,OAAA,CAAClB,YAAY;MAAAqB,QAAA,gBACXH,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAET,OAAA,CAACT,qBAAqB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,YAAY;QAACC,OAAO,eAAET,OAAA,CAACT,qBAAqB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/DP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,yBAAyB;QAACC,OAAO,eAAET,OAAA,CAACT,qBAAqB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5EP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAET,OAAA,CAACf,KAAK;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,WAAW;QAACC,OAAO,eAAET,OAAA,CAACd,QAAQ;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,eAAe;QAACC,OAAO,eAAET,OAAA,CAACb,WAAW;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,eAAe;QAACC,OAAO,eAAET,OAAA,CAACZ,WAAW;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,eAAe;QAACC,OAAO,eAAET,OAAA,CAACX,WAAW;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,wBAAwB;QAACC,OAAO,eAAET,OAAA,CAACV,oBAAoB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1EP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,wBAAwB;QAACC,OAAO,eAAET,OAAA,CAACR,mBAAmB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzEP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,qBAAqB;QAACC,OAAO,eAAET,OAAA,CAACP,iBAAiB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,mBAAmB;QAACC,OAAO,eAAET,OAAA,CAACN,eAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,8BAA8B;QAACC,OAAO,eAAET,OAAA,CAACN,eAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3EP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,uBAAuB;QAACC,OAAO,eAAET,OAAA,CAACL,kBAAkB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvEP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,YAAY;QAACC,OAAO,eAAET,OAAA,CAACJ,SAAS;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnDP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,UAAU;QAACC,OAAO,eAAET,OAAA,CAACH,OAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CP,OAAA,CAACjB,KAAK;QAACyB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAET,OAAA,CAACF,QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAAA,eACf,CAAC;AAEP,CAAC;AAACG,EAAA,GAzBI7B,MAAM;AA2BZ,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}