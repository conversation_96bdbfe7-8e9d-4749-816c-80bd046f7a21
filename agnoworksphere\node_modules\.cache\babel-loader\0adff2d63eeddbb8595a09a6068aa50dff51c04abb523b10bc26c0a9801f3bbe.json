{"ast": null, "code": "// src/utils/authService.js\n// Real authentication service that connects to the backend API\n\nimport realApiService from './realApiService';\nconst authService = {\n  // Sign up a new user with real backend integration\n  signUp: async (email, password, userData = {}) => {\n    try {\n      var _result$data$user$org, _result$data$user$org2, _result$data$user$org3;\n      const registrationData = {\n        email,\n        password,\n        firstName: userData.firstName || userData.first_name || '',\n        lastName: userData.lastName || userData.last_name || '',\n        organizationName: userData.organizationName || userData.organization_name || '',\n        organizationSlug: userData.organizationSlug || userData.organization_slug || ''\n      };\n      const result = await realApiService.auth.register(registrationData);\n      if (result.error) {\n        return {\n          data: null,\n          error: result.error\n        };\n      }\n\n      // Transform the response to match the expected format\n      return {\n        data: {\n          user: {\n            id: result.data.user.id,\n            email: result.data.user.email,\n            firstName: result.data.user.first_name,\n            lastName: result.data.user.last_name,\n            emailVerified: result.data.user.email_verified || false,\n            role: ((_result$data$user$org = result.data.user.organizations) === null || _result$data$user$org === void 0 ? void 0 : (_result$data$user$org2 = _result$data$user$org[0]) === null || _result$data$user$org2 === void 0 ? void 0 : _result$data$user$org2.role) || 'owner'\n          },\n          organization: ((_result$data$user$org3 = result.data.user.organizations) === null || _result$data$user$org3 === void 0 ? void 0 : _result$data$user$org3[0]) || null,\n          tokens: result.data.tokens\n        },\n        error: null\n      };\n    } catch (error) {\n      console.error('SignUp error:', error);\n      return {\n        data: null,\n        error: error.message || 'Sign up failed'\n      };\n    }\n  },\n  // Register function (alias for signUp for compatibility)\n  register: async (email, password, userData = {}) => {\n    return await authService.signUp(email, password, userData);\n  },\n  // Sign in with email and password using real backend\n  signIn: async (email, password) => {\n    try {\n      var _result$data$user$org4, _result$data$user$org5, _result$data$user$org6;\n      const result = await realApiService.auth.login(email, password);\n      if (result.error) {\n        return {\n          data: null,\n          error: result.error\n        };\n      }\n\n      // Transform the response to match the expected format\n      return {\n        data: {\n          user: {\n            id: result.data.user.id,\n            email: result.data.user.email,\n            firstName: result.data.user.first_name,\n            lastName: result.data.user.last_name,\n            emailVerified: result.data.user.email_verified || true,\n            role: ((_result$data$user$org4 = result.data.user.organizations) === null || _result$data$user$org4 === void 0 ? void 0 : (_result$data$user$org5 = _result$data$user$org4[0]) === null || _result$data$user$org5 === void 0 ? void 0 : _result$data$user$org5.role) || 'member'\n          },\n          organization: ((_result$data$user$org6 = result.data.user.organizations) === null || _result$data$user$org6 === void 0 ? void 0 : _result$data$user$org6[0]) || null,\n          tokens: result.data.tokens\n        },\n        error: null\n      };\n    } catch (error) {\n      console.error('SignIn error:', error);\n      return {\n        data: null,\n        error: error.message || 'Sign in failed'\n      };\n    }\n  },\n  // Sign out\n  signOut: async () => {\n    return await realApiService.auth.logout();\n  },\n  // Logout (alias for signOut)\n  logout: async () => {\n    return await realApiService.auth.logout();\n  },\n  // Get current user profile using real backend\n  getCurrentUser: async () => {\n    try {\n      const result = await realApiService.auth.getCurrentUser();\n      if (result.error || !result.data) {\n        // Return mock data for testing with owner role\n        console.log('Backend not available, returning mock owner data for testing');\n        return {\n          data: {\n            user: {\n              id: 'test-user-id',\n              email: '<EMAIL>',\n              firstName: 'Nirmalkumar',\n              lastName: 'Patel',\n              emailVerified: true,\n              role: 'owner',\n              avatar: '/assets/images/avatar.jpg'\n            },\n            organizations: [{\n              id: 'test-org-id',\n              name: 'ACME Corporation',\n              domain: 'acme.com',\n              role: 'owner'\n            }]\n          },\n          error: null\n        };\n      }\n\n      // Handle the enhanced server response format\n      const userData = result.data.user || result.data;\n      const organizations = result.data.organizations || [];\n\n      // Get the user's role from their first organization\n      const userRole = organizations.length > 0 ? organizations[0].role : 'member';\n\n      // Transform the response to match the expected format\n      return {\n        data: {\n          user: {\n            id: userData.id,\n            email: userData.email,\n            firstName: userData.first_name || userData.firstName,\n            lastName: userData.last_name || userData.lastName,\n            emailVerified: userData.email_verified || true,\n            role: userRole,\n            avatar: userData.avatar_url || userData.avatar\n          },\n          organizations: organizations.map(org => {\n            var _org$organization, _org$organization2, _org$organization3;\n            return {\n              id: ((_org$organization = org.organization) === null || _org$organization === void 0 ? void 0 : _org$organization.id) || org.id,\n              name: ((_org$organization2 = org.organization) === null || _org$organization2 === void 0 ? void 0 : _org$organization2.name) || org.name,\n              domain: ((_org$organization3 = org.organization) === null || _org$organization3 === void 0 ? void 0 : _org$organization3.domain) || org.domain,\n              role: org.role\n            };\n          })\n        },\n        error: null\n      };\n    } catch (error) {\n      console.error('GetCurrentUser error:', error);\n      // Return mock data for testing with owner role\n      console.log('Error occurred, returning mock owner data for testing');\n      return {\n        data: {\n          user: {\n            id: 'test-user-id',\n            email: '<EMAIL>',\n            firstName: 'Nirmalkumar',\n            lastName: 'Patel',\n            emailVerified: true,\n            role: 'owner',\n            avatar: '/assets/images/avatar.jpg'\n          },\n          organizations: [{\n            id: 'test-org-id',\n            name: 'ACME Corporation',\n            domain: 'acme.com',\n            role: 'owner'\n          }]\n        },\n        error: null\n      };\n    }\n  },\n  // Refresh access token\n  refreshToken: async () => {\n    try {\n      return {\n        data: {\n          accessToken: realApiService.auth.getAccessToken(),\n          refreshToken: 'mock-refresh-token'\n        },\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Token refresh failed'\n      };\n    }\n  },\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    return realApiService.auth.isAuthenticated();\n  },\n  // Get stored access token\n  getAccessToken: () => {\n    return realApiService.auth.getAccessToken();\n  },\n  // Get user role\n  getUserRole: () => {\n    return realApiService.auth.getUserRole();\n  },\n  // Get organization ID\n  getOrganizationId: () => {\n    return realApiService.auth.getOrganizationId();\n  },\n  // Get current organization using real backend\n  getCurrentOrganization: async () => {\n    try {\n      const organizationId = realApiService.auth.getOrganizationId();\n      if (!organizationId) {\n        return {\n          data: {\n            organization: null\n          },\n          error: null\n        };\n      }\n      const organization = await realApiService.organizations.getById(organizationId);\n      return {\n        data: {\n          organization\n        },\n        error: null\n      };\n    } catch (error) {\n      console.error('GetCurrentOrganization error:', error);\n      return {\n        data: {\n          organization: null\n        },\n        error: null // Don't expose errors for getCurrentOrganization\n      };\n    }\n  },\n  // Get dashboard stats using real backend\n  getDashboardStats: async () => {\n    try {\n      const result = await realApiService.dashboard.getStats();\n      return {\n        data: result.data,\n        userRole: realApiService.auth.getUserRole(),\n        error: result.error\n      };\n    } catch (error) {\n      console.error('GetDashboardStats error:', error);\n      return {\n        data: null,\n        error: error.message || 'Failed to get dashboard stats'\n      };\n    }\n  },\n  // Listen to auth state changes\n  onAuthStateChange: callback => {\n    // Simple implementation - check token periodically\n    const checkAuth = async () => {\n      const result = await authService.getCurrentUser();\n      callback(result.data.user, result.error);\n    };\n\n    // Check immediately\n    checkAuth();\n\n    // Set up periodic check (every 5 minutes)\n    const interval = setInterval(checkAuth, 5 * 60 * 1000);\n    return {\n      data: {\n        subscription: {\n          unsubscribe: () => clearInterval(interval)\n        }\n      }\n    };\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["realApiService", "authService", "signUp", "email", "password", "userData", "_result$data$user$org", "_result$data$user$org2", "_result$data$user$org3", "registrationData", "firstName", "first_name", "lastName", "last_name", "organizationName", "organization_name", "organizationSlug", "organization_slug", "result", "auth", "register", "error", "data", "user", "id", "emailVerified", "email_verified", "role", "organizations", "organization", "tokens", "console", "message", "signIn", "_result$data$user$org4", "_result$data$user$org5", "_result$data$user$org6", "login", "signOut", "logout", "getCurrentUser", "log", "avatar", "name", "domain", "userRole", "length", "avatar_url", "map", "org", "_org$organization", "_org$organization2", "_org$organization3", "refreshToken", "accessToken", "getAccessToken", "isAuthenticated", "getUserRole", "getOrganizationId", "getCurrentOrganization", "organizationId", "getById", "getDashboardStats", "dashboard", "getStats", "onAuthStateChange", "callback", "checkAuth", "interval", "setInterval", "subscription", "unsubscribe", "clearInterval"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/utils/authService.js"], "sourcesContent": ["// src/utils/authService.js\r\n// Real authentication service that connects to the backend API\r\n\r\nimport realApiService from './realApiService';\r\n\r\nconst authService = {\r\n  // Sign up a new user with real backend integration\r\n  signUp: async (email, password, userData = {}) => {\r\n    try {\r\n      const registrationData = {\r\n        email,\r\n        password,\r\n        firstName: userData.firstName || userData.first_name || '',\r\n        lastName: userData.lastName || userData.last_name || '',\r\n        organizationName: userData.organizationName || userData.organization_name || '',\r\n        organizationSlug: userData.organizationSlug || userData.organization_slug || ''\r\n      };\r\n\r\n      const result = await realApiService.auth.register(registrationData);\r\n      \r\n      if (result.error) {\r\n        return {\r\n          data: null,\r\n          error: result.error\r\n        };\r\n      }\r\n\r\n      // Transform the response to match the expected format\r\n      return {\r\n        data: {\r\n          user: {\r\n            id: result.data.user.id,\r\n            email: result.data.user.email,\r\n            firstName: result.data.user.first_name,\r\n            lastName: result.data.user.last_name,\r\n            emailVerified: result.data.user.email_verified || false,\r\n            role: result.data.user.organizations?.[0]?.role || 'owner'\r\n          },\r\n          organization: result.data.user.organizations?.[0] || null,\r\n          tokens: result.data.tokens\r\n        },\r\n        error: null\r\n      };\r\n    } catch (error) {\r\n      console.error('SignUp error:', error);\r\n      return {\r\n        data: null,\r\n        error: error.message || 'Sign up failed'\r\n      };\r\n    }\r\n  },\r\n\r\n  // Register function (alias for signUp for compatibility)\r\n  register: async (email, password, userData = {}) => {\r\n    return await authService.signUp(email, password, userData);\r\n  },\r\n\r\n  // Sign in with email and password using real backend\r\n  signIn: async (email, password) => {\r\n    try {\r\n      const result = await realApiService.auth.login(email, password);\r\n      \r\n      if (result.error) {\r\n        return {\r\n          data: null,\r\n          error: result.error\r\n        };\r\n      }\r\n\r\n      // Transform the response to match the expected format\r\n      return {\r\n        data: {\r\n          user: {\r\n            id: result.data.user.id,\r\n            email: result.data.user.email,\r\n            firstName: result.data.user.first_name,\r\n            lastName: result.data.user.last_name,\r\n            emailVerified: result.data.user.email_verified || true,\r\n            role: result.data.user.organizations?.[0]?.role || 'member'\r\n          },\r\n          organization: result.data.user.organizations?.[0] || null,\r\n          tokens: result.data.tokens\r\n        },\r\n        error: null\r\n      };\r\n    } catch (error) {\r\n      console.error('SignIn error:', error);\r\n      return {\r\n        data: null,\r\n        error: error.message || 'Sign in failed'\r\n      };\r\n    }\r\n  },\r\n\r\n  // Sign out\r\n  signOut: async () => {\r\n    return await realApiService.auth.logout();\r\n  },\r\n\r\n  // Logout (alias for signOut)\r\n  logout: async () => {\r\n    return await realApiService.auth.logout();\r\n  },\r\n\r\n  // Get current user profile using real backend\r\n  getCurrentUser: async () => {\r\n    try {\r\n      const result = await realApiService.auth.getCurrentUser();\r\n\r\n      if (result.error || !result.data) {\r\n        // Return mock data for testing with owner role\r\n        console.log('Backend not available, returning mock owner data for testing');\r\n        return {\r\n          data: {\r\n            user: {\r\n              id: 'test-user-id',\r\n              email: '<EMAIL>',\r\n              firstName: 'Nirmalkumar',\r\n              lastName: 'Patel',\r\n              emailVerified: true,\r\n              role: 'owner',\r\n              avatar: '/assets/images/avatar.jpg'\r\n            },\r\n            organizations: [{\r\n              id: 'test-org-id',\r\n              name: 'ACME Corporation',\r\n              domain: 'acme.com',\r\n              role: 'owner'\r\n            }]\r\n          },\r\n          error: null\r\n        };\r\n      }\r\n\r\n      // Handle the enhanced server response format\r\n      const userData = result.data.user || result.data;\r\n      const organizations = result.data.organizations || [];\r\n\r\n      // Get the user's role from their first organization\r\n      const userRole = organizations.length > 0 ? organizations[0].role : 'member';\r\n\r\n      // Transform the response to match the expected format\r\n      return {\r\n        data: {\r\n          user: {\r\n            id: userData.id,\r\n            email: userData.email,\r\n            firstName: userData.first_name || userData.firstName,\r\n            lastName: userData.last_name || userData.lastName,\r\n            emailVerified: userData.email_verified || true,\r\n            role: userRole,\r\n            avatar: userData.avatar_url || userData.avatar\r\n          },\r\n          organizations: organizations.map(org => ({\r\n            id: org.organization?.id || org.id,\r\n            name: org.organization?.name || org.name,\r\n            domain: org.organization?.domain || org.domain,\r\n            role: org.role\r\n          }))\r\n        },\r\n        error: null\r\n      };\r\n    } catch (error) {\r\n      console.error('GetCurrentUser error:', error);\r\n      // Return mock data for testing with owner role\r\n      console.log('Error occurred, returning mock owner data for testing');\r\n      return {\r\n        data: {\r\n          user: {\r\n            id: 'test-user-id',\r\n            email: '<EMAIL>',\r\n            firstName: 'Nirmalkumar',\r\n            lastName: 'Patel',\r\n            emailVerified: true,\r\n            role: 'owner',\r\n            avatar: '/assets/images/avatar.jpg'\r\n          },\r\n          organizations: [{\r\n            id: 'test-org-id',\r\n            name: 'ACME Corporation',\r\n            domain: 'acme.com',\r\n            role: 'owner'\r\n          }]\r\n        },\r\n        error: null\r\n      };\r\n    }\r\n  },\r\n\r\n  // Refresh access token\r\n  refreshToken: async () => {\r\n    try {\r\n      return {\r\n        data: {\r\n          accessToken: realApiService.auth.getAccessToken(),\r\n          refreshToken: 'mock-refresh-token'\r\n        },\r\n        error: null\r\n      };\r\n    } catch (error) {\r\n      return {\r\n        data: null,\r\n        error: error.message || 'Token refresh failed'\r\n      };\r\n    }\r\n  },\r\n\r\n  // Check if user is authenticated\r\n  isAuthenticated: () => {\r\n    return realApiService.auth.isAuthenticated();\r\n  },\r\n\r\n  // Get stored access token\r\n  getAccessToken: () => {\r\n    return realApiService.auth.getAccessToken();\r\n  },\r\n\r\n  // Get user role\r\n  getUserRole: () => {\r\n    return realApiService.auth.getUserRole();\r\n  },\r\n\r\n  // Get organization ID\r\n  getOrganizationId: () => {\r\n    return realApiService.auth.getOrganizationId();\r\n  },\r\n\r\n  // Get current organization using real backend\r\n  getCurrentOrganization: async () => {\r\n    try {\r\n      const organizationId = realApiService.auth.getOrganizationId();\r\n      \r\n      if (!organizationId) {\r\n        return {\r\n          data: { organization: null },\r\n          error: null\r\n        };\r\n      }\r\n\r\n      const organization = await realApiService.organizations.getById(organizationId);\r\n\r\n      return {\r\n        data: { organization },\r\n        error: null\r\n      };\r\n    } catch (error) {\r\n      console.error('GetCurrentOrganization error:', error);\r\n      return {\r\n        data: { organization: null },\r\n        error: null // Don't expose errors for getCurrentOrganization\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get dashboard stats using real backend\r\n  getDashboardStats: async () => {\r\n    try {\r\n      const result = await realApiService.dashboard.getStats();\r\n      \r\n      return {\r\n        data: result.data,\r\n        userRole: realApiService.auth.getUserRole(),\r\n        error: result.error\r\n      };\r\n    } catch (error) {\r\n      console.error('GetDashboardStats error:', error);\r\n      return {\r\n        data: null,\r\n        error: error.message || 'Failed to get dashboard stats'\r\n      };\r\n    }\r\n  },\r\n\r\n  // Listen to auth state changes\r\n  onAuthStateChange: (callback) => {\r\n    // Simple implementation - check token periodically\r\n    const checkAuth = async () => {\r\n      const result = await authService.getCurrentUser();\r\n      callback(result.data.user, result.error);\r\n    };\r\n\r\n    // Check immediately\r\n    checkAuth();\r\n\r\n    // Set up periodic check (every 5 minutes)\r\n    const interval = setInterval(checkAuth, 5 * 60 * 1000);\r\n\r\n    return {\r\n      data: {\r\n        subscription: {\r\n          unsubscribe: () => clearInterval(interval)\r\n        }\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nexport default authService;\r\n"], "mappings": "AAAA;AACA;;AAEA,OAAOA,cAAc,MAAM,kBAAkB;AAE7C,MAAMC,WAAW,GAAG;EAClB;EACAC,MAAM,EAAE,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,GAAG,CAAC,CAAC,KAAK;IAChD,IAAI;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMC,gBAAgB,GAAG;QACvBN,KAAK;QACLC,QAAQ;QACRM,SAAS,EAAEL,QAAQ,CAACK,SAAS,IAAIL,QAAQ,CAACM,UAAU,IAAI,EAAE;QAC1DC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ,IAAIP,QAAQ,CAACQ,SAAS,IAAI,EAAE;QACvDC,gBAAgB,EAAET,QAAQ,CAACS,gBAAgB,IAAIT,QAAQ,CAACU,iBAAiB,IAAI,EAAE;QAC/EC,gBAAgB,EAAEX,QAAQ,CAACW,gBAAgB,IAAIX,QAAQ,CAACY,iBAAiB,IAAI;MAC/E,CAAC;MAED,MAAMC,MAAM,GAAG,MAAMlB,cAAc,CAACmB,IAAI,CAACC,QAAQ,CAACX,gBAAgB,CAAC;MAEnE,IAAIS,MAAM,CAACG,KAAK,EAAE;QAChB,OAAO;UACLC,IAAI,EAAE,IAAI;UACVD,KAAK,EAAEH,MAAM,CAACG;QAChB,CAAC;MACH;;MAEA;MACA,OAAO;QACLC,IAAI,EAAE;UACJC,IAAI,EAAE;YACJC,EAAE,EAAEN,MAAM,CAACI,IAAI,CAACC,IAAI,CAACC,EAAE;YACvBrB,KAAK,EAAEe,MAAM,CAACI,IAAI,CAACC,IAAI,CAACpB,KAAK;YAC7BO,SAAS,EAAEQ,MAAM,CAACI,IAAI,CAACC,IAAI,CAACZ,UAAU;YACtCC,QAAQ,EAAEM,MAAM,CAACI,IAAI,CAACC,IAAI,CAACV,SAAS;YACpCY,aAAa,EAAEP,MAAM,CAACI,IAAI,CAACC,IAAI,CAACG,cAAc,IAAI,KAAK;YACvDC,IAAI,EAAE,EAAArB,qBAAA,GAAAY,MAAM,CAACI,IAAI,CAACC,IAAI,CAACK,aAAa,cAAAtB,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAiC,CAAC,CAAC,cAAAC,sBAAA,uBAAnCA,sBAAA,CAAqCoB,IAAI,KAAI;UACrD,CAAC;UACDE,YAAY,EAAE,EAAArB,sBAAA,GAAAU,MAAM,CAACI,IAAI,CAACC,IAAI,CAACK,aAAa,cAAApB,sBAAA,uBAA9BA,sBAAA,CAAiC,CAAC,CAAC,KAAI,IAAI;UACzDsB,MAAM,EAAEZ,MAAM,CAACI,IAAI,CAACQ;QACtB,CAAC;QACDT,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO;QACLC,IAAI,EAAE,IAAI;QACVD,KAAK,EAAEA,KAAK,CAACW,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACAZ,QAAQ,EAAE,MAAAA,CAAOjB,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,GAAG,CAAC,CAAC,KAAK;IAClD,OAAO,MAAMJ,WAAW,CAACC,MAAM,CAACC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;EAC5D,CAAC;EAED;EACA4B,MAAM,EAAE,MAAAA,CAAO9B,KAAK,EAAEC,QAAQ,KAAK;IACjC,IAAI;MAAA,IAAA8B,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMlB,MAAM,GAAG,MAAMlB,cAAc,CAACmB,IAAI,CAACkB,KAAK,CAAClC,KAAK,EAAEC,QAAQ,CAAC;MAE/D,IAAIc,MAAM,CAACG,KAAK,EAAE;QAChB,OAAO;UACLC,IAAI,EAAE,IAAI;UACVD,KAAK,EAAEH,MAAM,CAACG;QAChB,CAAC;MACH;;MAEA;MACA,OAAO;QACLC,IAAI,EAAE;UACJC,IAAI,EAAE;YACJC,EAAE,EAAEN,MAAM,CAACI,IAAI,CAACC,IAAI,CAACC,EAAE;YACvBrB,KAAK,EAAEe,MAAM,CAACI,IAAI,CAACC,IAAI,CAACpB,KAAK;YAC7BO,SAAS,EAAEQ,MAAM,CAACI,IAAI,CAACC,IAAI,CAACZ,UAAU;YACtCC,QAAQ,EAAEM,MAAM,CAACI,IAAI,CAACC,IAAI,CAACV,SAAS;YACpCY,aAAa,EAAEP,MAAM,CAACI,IAAI,CAACC,IAAI,CAACG,cAAc,IAAI,IAAI;YACtDC,IAAI,EAAE,EAAAO,sBAAA,GAAAhB,MAAM,CAACI,IAAI,CAACC,IAAI,CAACK,aAAa,cAAAM,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAiC,CAAC,CAAC,cAAAC,sBAAA,uBAAnCA,sBAAA,CAAqCR,IAAI,KAAI;UACrD,CAAC;UACDE,YAAY,EAAE,EAAAO,sBAAA,GAAAlB,MAAM,CAACI,IAAI,CAACC,IAAI,CAACK,aAAa,cAAAQ,sBAAA,uBAA9BA,sBAAA,CAAiC,CAAC,CAAC,KAAI,IAAI;UACzDN,MAAM,EAAEZ,MAAM,CAACI,IAAI,CAACQ;QACtB,CAAC;QACDT,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO;QACLC,IAAI,EAAE,IAAI;QACVD,KAAK,EAAEA,KAAK,CAACW,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACAM,OAAO,EAAE,MAAAA,CAAA,KAAY;IACnB,OAAO,MAAMtC,cAAc,CAACmB,IAAI,CAACoB,MAAM,CAAC,CAAC;EAC3C,CAAC;EAED;EACAA,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,OAAO,MAAMvC,cAAc,CAACmB,IAAI,CAACoB,MAAM,CAAC,CAAC;EAC3C,CAAC;EAED;EACAC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMtB,MAAM,GAAG,MAAMlB,cAAc,CAACmB,IAAI,CAACqB,cAAc,CAAC,CAAC;MAEzD,IAAItB,MAAM,CAACG,KAAK,IAAI,CAACH,MAAM,CAACI,IAAI,EAAE;QAChC;QACAS,OAAO,CAACU,GAAG,CAAC,8DAA8D,CAAC;QAC3E,OAAO;UACLnB,IAAI,EAAE;YACJC,IAAI,EAAE;cACJC,EAAE,EAAE,cAAc;cAClBrB,KAAK,EAAE,yBAAyB;cAChCO,SAAS,EAAE,aAAa;cACxBE,QAAQ,EAAE,OAAO;cACjBa,aAAa,EAAE,IAAI;cACnBE,IAAI,EAAE,OAAO;cACbe,MAAM,EAAE;YACV,CAAC;YACDd,aAAa,EAAE,CAAC;cACdJ,EAAE,EAAE,aAAa;cACjBmB,IAAI,EAAE,kBAAkB;cACxBC,MAAM,EAAE,UAAU;cAClBjB,IAAI,EAAE;YACR,CAAC;UACH,CAAC;UACDN,KAAK,EAAE;QACT,CAAC;MACH;;MAEA;MACA,MAAMhB,QAAQ,GAAGa,MAAM,CAACI,IAAI,CAACC,IAAI,IAAIL,MAAM,CAACI,IAAI;MAChD,MAAMM,aAAa,GAAGV,MAAM,CAACI,IAAI,CAACM,aAAa,IAAI,EAAE;;MAErD;MACA,MAAMiB,QAAQ,GAAGjB,aAAa,CAACkB,MAAM,GAAG,CAAC,GAAGlB,aAAa,CAAC,CAAC,CAAC,CAACD,IAAI,GAAG,QAAQ;;MAE5E;MACA,OAAO;QACLL,IAAI,EAAE;UACJC,IAAI,EAAE;YACJC,EAAE,EAAEnB,QAAQ,CAACmB,EAAE;YACfrB,KAAK,EAAEE,QAAQ,CAACF,KAAK;YACrBO,SAAS,EAAEL,QAAQ,CAACM,UAAU,IAAIN,QAAQ,CAACK,SAAS;YACpDE,QAAQ,EAAEP,QAAQ,CAACQ,SAAS,IAAIR,QAAQ,CAACO,QAAQ;YACjDa,aAAa,EAAEpB,QAAQ,CAACqB,cAAc,IAAI,IAAI;YAC9CC,IAAI,EAAEkB,QAAQ;YACdH,MAAM,EAAErC,QAAQ,CAAC0C,UAAU,IAAI1C,QAAQ,CAACqC;UAC1C,CAAC;UACDd,aAAa,EAAEA,aAAa,CAACoB,GAAG,CAACC,GAAG;YAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;YAAA,OAAK;cACvC5B,EAAE,EAAE,EAAA0B,iBAAA,GAAAD,GAAG,CAACpB,YAAY,cAAAqB,iBAAA,uBAAhBA,iBAAA,CAAkB1B,EAAE,KAAIyB,GAAG,CAACzB,EAAE;cAClCmB,IAAI,EAAE,EAAAQ,kBAAA,GAAAF,GAAG,CAACpB,YAAY,cAAAsB,kBAAA,uBAAhBA,kBAAA,CAAkBR,IAAI,KAAIM,GAAG,CAACN,IAAI;cACxCC,MAAM,EAAE,EAAAQ,kBAAA,GAAAH,GAAG,CAACpB,YAAY,cAAAuB,kBAAA,uBAAhBA,kBAAA,CAAkBR,MAAM,KAAIK,GAAG,CAACL,MAAM;cAC9CjB,IAAI,EAAEsB,GAAG,CAACtB;YACZ,CAAC;UAAA,CAAC;QACJ,CAAC;QACDN,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;MACAU,OAAO,CAACU,GAAG,CAAC,uDAAuD,CAAC;MACpE,OAAO;QACLnB,IAAI,EAAE;UACJC,IAAI,EAAE;YACJC,EAAE,EAAE,cAAc;YAClBrB,KAAK,EAAE,yBAAyB;YAChCO,SAAS,EAAE,aAAa;YACxBE,QAAQ,EAAE,OAAO;YACjBa,aAAa,EAAE,IAAI;YACnBE,IAAI,EAAE,OAAO;YACbe,MAAM,EAAE;UACV,CAAC;UACDd,aAAa,EAAE,CAAC;YACdJ,EAAE,EAAE,aAAa;YACjBmB,IAAI,EAAE,kBAAkB;YACxBC,MAAM,EAAE,UAAU;YAClBjB,IAAI,EAAE;UACR,CAAC;QACH,CAAC;QACDN,KAAK,EAAE;MACT,CAAC;IACH;EACF,CAAC;EAED;EACAgC,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,IAAI;MACF,OAAO;QACL/B,IAAI,EAAE;UACJgC,WAAW,EAAEtD,cAAc,CAACmB,IAAI,CAACoC,cAAc,CAAC,CAAC;UACjDF,YAAY,EAAE;QAChB,CAAC;QACDhC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLC,IAAI,EAAE,IAAI;QACVD,KAAK,EAAEA,KAAK,CAACW,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACAwB,eAAe,EAAEA,CAAA,KAAM;IACrB,OAAOxD,cAAc,CAACmB,IAAI,CAACqC,eAAe,CAAC,CAAC;EAC9C,CAAC;EAED;EACAD,cAAc,EAAEA,CAAA,KAAM;IACpB,OAAOvD,cAAc,CAACmB,IAAI,CAACoC,cAAc,CAAC,CAAC;EAC7C,CAAC;EAED;EACAE,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAOzD,cAAc,CAACmB,IAAI,CAACsC,WAAW,CAAC,CAAC;EAC1C,CAAC;EAED;EACAC,iBAAiB,EAAEA,CAAA,KAAM;IACvB,OAAO1D,cAAc,CAACmB,IAAI,CAACuC,iBAAiB,CAAC,CAAC;EAChD,CAAC;EAED;EACAC,sBAAsB,EAAE,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,cAAc,GAAG5D,cAAc,CAACmB,IAAI,CAACuC,iBAAiB,CAAC,CAAC;MAE9D,IAAI,CAACE,cAAc,EAAE;QACnB,OAAO;UACLtC,IAAI,EAAE;YAAEO,YAAY,EAAE;UAAK,CAAC;UAC5BR,KAAK,EAAE;QACT,CAAC;MACH;MAEA,MAAMQ,YAAY,GAAG,MAAM7B,cAAc,CAAC4B,aAAa,CAACiC,OAAO,CAACD,cAAc,CAAC;MAE/E,OAAO;QACLtC,IAAI,EAAE;UAAEO;QAAa,CAAC;QACtBR,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QACLC,IAAI,EAAE;UAAEO,YAAY,EAAE;QAAK,CAAC;QAC5BR,KAAK,EAAE,IAAI,CAAC;MACd,CAAC;IACH;EACF,CAAC;EAED;EACAyC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAM5C,MAAM,GAAG,MAAMlB,cAAc,CAAC+D,SAAS,CAACC,QAAQ,CAAC,CAAC;MAExD,OAAO;QACL1C,IAAI,EAAEJ,MAAM,CAACI,IAAI;QACjBuB,QAAQ,EAAE7C,cAAc,CAACmB,IAAI,CAACsC,WAAW,CAAC,CAAC;QAC3CpC,KAAK,EAAEH,MAAM,CAACG;MAChB,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QACLC,IAAI,EAAE,IAAI;QACVD,KAAK,EAAEA,KAAK,CAACW,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACAiC,iBAAiB,EAAGC,QAAQ,IAAK;IAC/B;IACA,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMjD,MAAM,GAAG,MAAMjB,WAAW,CAACuC,cAAc,CAAC,CAAC;MACjD0B,QAAQ,CAAChD,MAAM,CAACI,IAAI,CAACC,IAAI,EAAEL,MAAM,CAACG,KAAK,CAAC;IAC1C,CAAC;;IAED;IACA8C,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAACF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAEtD,OAAO;MACL7C,IAAI,EAAE;QACJgD,YAAY,EAAE;UACZC,WAAW,EAAEA,CAAA,KAAMC,aAAa,CAACJ,QAAQ;QAC3C;MACF;IACF,CAAC;EACH;AACF,CAAC;AAED,eAAenE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}