{"ast": null, "code": "import realApiService from './realApiService';\n\n/**\n * Notification service for managing user notifications\n */\n\n// Get notifications for the current user\nexport const getNotifications = async (filters = {}) => {\n  try {\n    console.log('Fetching notifications with filters:', filters);\n\n    // For now, return mock data since the API endpoint doesn't exist yet\n    const mockNotifications = [{\n      id: '1',\n      title: 'Welcome to Agno WorkSphere',\n      message: 'Your account has been successfully created.',\n      type: 'info',\n      priority: 'medium',\n      read: false,\n      createdAt: new Date().toISOString(),\n      actionUrl: null\n    }, {\n      id: '2',\n      title: 'Project Update',\n      message: 'New task has been assigned to you.',\n      type: 'task',\n      priority: 'high',\n      read: false,\n      createdAt: new Date(Date.now() - 3600000).toISOString(),\n      actionUrl: '/kanban-board'\n    }];\n    return {\n      success: true,\n      data: mockNotifications,\n      pagination: {\n        page: filters.page || 1,\n        limit: filters.limit || 20,\n        total: mockNotifications.length,\n        totalPages: 1\n      }\n    };\n  } catch (error) {\n    console.error('Failed to fetch notifications:', error);\n\n    // Return empty array on error\n    return {\n      success: false,\n      data: [],\n      error: error.message\n    };\n  }\n};\n\n// Mark notification as read\nexport const markNotificationAsRead = async notificationId => {\n  try {\n    console.log(`Marking notification ${notificationId} as read`);\n\n    // Mock successful response\n    return {\n      success: true,\n      message: 'Notification marked as read',\n      data: {\n        id: notificationId,\n        read: true\n      }\n    };\n  } catch (error) {\n    console.error('Failed to mark notification as read:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Mark all notifications as read\nexport const markAllNotificationsAsRead = async () => {\n  try {\n    console.log('Marking all notifications as read');\n\n    // Mock successful response\n    return {\n      success: true,\n      message: 'All notifications marked as read',\n      data: {\n        markedCount: 2\n      }\n    };\n  } catch (error) {\n    console.error('Failed to mark all notifications as read:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Create a welcome notification for new users\nexport const createWelcomeNotification = async (userId, organizationName) => {\n  try {\n    console.log(`Creating welcome notification for user ${userId} in organization ${organizationName}`);\n    const welcomeNotification = {\n      id: `welcome_${userId}_${Date.now()}`,\n      type: 'welcome',\n      title: 'Welcome to Agno WorkSphere!',\n      message: `Welcome to ${organizationName}! We're excited to have you on board. Start by exploring your dashboard and setting up your first project.`,\n      priority: 'high',\n      read: false,\n      createdAt: new Date().toISOString(),\n      data: {\n        isWelcome: true,\n        organizationName,\n        actions: [{\n          label: 'Get Started',\n          variant: 'default',\n          action: 'tour'\n        }, {\n          label: 'View Profile',\n          variant: 'outline',\n          action: 'profile'\n        }]\n      }\n    };\n\n    // Mock successful creation\n    return {\n      success: true,\n      data: welcomeNotification,\n      message: 'Welcome notification created successfully'\n    };\n  } catch (error) {\n    console.error('Failed to create welcome notification:', error);\n    // Don't throw error for welcome notifications - they're not critical\n    return null;\n  }\n};\n\n// Create a notification\nexport const createNotification = async notificationData => {\n  try {\n    console.log('Creating notification:', notificationData);\n    const notification = {\n      id: `notif_${Date.now()}`,\n      ...notificationData,\n      createdAt: new Date().toISOString(),\n      read: false\n    };\n\n    // Mock successful creation\n    return {\n      success: true,\n      data: notification,\n      message: 'Notification created successfully'\n    };\n  } catch (error) {\n    console.error('Failed to create notification:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Delete notification\nexport const deleteNotification = async notificationId => {\n  try {\n    console.log(`Deleting notification ${notificationId}`);\n\n    // Mock successful deletion\n    return {\n      success: true,\n      message: 'Notification deleted successfully',\n      data: {\n        id: notificationId\n      }\n    };\n  } catch (error) {\n    console.error('Failed to delete notification:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Get notification preferences\nexport const getNotificationPreferences = async () => {\n  try {\n    console.log('Fetching notification preferences');\n\n    // Mock preferences\n    const mockPreferences = {\n      email: true,\n      push: true,\n      inApp: true,\n      types: {\n        tasks: true,\n        projects: true,\n        mentions: true,\n        deadlines: true,\n        system: false\n      }\n    };\n    return mockPreferences;\n  } catch (error) {\n    console.error('Failed to fetch notification preferences:', error);\n    return {};\n  }\n};\n\n// Update notification preferences\nexport const updateNotificationPreferences = async preferences => {\n  try {\n    console.log('Updating notification preferences:', preferences);\n\n    // Mock successful update\n    return {\n      success: true,\n      data: preferences,\n      message: 'Notification preferences updated successfully'\n    };\n  } catch (error) {\n    console.error('Failed to update notification preferences:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Check if user is a first-time user and needs welcome notification\nexport const checkFirstTimeUser = async () => {\n  try {\n    // Check if user has any notifications or if this is their first login\n    const result = await getNotifications({\n      limit: 1\n    });\n    const notifications = result.data || [];\n\n    // If no notifications exist, this might be a first-time user\n    if (notifications.length === 0) {\n      return true;\n    }\n\n    // Check if there's already a welcome notification\n    const hasWelcomeNotification = notifications.some(n => {\n      var _n$data;\n      return n.type === 'welcome' || ((_n$data = n.data) === null || _n$data === void 0 ? void 0 : _n$data.isWelcome);\n    });\n    return !hasWelcomeNotification;\n  } catch (error) {\n    console.error('Failed to check first-time user status:', error);\n    return false;\n  }\n};\n\n// Generate mock notifications for development/fallback\nexport const generateMockNotifications = () => {\n  return [{\n    id: 'welcome_001',\n    type: 'welcome',\n    title: 'Welcome to Agno WorkSphere!',\n    message: 'Welcome to your new workspace! Start by exploring your dashboard and setting up your first project.',\n    timestamp: new Date(),\n    isRead: false,\n    read: false,\n    priority: 'high',\n    data: {\n      isWelcome: true,\n      actions: [{\n        label: 'Get Started',\n        variant: 'default',\n        action: 'tour'\n      }, {\n        label: 'View Profile',\n        variant: 'outline',\n        action: 'profile'\n      }]\n    }\n  }];\n};\n\n// Default export\nconst notificationService = {\n  getNotifications,\n  markNotificationAsRead,\n  markAllNotificationsAsRead,\n  createWelcomeNotification,\n  createNotification,\n  deleteNotification,\n  getNotificationPreferences,\n  updateNotificationPreferences,\n  checkFirstTimeUser,\n  generateMockNotifications\n};\nexport default notificationService;", "map": {"version": 3, "names": ["realApiService", "getNotifications", "filters", "console", "log", "mockNotifications", "id", "title", "message", "type", "priority", "read", "createdAt", "Date", "toISOString", "actionUrl", "now", "success", "data", "pagination", "page", "limit", "total", "length", "totalPages", "error", "markNotificationAsRead", "notificationId", "markAllNotificationsAsRead", "markedCount", "createWelcomeNotification", "userId", "organizationName", "welcomeNotification", "isWelcome", "actions", "label", "variant", "action", "createNotification", "notificationData", "notification", "deleteNotification", "getNotificationPreferences", "mockPreferences", "email", "push", "inApp", "types", "tasks", "projects", "mentions", "deadlines", "system", "updateNotificationPreferences", "preferences", "checkFirstTimeUser", "result", "notifications", "hasWelcomeNotification", "some", "n", "_n$data", "generateMockNotifications", "timestamp", "isRead", "notificationService"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/utils/notificationService.js"], "sourcesContent": ["import realApiService from './realApiService';\n\n/**\n * Notification service for managing user notifications\n */\n\n// Get notifications for the current user\nexport const getNotifications = async (filters = {}) => {\n  try {\n    console.log('Fetching notifications with filters:', filters);\n\n    // For now, return mock data since the API endpoint doesn't exist yet\n    const mockNotifications = [\n      {\n        id: '1',\n        title: 'Welcome to Agno WorkSphere',\n        message: 'Your account has been successfully created.',\n        type: 'info',\n        priority: 'medium',\n        read: false,\n        createdAt: new Date().toISOString(),\n        actionUrl: null\n      },\n      {\n        id: '2',\n        title: 'Project Update',\n        message: 'New task has been assigned to you.',\n        type: 'task',\n        priority: 'high',\n        read: false,\n        createdAt: new Date(Date.now() - 3600000).toISOString(),\n        actionUrl: '/kanban-board'\n      }\n    ];\n\n    return {\n      success: true,\n      data: mockNotifications,\n      pagination: {\n        page: filters.page || 1,\n        limit: filters.limit || 20,\n        total: mockNotifications.length,\n        totalPages: 1\n      }\n    };\n  } catch (error) {\n    console.error('Failed to fetch notifications:', error);\n\n    // Return empty array on error\n    return {\n      success: false,\n      data: [],\n      error: error.message\n    };\n  }\n};\n\n// Mark notification as read\nexport const markNotificationAsRead = async (notificationId) => {\n  try {\n    console.log(`Marking notification ${notificationId} as read`);\n\n    // Mock successful response\n    return {\n      success: true,\n      message: 'Notification marked as read',\n      data: { id: notificationId, read: true }\n    };\n  } catch (error) {\n    console.error('Failed to mark notification as read:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Mark all notifications as read\nexport const markAllNotificationsAsRead = async () => {\n  try {\n    console.log('Marking all notifications as read');\n\n    // Mock successful response\n    return {\n      success: true,\n      message: 'All notifications marked as read',\n      data: { markedCount: 2 }\n    };\n  } catch (error) {\n    console.error('Failed to mark all notifications as read:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Create a welcome notification for new users\nexport const createWelcomeNotification = async (userId, organizationName) => {\n  try {\n    console.log(`Creating welcome notification for user ${userId} in organization ${organizationName}`);\n\n    const welcomeNotification = {\n      id: `welcome_${userId}_${Date.now()}`,\n      type: 'welcome',\n      title: 'Welcome to Agno WorkSphere!',\n      message: `Welcome to ${organizationName}! We're excited to have you on board. Start by exploring your dashboard and setting up your first project.`,\n      priority: 'high',\n      read: false,\n      createdAt: new Date().toISOString(),\n      data: {\n        isWelcome: true,\n        organizationName,\n        actions: [\n          { label: 'Get Started', variant: 'default', action: 'tour' },\n          { label: 'View Profile', variant: 'outline', action: 'profile' }\n        ]\n      }\n    };\n\n    // Mock successful creation\n    return {\n      success: true,\n      data: welcomeNotification,\n      message: 'Welcome notification created successfully'\n    };\n  } catch (error) {\n    console.error('Failed to create welcome notification:', error);\n    // Don't throw error for welcome notifications - they're not critical\n    return null;\n  }\n};\n\n// Create a notification\nexport const createNotification = async (notificationData) => {\n  try {\n    console.log('Creating notification:', notificationData);\n\n    const notification = {\n      id: `notif_${Date.now()}`,\n      ...notificationData,\n      createdAt: new Date().toISOString(),\n      read: false\n    };\n\n    // Mock successful creation\n    return {\n      success: true,\n      data: notification,\n      message: 'Notification created successfully'\n    };\n  } catch (error) {\n    console.error('Failed to create notification:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Delete notification\nexport const deleteNotification = async (notificationId) => {\n  try {\n    console.log(`Deleting notification ${notificationId}`);\n\n    // Mock successful deletion\n    return {\n      success: true,\n      message: 'Notification deleted successfully',\n      data: { id: notificationId }\n    };\n  } catch (error) {\n    console.error('Failed to delete notification:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Get notification preferences\nexport const getNotificationPreferences = async () => {\n  try {\n    console.log('Fetching notification preferences');\n\n    // Mock preferences\n    const mockPreferences = {\n      email: true,\n      push: true,\n      inApp: true,\n      types: {\n        tasks: true,\n        projects: true,\n        mentions: true,\n        deadlines: true,\n        system: false\n      }\n    };\n\n    return mockPreferences;\n  } catch (error) {\n    console.error('Failed to fetch notification preferences:', error);\n    return {};\n  }\n};\n\n// Update notification preferences\nexport const updateNotificationPreferences = async (preferences) => {\n  try {\n    console.log('Updating notification preferences:', preferences);\n\n    // Mock successful update\n    return {\n      success: true,\n      data: preferences,\n      message: 'Notification preferences updated successfully'\n    };\n  } catch (error) {\n    console.error('Failed to update notification preferences:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Check if user is a first-time user and needs welcome notification\nexport const checkFirstTimeUser = async () => {\n  try {\n    // Check if user has any notifications or if this is their first login\n    const result = await getNotifications({ limit: 1 });\n    const notifications = result.data || [];\n\n    // If no notifications exist, this might be a first-time user\n    if (notifications.length === 0) {\n      return true;\n    }\n\n    // Check if there's already a welcome notification\n    const hasWelcomeNotification = notifications.some(n => n.type === 'welcome' || n.data?.isWelcome);\n    return !hasWelcomeNotification;\n  } catch (error) {\n    console.error('Failed to check first-time user status:', error);\n    return false;\n  }\n};\n\n// Generate mock notifications for development/fallback\nexport const generateMockNotifications = () => {\n  return [\n    {\n      id: 'welcome_001',\n      type: 'welcome',\n      title: 'Welcome to Agno WorkSphere!',\n      message: 'Welcome to your new workspace! Start by exploring your dashboard and setting up your first project.',\n      timestamp: new Date(),\n      isRead: false,\n      read: false,\n      priority: 'high',\n      data: {\n        isWelcome: true,\n        actions: [\n          { label: 'Get Started', variant: 'default', action: 'tour' },\n          { label: 'View Profile', variant: 'outline', action: 'profile' }\n        ]\n      }\n    }\n  ];\n};\n\n// Default export\nconst notificationService = {\n  getNotifications,\n  markNotificationAsRead,\n  markAllNotificationsAsRead,\n  createWelcomeNotification,\n  createNotification,\n  deleteNotification,\n  getNotificationPreferences,\n  updateNotificationPreferences,\n  checkFirstTimeUser,\n  generateMockNotifications\n};\n\nexport default notificationService;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;;AAE7C;AACA;AACA;;AAEA;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,CAAC,KAAK;EACtD,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEF,OAAO,CAAC;;IAE5D;IACA,MAAMG,iBAAiB,GAAG,CACxB;MACEC,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,4BAA4B;MACnCC,OAAO,EAAE,6CAA6C;MACtDC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,QAAQ;MAClBC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE;IACb,CAAC,EACD;MACET,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,gBAAgB;MACvBC,OAAO,EAAE,oCAAoC;MAC7CC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACF,WAAW,CAAC,CAAC;MACvDC,SAAS,EAAE;IACb,CAAC,CACF;IAED,OAAO;MACLE,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEb,iBAAiB;MACvBc,UAAU,EAAE;QACVC,IAAI,EAAElB,OAAO,CAACkB,IAAI,IAAI,CAAC;QACvBC,KAAK,EAAEnB,OAAO,CAACmB,KAAK,IAAI,EAAE;QAC1BC,KAAK,EAAEjB,iBAAiB,CAACkB,MAAM;QAC/BC,UAAU,EAAE;MACd;IACF,CAAC;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;IAEtD;IACA,OAAO;MACLR,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,EAAE;MACRO,KAAK,EAAEA,KAAK,CAACjB;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMkB,sBAAsB,GAAG,MAAOC,cAAc,IAAK;EAC9D,IAAI;IACFxB,OAAO,CAACC,GAAG,CAAC,wBAAwBuB,cAAc,UAAU,CAAC;;IAE7D;IACA,OAAO;MACLV,OAAO,EAAE,IAAI;MACbT,OAAO,EAAE,6BAA6B;MACtCU,IAAI,EAAE;QAAEZ,EAAE,EAAEqB,cAAc;QAAEhB,IAAI,EAAE;MAAK;IACzC,CAAC;EACH,CAAC,CAAC,OAAOc,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC5D,OAAO;MACLR,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAEA,KAAK,CAACjB;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMoB,0BAA0B,GAAG,MAAAA,CAAA,KAAY;EACpD,IAAI;IACFzB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;IAEhD;IACA,OAAO;MACLa,OAAO,EAAE,IAAI;MACbT,OAAO,EAAE,kCAAkC;MAC3CU,IAAI,EAAE;QAAEW,WAAW,EAAE;MAAE;IACzB,CAAC;EACH,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACjE,OAAO;MACLR,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAEA,KAAK,CAACjB;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMsB,yBAAyB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,gBAAgB,KAAK;EAC3E,IAAI;IACF7B,OAAO,CAACC,GAAG,CAAC,0CAA0C2B,MAAM,oBAAoBC,gBAAgB,EAAE,CAAC;IAEnG,MAAMC,mBAAmB,GAAG;MAC1B3B,EAAE,EAAE,WAAWyB,MAAM,IAAIlB,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE;MACrCP,IAAI,EAAE,SAAS;MACfF,KAAK,EAAE,6BAA6B;MACpCC,OAAO,EAAE,cAAcwB,gBAAgB,4GAA4G;MACnJtB,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCI,IAAI,EAAE;QACJgB,SAAS,EAAE,IAAI;QACfF,gBAAgB;QAChBG,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,aAAa;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAO,CAAC,EAC5D;UAAEF,KAAK,EAAE,cAAc;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAU,CAAC;MAEpE;IACF,CAAC;;IAED;IACA,OAAO;MACLrB,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEe,mBAAmB;MACzBzB,OAAO,EAAE;IACX,CAAC;EACH,CAAC,CAAC,OAAOiB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAC9D;IACA,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,kBAAkB,GAAG,MAAOC,gBAAgB,IAAK;EAC5D,IAAI;IACFrC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoC,gBAAgB,CAAC;IAEvD,MAAMC,YAAY,GAAG;MACnBnC,EAAE,EAAE,SAASO,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE;MACzB,GAAGwB,gBAAgB;MACnB5B,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCH,IAAI,EAAE;IACR,CAAC;;IAED;IACA,OAAO;MACLM,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEuB,YAAY;MAClBjC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,CAAC,OAAOiB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,OAAO;MACLR,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAEA,KAAK,CAACjB;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMkC,kBAAkB,GAAG,MAAOf,cAAc,IAAK;EAC1D,IAAI;IACFxB,OAAO,CAACC,GAAG,CAAC,yBAAyBuB,cAAc,EAAE,CAAC;;IAEtD;IACA,OAAO;MACLV,OAAO,EAAE,IAAI;MACbT,OAAO,EAAE,mCAAmC;MAC5CU,IAAI,EAAE;QAAEZ,EAAE,EAAEqB;MAAe;IAC7B,CAAC;EACH,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,OAAO;MACLR,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAEA,KAAK,CAACjB;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMmC,0BAA0B,GAAG,MAAAA,CAAA,KAAY;EACpD,IAAI;IACFxC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;IAEhD;IACA,MAAMwC,eAAe,GAAG;MACtBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAI;QACfC,MAAM,EAAE;MACV;IACF,CAAC;IAED,OAAOT,eAAe;EACxB,CAAC,CAAC,OAAOnB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACjE,OAAO,CAAC,CAAC;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAM6B,6BAA6B,GAAG,MAAOC,WAAW,IAAK;EAClE,IAAI;IACFpD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmD,WAAW,CAAC;;IAE9D;IACA,OAAO;MACLtC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEqC,WAAW;MACjB/C,OAAO,EAAE;IACX,CAAC;EACH,CAAC,CAAC,OAAOiB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IAClE,OAAO;MACLR,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAEA,KAAK,CAACjB;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMgD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACF;IACA,MAAMC,MAAM,GAAG,MAAMxD,gBAAgB,CAAC;MAAEoB,KAAK,EAAE;IAAE,CAAC,CAAC;IACnD,MAAMqC,aAAa,GAAGD,MAAM,CAACvC,IAAI,IAAI,EAAE;;IAEvC;IACA,IAAIwC,aAAa,CAACnC,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO,IAAI;IACb;;IAEA;IACA,MAAMoC,sBAAsB,GAAGD,aAAa,CAACE,IAAI,CAACC,CAAC;MAAA,IAAAC,OAAA;MAAA,OAAID,CAAC,CAACpD,IAAI,KAAK,SAAS,MAAAqD,OAAA,GAAID,CAAC,CAAC3C,IAAI,cAAA4C,OAAA,uBAANA,OAAA,CAAQ5B,SAAS;IAAA,EAAC;IACjG,OAAO,CAACyB,sBAAsB;EAChC,CAAC,CAAC,OAAOlC,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMsC,yBAAyB,GAAGA,CAAA,KAAM;EAC7C,OAAO,CACL;IACEzD,EAAE,EAAE,aAAa;IACjBG,IAAI,EAAE,SAAS;IACfF,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,qGAAqG;IAC9GwD,SAAS,EAAE,IAAInD,IAAI,CAAC,CAAC;IACrBoD,MAAM,EAAE,KAAK;IACbtD,IAAI,EAAE,KAAK;IACXD,QAAQ,EAAE,MAAM;IAChBQ,IAAI,EAAE;MACJgB,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,aAAa;QAAEC,OAAO,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAO,CAAC,EAC5D;QAAEF,KAAK,EAAE,cAAc;QAAEC,OAAO,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC;IAEpE;EACF,CAAC,CACF;AACH,CAAC;;AAED;AACA,MAAM4B,mBAAmB,GAAG;EAC1BjE,gBAAgB;EAChByB,sBAAsB;EACtBE,0BAA0B;EAC1BE,yBAAyB;EACzBS,kBAAkB;EAClBG,kBAAkB;EAClBC,0BAA0B;EAC1BW,6BAA6B;EAC7BE,kBAAkB;EAClBO;AACF,CAAC;AAED,eAAeG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}