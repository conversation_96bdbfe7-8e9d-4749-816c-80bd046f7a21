import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import { Checkbox } from '../../../components/ui/Checkbox';

const TasksTab = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSort, setSelectedSort] = useState('dueDate');
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Real tasks data - will be loaded from API
  const [tasks, setTasks] = useState([]);

  const filterOptions = [
    { value: 'all', label: 'All Tasks' },
    { value: 'todo', label: 'To Do' },
    { value: 'in-progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' }
  ];

  const sortOptions = [
    { value: 'dueDate', label: 'Due Date' },
    { value: 'priority', label: 'Priority' },
    { value: 'status', label: 'Status' },
    { value: 'assignee', label: 'Assignee' },
    { value: 'title', label: 'Title' }
  ];

  const priorityOptions = [
    { value: 'high', label: 'High Priority' },
    { value: 'medium', label: 'Medium Priority' },
    { value: 'low', label: 'Low Priority' }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-success bg-success/10';
      case 'in-progress':
        return 'text-warning bg-warning/10';
      case 'todo':
        return 'text-text-secondary bg-muted';
      default:
        return 'text-text-secondary bg-muted';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'text-error bg-error/10';
      case 'medium':
        return 'text-warning bg-warning/10';
      case 'low':
        return 'text-success bg-success/10';
      default:
        return 'text-text-secondary bg-muted';
    }
  };

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || task.status === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  const sortedTasks = [...filteredTasks].sort((a, b) => {
    switch (selectedSort) {
      case 'dueDate':
        return new Date(a.dueDate) - new Date(b.dueDate);
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      case 'status':
        return a.status.localeCompare(b.status);
      case 'assignee':
        return a.assignee.name.localeCompare(b.assignee.name);
      case 'title':
        return a.title.localeCompare(b.title);
      default:
        return 0;
    }
  });

  const handleTaskSelection = (taskId, checked) => {
    if (checked) {
      setSelectedTasks([...selectedTasks, taskId]);
    } else {
      setSelectedTasks(selectedTasks.filter(id => id !== taskId));
    }
    setShowBulkActions(selectedTasks.length > 0 || checked);
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedTasks(sortedTasks.map(task => task.id));
    } else {
      setSelectedTasks([]);
    }
    setShowBulkActions(checked);
  };

  const handleBulkAction = (action) => {
    console.log(`Performing ${action} on tasks:`, selectedTasks);
    setSelectedTasks([]);
    setShowBulkActions(false);
  };

  const isOverdue = (dueDate) => {
    return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString();
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <div className="flex-1 max-w-md">
            <Input
              type="search"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex gap-2">
            <Select
              options={filterOptions}
              value={selectedFilter}
              onChange={setSelectedFilter}
              placeholder="Filter by status"
              className="w-40"
            />
            <Select
              options={sortOptions}
              value={selectedSort}
              onChange={setSelectedSort}
              placeholder="Sort by"
              className="w-32"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" iconName="Filter" iconPosition="left">
            More Filters
          </Button>
          <Button variant="default" iconName="Plus" iconPosition="left">
            Add Task
          </Button>
        </div>
      </div>

      {/* Bulk Actions */}
      {showBulkActions && (
        <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-foreground">
              {selectedTasks.length} task{selectedTasks.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => handleBulkAction('assign')}>
                Assign
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleBulkAction('status')}>
                Change Status
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleBulkAction('priority')}>
                Set Priority
              </Button>
              <Button variant="destructive" size="sm" onClick={() => handleBulkAction('delete')}>
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Tasks Table */}
      <div className="bg-card rounded-lg border border-border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted/50 border-b border-border">
              <tr>
                <th className="text-left p-4 w-12">
                  <Checkbox
                    checked={selectedTasks.length === sortedTasks.length && sortedTasks.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    indeterminate={selectedTasks.length > 0 && selectedTasks.length < sortedTasks.length}
                  />
                </th>
                <th className="text-left p-4 font-medium text-foreground">Task</th>
                <th className="text-left p-4 font-medium text-foreground">Status</th>
                <th className="text-left p-4 font-medium text-foreground">Priority</th>
                <th className="text-left p-4 font-medium text-foreground">Assignee</th>
                <th className="text-left p-4 font-medium text-foreground">Due Date</th>
                <th className="text-left p-4 font-medium text-foreground">Progress</th>
                <th className="text-left p-4 font-medium text-foreground">Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedTasks.map((task) => (
                <tr key={task.id} className="border-b border-border hover:bg-muted/30 transition-colors">
                  <td className="p-4">
                    <Checkbox
                      checked={selectedTasks.includes(task.id)}
                      onChange={(e) => handleTaskSelection(task.id, e.target.checked)}
                    />
                  </td>
                  <td className="p-4">
                    <div className="space-y-1">
                      <h4 className="font-medium text-foreground hover:text-primary cursor-pointer">
                        {task.title}
                      </h4>
                      <p className="text-sm text-text-secondary line-clamp-2">
                        {task.description}
                      </p>
                      <div className="flex items-center gap-2">
                        {task.tags.slice(0, 2).map((tag) => (
                          <span key={tag} className="px-2 py-1 bg-muted rounded-full text-xs text-text-secondary">
                            {tag}
                          </span>
                        ))}
                        {task.tags.length > 2 && (
                          <span className="text-xs text-text-secondary">+{task.tags.length - 2}</span>
                        )}
                      </div>
                      <div className="flex items-center gap-3 text-xs text-text-secondary">
                        {task.comments > 0 && (
                          <div className="flex items-center gap-1">
                            <Icon name="MessageCircle" size={12} />
                            <span>{task.comments}</span>
                          </div>
                        )}
                        {task.attachments > 0 && (
                          <div className="flex items-center gap-1">
                            <Icon name="Paperclip" size={12} />
                            <span>{task.attachments}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {task.status.replace('-', ' ')}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </span>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      <img
                        src={task.assignee.avatar}
                        alt={task.assignee.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <span className="text-sm text-foreground">{task.assignee.name}</span>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      <span className={`text-sm ${isOverdue(task.dueDate) ? 'text-error font-medium' : 'text-foreground'}`}>
                        {new Date(task.dueDate).toLocaleDateString()}
                      </span>
                      {isOverdue(task.dueDate) && (
                        <Icon name="AlertTriangle" size={14} className="text-error" />
                      )}
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${task.progress}%` }}
                        />
                      </div>
                      <span className="text-sm text-text-secondary">{task.progress}%</span>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-1">
                      <Button variant="ghost" size="sm" iconName="Eye" />
                      <Button variant="ghost" size="sm" iconName="Edit" />
                      <Button variant="ghost" size="sm" iconName="MoreHorizontal" />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Task Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Total Tasks</p>
              <p className="text-2xl font-bold text-foreground">{tasks.length}</p>
            </div>
            <Icon name="CheckSquare" size={24} className="text-primary" />
          </div>
        </div>
        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Completed</p>
              <p className="text-2xl font-bold text-success">
                {tasks.filter(task => task.status === 'completed').length}
              </p>
            </div>
            <Icon name="CheckCircle" size={24} className="text-success" />
          </div>
        </div>
        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">In Progress</p>
              <p className="text-2xl font-bold text-warning">
                {tasks.filter(task => task.status === 'in-progress').length}
              </p>
            </div>
            <Icon name="Clock" size={24} className="text-warning" />
          </div>
        </div>
        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Overdue</p>
              <p className="text-2xl font-bold text-error">
                {tasks.filter(task => isOverdue(task.dueDate) && task.status !== 'completed').length}
              </p>
            </div>
            <Icon name="AlertTriangle" size={24} className="text-error" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TasksTab;