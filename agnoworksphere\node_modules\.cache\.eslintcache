[{"C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx": "1", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx": "2", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx": "3", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx": "4", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx": "5", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx": "6", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx": "7", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx": "8", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx": "9", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx": "10", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx": "11", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx": "12", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx": "13", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx": "14", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx": "15", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx": "16", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx": "17", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx": "18", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx": "19", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx": "20", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx": "21", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx": "22", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx": "23", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx": "24", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx": "25", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx": "26", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx": "27", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js": "28", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx": "29", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx": "30", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx": "31", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx": "32", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx": "33", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx": "34", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx": "35", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx": "36", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx": "37", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx": "38", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx": "39", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx": "40", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx": "41", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx": "42", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx": "43", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx": "44", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx": "45", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx": "46", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx": "47", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx": "48", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx": "49", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx": "50", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx": "51", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx": "52", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx": "53", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx": "54", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx": "55", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx": "56", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx": "57", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx": "58", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx": "59", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx": "60", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx": "61", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx": "62", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx": "63", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx": "64", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx": "65", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx": "66", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx": "67", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx": "68", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx": "69", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx": "70", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx": "71", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx": "72", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js": "73", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx": "74", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx": "75", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx": "76", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx": "77", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx": "78", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js": "79", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js": "80", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx": "81", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx": "82", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx": "83", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx": "84", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx": "85", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx": "86", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx": "87", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js": "88", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js": "89", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js": "90", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateOrganizationModal.jsx": "91", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realApiService.js": "92", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\teamService.js": "93", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\notificationService.js": "94", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\analytics\\index.jsx": "95", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\billing\\index.jsx": "96"}, {"size": 1396, "mtime": 1754034229833, "results": "97", "hashOfConfig": "98"}, {"size": 977, "mtime": 1754033081943, "results": "99", "hashOfConfig": "98"}, {"size": 2200, "mtime": 1754195201436, "results": "100", "hashOfConfig": "98"}, {"size": 2279, "mtime": 1754126212460, "results": "101", "hashOfConfig": "98"}, {"size": 263, "mtime": 1753662156000, "results": "102", "hashOfConfig": "98"}, {"size": 2597, "mtime": 1753662156000, "results": "103", "hashOfConfig": "98"}, {"size": 1443, "mtime": 1753896368000, "results": "104", "hashOfConfig": "98"}, {"size": 1851, "mtime": 1753662960000, "results": "105", "hashOfConfig": "98"}, {"size": 2221, "mtime": 1754033188241, "results": "106", "hashOfConfig": "98"}, {"size": 20875, "mtime": 1754199026375, "results": "107", "hashOfConfig": "98"}, {"size": 13065, "mtime": 1754199140547, "results": "108", "hashOfConfig": "98"}, {"size": 19565, "mtime": 1754126131895, "results": "109", "hashOfConfig": "98"}, {"size": 8178, "mtime": 1754043014618, "results": "110", "hashOfConfig": "98"}, {"size": 12073, "mtime": 1754036144487, "results": "111", "hashOfConfig": "98"}, {"size": 8306, "mtime": 1754042999489, "results": "112", "hashOfConfig": "98"}, {"size": 10580, "mtime": 1754120811344, "results": "113", "hashOfConfig": "98"}, {"size": 27637, "mtime": 1754198962398, "results": "114", "hashOfConfig": "98"}, {"size": 619, "mtime": 1753662156000, "results": "115", "hashOfConfig": "98"}, {"size": 3229, "mtime": 1753931434000, "results": "116", "hashOfConfig": "98"}, {"size": 15516, "mtime": 1754037223798, "results": "117", "hashOfConfig": "98"}, {"size": 3119, "mtime": 1753662156000, "results": "118", "hashOfConfig": "98"}, {"size": 9775, "mtime": 1753662156000, "results": "119", "hashOfConfig": "98"}, {"size": 1661, "mtime": 1753662960000, "results": "120", "hashOfConfig": "98"}, {"size": 8345, "mtime": 1754040316710, "results": "121", "hashOfConfig": "98"}, {"size": 2069, "mtime": 1753662960000, "results": "122", "hashOfConfig": "98"}, {"size": 4355, "mtime": 1753662960000, "results": "123", "hashOfConfig": "98"}, {"size": 1270, "mtime": 1753662960000, "results": "124", "hashOfConfig": "98"}, {"size": 12175, "mtime": 1754056135125, "results": "125", "hashOfConfig": "98"}, {"size": 14625, "mtime": 1754121145710, "results": "126", "hashOfConfig": "98"}, {"size": 3061, "mtime": 1753662960000, "results": "127", "hashOfConfig": "98"}, {"size": 13946, "mtime": 1754050960159, "results": "128", "hashOfConfig": "98"}, {"size": 8743, "mtime": 1754041573665, "results": "129", "hashOfConfig": "98"}, {"size": 7293, "mtime": 1754121209630, "results": "130", "hashOfConfig": "98"}, {"size": 4639, "mtime": 1754041421031, "results": "131", "hashOfConfig": "98"}, {"size": 4319, "mtime": 1753662960000, "results": "132", "hashOfConfig": "98"}, {"size": 3136, "mtime": 1754037270759, "results": "133", "hashOfConfig": "98"}, {"size": 11197, "mtime": 1754052040562, "results": "134", "hashOfConfig": "98"}, {"size": 8439, "mtime": 1754123709182, "results": "135", "hashOfConfig": "98"}, {"size": 2962, "mtime": 1753662960000, "results": "136", "hashOfConfig": "98"}, {"size": 4192, "mtime": 1753662960000, "results": "137", "hashOfConfig": "98"}, {"size": 2269, "mtime": 1753662960000, "results": "138", "hashOfConfig": "98"}, {"size": 6524, "mtime": 1754123731298, "results": "139", "hashOfConfig": "98"}, {"size": 5011, "mtime": 1753662960000, "results": "140", "hashOfConfig": "98"}, {"size": 5667, "mtime": 1753662960000, "results": "141", "hashOfConfig": "98"}, {"size": 6178, "mtime": 1753662960000, "results": "142", "hashOfConfig": "98"}, {"size": 3646, "mtime": 1754043560517, "results": "143", "hashOfConfig": "98"}, {"size": 8113, "mtime": 1754043500588, "results": "144", "hashOfConfig": "98"}, {"size": 6935, "mtime": 1753662960000, "results": "145", "hashOfConfig": "98"}, {"size": 5068, "mtime": 1753662960000, "results": "146", "hashOfConfig": "98"}, {"size": 3503, "mtime": 1754043686958, "results": "147", "hashOfConfig": "98"}, {"size": 11618, "mtime": 1754120839603, "results": "148", "hashOfConfig": "98"}, {"size": 12779, "mtime": 1753660406000, "results": "149", "hashOfConfig": "98"}, {"size": 18772, "mtime": 1753660406000, "results": "150", "hashOfConfig": "98"}, {"size": 16373, "mtime": 1753660406000, "results": "151", "hashOfConfig": "98"}, {"size": 20926, "mtime": 1754121648073, "results": "152", "hashOfConfig": "98"}, {"size": 55166, "mtime": 1754198926993, "results": "153", "hashOfConfig": "98"}, {"size": 5370, "mtime": 1753663554000, "results": "154", "hashOfConfig": "98"}, {"size": 5637, "mtime": 1753663554000, "results": "155", "hashOfConfig": "98"}, {"size": 1689, "mtime": 1753663554000, "results": "156", "hashOfConfig": "98"}, {"size": 5556, "mtime": 1753663554000, "results": "157", "hashOfConfig": "98"}, {"size": 14343, "mtime": 1754127034099, "results": "158", "hashOfConfig": "98"}, {"size": 13209, "mtime": 1753667086000, "results": "159", "hashOfConfig": "98"}, {"size": 19464, "mtime": 1753667086000, "results": "160", "hashOfConfig": "98"}, {"size": 9333, "mtime": 1754120749717, "results": "161", "hashOfConfig": "98"}, {"size": 13135, "mtime": 1754121313818, "results": "162", "hashOfConfig": "98"}, {"size": 13718, "mtime": 1753660406000, "results": "163", "hashOfConfig": "98"}, {"size": 2946, "mtime": 1753916458000, "results": "164", "hashOfConfig": "98"}, {"size": 5117, "mtime": 1753916458000, "results": "165", "hashOfConfig": "98"}, {"size": 3744, "mtime": 1753916090000, "results": "166", "hashOfConfig": "98"}, {"size": 6686, "mtime": 1754132191107, "results": "167", "hashOfConfig": "98"}, {"size": 6636, "mtime": 1754056277854, "results": "168", "hashOfConfig": "98"}, {"size": 5794, "mtime": 1753916090000, "results": "169", "hashOfConfig": "98"}, {"size": 139, "mtime": 1753662156000, "results": "170", "hashOfConfig": "98"}, {"size": 8156, "mtime": 1754123202462, "results": "171", "hashOfConfig": "98"}, {"size": 5802, "mtime": 1753916090000, "results": "172", "hashOfConfig": "98"}, {"size": 4753, "mtime": 1753662156000, "results": "173", "hashOfConfig": "98"}, {"size": 329, "mtime": 1753662156000, "results": "174", "hashOfConfig": "98"}, {"size": 6210, "mtime": 1754041438145, "results": "175", "hashOfConfig": "98"}, {"size": 9965, "mtime": 1754198415129, "results": "176", "hashOfConfig": "98"}, {"size": 13056, "mtime": 1754196793734, "results": "177", "hashOfConfig": "98"}, {"size": 4743, "mtime": 1753979063817, "results": "178", "hashOfConfig": "98"}, {"size": 623, "mtime": 1753979082425, "results": "179", "hashOfConfig": "98"}, {"size": 5337, "mtime": 1753979469770, "results": "180", "hashOfConfig": "98"}, {"size": 8078, "mtime": 1754026444720, "results": "181", "hashOfConfig": "98"}, {"size": 2645, "mtime": 1754026405389, "results": "182", "hashOfConfig": "98"}, {"size": 8796, "mtime": 1754030789819, "results": "183", "hashOfConfig": "98"}, {"size": 23509, "mtime": 1754199446402, "results": "184", "hashOfConfig": "98"}, {"size": 5668, "mtime": 1754050276943, "results": "185", "hashOfConfig": "98"}, {"size": 9462, "mtime": 1754050330607, "results": "186", "hashOfConfig": "98"}, {"size": 8055, "mtime": 1754050911094, "results": "187", "hashOfConfig": "98"}, {"size": 24107, "mtime": 1754055910235, "results": "188", "hashOfConfig": "98"}, {"size": 10317, "mtime": 1754195468362, "results": "189", "hashOfConfig": "98"}, {"size": 6789, "mtime": 1754127109680, "results": "190", "hashOfConfig": "98"}, {"size": 7536, "mtime": 1754196733252, "results": "191", "hashOfConfig": "98"}, {"size": 15845, "mtime": 1754195084404, "results": "192", "hashOfConfig": "98"}, {"size": 15642, "mtime": 1754195149865, "results": "193", "hashOfConfig": "98"}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7s4ywu", {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx", ["482", "483", "484", "485"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx", ["486", "487"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx", ["488"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx", ["489"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx", ["490", "491", "492"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx", ["493"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx", ["494", "495", "496"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx", ["497", "498", "499", "500"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx", ["501", "502", "503"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx", ["504", "505"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx", ["506"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx", ["507", "508"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js", ["509", "510"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js", ["511"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js", ["512"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateOrganizationModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realApiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\teamService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\notificationService.js", ["513"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\analytics\\index.jsx", ["514"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\billing\\index.jsx", ["515"], [], {"ruleId": "516", "severity": 1, "message": "517", "line": 20, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 20, "endColumn": 15}, {"ruleId": "516", "severity": 1, "message": "520", "line": 61, "column": 19, "nodeType": "518", "messageId": "519", "endLine": 61, "endColumn": 29}, {"ruleId": "516", "severity": 1, "message": "521", "line": 264, "column": 9, "nodeType": "518", "messageId": "519", "endLine": 264, "endColumn": 21}, {"ruleId": "516", "severity": 1, "message": "522", "line": 269, "column": 9, "nodeType": "518", "messageId": "519", "endLine": 269, "endColumn": 23}, {"ruleId": "516", "severity": 1, "message": "523", "line": 69, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 69, "endColumn": 17}, {"ruleId": "524", "severity": 1, "message": "525", "line": 191, "column": 6, "nodeType": "526", "endLine": 191, "endColumn": 52, "suggestions": "527"}, {"ruleId": "516", "severity": 1, "message": "528", "line": 14, "column": 27, "nodeType": "518", "messageId": "519", "endLine": 14, "endColumn": 45}, {"ruleId": "516", "severity": 1, "message": "523", "line": 17, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 17, "endColumn": 17}, {"ruleId": "516", "severity": 1, "message": "529", "line": 22, "column": 23, "nodeType": "518", "messageId": "519", "endLine": 22, "endColumn": 37}, {"ruleId": "516", "severity": 1, "message": "530", "line": 23, "column": 23, "nodeType": "518", "messageId": "519", "endLine": 23, "endColumn": 37}, {"ruleId": "524", "severity": 1, "message": "531", "line": 178, "column": 6, "nodeType": "526", "endLine": 178, "endColumn": 22, "suggestions": "532"}, {"ruleId": "516", "severity": 1, "message": "533", "line": 8, "column": 49, "nodeType": "518", "messageId": "519", "endLine": 8, "endColumn": 60}, {"ruleId": "516", "severity": 1, "message": "534", "line": 4, "column": 31, "nodeType": "518", "messageId": "519", "endLine": 4, "endColumn": 48}, {"ruleId": "516", "severity": 1, "message": "535", "line": 12, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 12, "endColumn": 25}, {"ruleId": "516", "severity": 1, "message": "536", "line": 65, "column": 9, "nodeType": "518", "messageId": "519", "endLine": 65, "endColumn": 31}, {"ruleId": "516", "severity": 1, "message": "523", "line": 11, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 11, "endColumn": 17}, {"ruleId": "516", "severity": 1, "message": "537", "line": 12, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 12, "endColumn": 16}, {"ruleId": "516", "severity": 1, "message": "538", "line": 12, "column": 18, "nodeType": "518", "messageId": "519", "endLine": 12, "endColumn": 27}, {"ruleId": "516", "severity": 1, "message": "539", "line": 57, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 57, "endColumn": 18}, {"ruleId": "516", "severity": 1, "message": "540", "line": 9, "column": 8, "nodeType": "518", "messageId": "519", "endLine": 9, "endColumn": 22}, {"ruleId": "541", "severity": 1, "message": "542", "line": 151, "column": 5, "nodeType": "543", "messageId": "544", "endLine": 169, "endColumn": 6}, {"ruleId": "516", "severity": 1, "message": "545", "line": 181, "column": 9, "nodeType": "518", "messageId": "519", "endLine": 181, "endColumn": 21}, {"ruleId": "516", "severity": 1, "message": "546", "line": 16, "column": 17, "nodeType": "518", "messageId": "519", "endLine": 16, "endColumn": 25}, {"ruleId": "516", "severity": 1, "message": "547", "line": 33, "column": 9, "nodeType": "518", "messageId": "519", "endLine": 33, "endColumn": 24}, {"ruleId": "516", "severity": 1, "message": "540", "line": 6, "column": 8, "nodeType": "518", "messageId": "519", "endLine": 6, "endColumn": 22}, {"ruleId": "524", "severity": 1, "message": "548", "line": 135, "column": 6, "nodeType": "526", "endLine": 135, "endColumn": 62, "suggestions": "549"}, {"ruleId": "524", "severity": 1, "message": "550", "line": 151, "column": 6, "nodeType": "526", "endLine": 151, "endColumn": 8, "suggestions": "551"}, {"ruleId": "516", "severity": 1, "message": "552", "line": 124, "column": 9, "nodeType": "518", "messageId": "519", "endLine": 124, "endColumn": 20}, {"ruleId": "553", "severity": 1, "message": "554", "line": 185, "column": 1, "nodeType": "555", "endLine": 193, "endColumn": 3}, {"ruleId": "553", "severity": 1, "message": "554", "line": 315, "column": 1, "nodeType": "555", "endLine": 319, "endColumn": 3}, {"ruleId": "553", "severity": 1, "message": "554", "line": 250, "column": 1, "nodeType": "555", "endLine": 260, "endColumn": 3}, {"ruleId": "516", "severity": 1, "message": "540", "line": 1, "column": 8, "nodeType": "518", "messageId": "519", "endLine": 1, "endColumn": 22}, {"ruleId": "524", "severity": 1, "message": "556", "line": 34, "column": 6, "nodeType": "526", "endLine": 34, "endColumn": 18, "suggestions": "557"}, {"ruleId": "524", "severity": 1, "message": "558", "line": 36, "column": 6, "nodeType": "526", "endLine": 36, "endColumn": 8, "suggestions": "559"}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'setMembers' is assigned a value but never used.", "'canEditCards' is assigned a value but never used.", "'canDeleteCards' is assigned a value but never used.", "'loading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'currentUser?.email', 'currentUser?.firstName', 'currentUser?.lastName', and 'userRole'. Either include them or remove the dependency array.", "ArrayExpression", ["560"], "'setSidebarExpanded' is assigned a value but never used.", "'setSearchValue' is assigned a value but never used.", "'setFilterValue' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentUser' and 'organizations.length'. Either include them or remove the dependency array.", ["561"], "'handleError' is defined but never used.", "'getSuggestedItems' is defined but never used.", "'showSuggestions' is assigned a value but never used.", "'handleAddSuggestedItem' is assigned a value but never used.", "'saving' is assigned a value but never used.", "'setSaving' is assigned a value but never used.", "'logoFile' is assigned a value but never used.", "'realApiService' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'getRoleColor' is assigned a value but never used.", "'setTasks' is assigned a value but never used.", "'priorityOptions' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["562"], "React Hook useEffect has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["563"], "'permissions' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'loadAnalyticsData'. Either include it or remove the dependency array.", ["564"], "React Hook useEffect has a missing dependency: 'loadBillingData'. Either include it or remove the dependency array.", ["565"], {"desc": "566", "fix": "567"}, {"desc": "568", "fix": "569"}, {"desc": "570", "fix": "571"}, {"desc": "572", "fix": "573"}, {"desc": "574", "fix": "575"}, {"desc": "576", "fix": "577"}, "Update the dependencies array to be: [currentOrganization, searchQuery, roleFilter, currentUser?.firstName, currentUser?.lastName, currentUser?.email, userRole]", {"range": "578", "text": "579"}, "Update the dependencies array to be: [currentUser, location.state, organizations.length]", {"range": "580", "text": "581"}, "Update the dependencies array to be: [defaultShortcuts, sequenceTimeout, keySequence]", {"range": "582", "text": "583"}, "Update the dependencies array to be: [defaultShortcuts]", {"range": "584", "text": "585"}, "Update the dependencies array to be: [loadAnalyticsData, timePeriod]", {"range": "586", "text": "587"}, "Update the dependencies array to be: [loadBillingData]", {"range": "588", "text": "589"}, [6367, 6413], "[currentOrganization, searchQuery, roleFilter, currentUser?.firstName, currentUser?.lastName, currentUser?.email, userRole]", [7162, 7178], "[currentUser, location.state, organizations.length]", [4586, 4642], "[defaultShortcuts, sequenceTimeout, keySequence]", [5058, 5060], "[defaultShortcuts]", [1168, 1180], "[loadAnalyticsData, timePeriod]", [1271, 1273], "[loadBillingData]"]