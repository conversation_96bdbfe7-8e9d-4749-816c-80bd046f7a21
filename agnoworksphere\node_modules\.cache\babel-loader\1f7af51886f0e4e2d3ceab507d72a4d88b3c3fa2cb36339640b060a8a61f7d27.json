{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\billing\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Billing = () => {\n  _s();\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [billingData, setBillingData] = useState({\n    subscription: {},\n    paymentHistory: [],\n    usage: {},\n    plans: []\n  });\n\n  // Active tab state\n  const [activeTab, setActiveTab] = useState('overview');\n  const tabs = [{\n    id: 'overview',\n    label: 'Overview',\n    icon: 'CreditCard'\n  }, {\n    id: 'subscription',\n    label: 'Subscription',\n    icon: 'Package'\n  }, {\n    id: 'usage',\n    label: 'Usage',\n    icon: 'BarChart3'\n  }, {\n    id: 'history',\n    label: 'Payment History',\n    icon: 'Receipt'\n  }, {\n    id: 'plans',\n    label: 'Plans & Pricing',\n    icon: 'Layers'\n  }];\n  useEffect(() => {\n    loadUserData();\n    loadBillingData();\n  }, []);\n  const loadUserData = async () => {\n    try {\n      const user = await authService.getCurrentUser();\n      const role = await authService.getUserRole();\n      const organization = await authService.getCurrentOrganization();\n      setCurrentUser(user);\n      setUserRole(role);\n      setCurrentOrganization(organization);\n    } catch (error) {\n      console.error('Failed to load user data:', error);\n    }\n  };\n  const loadBillingData = async () => {\n    try {\n      setLoading(true);\n\n      // Load billing data from API\n      const [subscription, paymentHistory, usage, plans] = await Promise.all([apiService.getSubscriptionDetails(), apiService.getPaymentHistory(), apiService.getUsageBilling(), apiService.getAvailablePlans()]);\n      setBillingData({\n        subscription: subscription || generateMockSubscription(),\n        paymentHistory: paymentHistory || generateMockPaymentHistory(),\n        usage: usage || generateMockUsage(),\n        plans: plans || generateMockPlans()\n      });\n    } catch (error) {\n      console.error('Failed to load billing data:', error);\n      // Use mock data as fallback\n      setBillingData({\n        subscription: generateMockSubscription(),\n        paymentHistory: generateMockPaymentHistory(),\n        usage: generateMockUsage(),\n        plans: generateMockPlans()\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mock data generators\n  const generateMockSubscription = () => ({\n    plan: 'Professional',\n    status: 'active',\n    price: 29.99,\n    currency: 'USD',\n    billingCycle: 'monthly',\n    nextBillingDate: '2024-02-15',\n    seats: 25,\n    usedSeats: 18,\n    features: ['Unlimited Projects', 'Advanced Analytics', 'Priority Support', 'Custom Integrations', 'Advanced Security']\n  });\n  const generateMockPaymentHistory = () => [{\n    id: 'inv_001',\n    date: '2024-01-15',\n    amount: 29.99,\n    status: 'paid',\n    description: 'Professional Plan - Monthly',\n    downloadUrl: '#'\n  }, {\n    id: 'inv_002',\n    date: '2023-12-15',\n    amount: 29.99,\n    status: 'paid',\n    description: 'Professional Plan - Monthly',\n    downloadUrl: '#'\n  }, {\n    id: 'inv_003',\n    date: '2023-11-15',\n    amount: 29.99,\n    status: 'paid',\n    description: 'Professional Plan - Monthly',\n    downloadUrl: '#'\n  }];\n  const generateMockUsage = () => ({\n    currentPeriod: {\n      start: '2024-01-15',\n      end: '2024-02-15'\n    },\n    metrics: [{\n      name: 'Active Users',\n      current: 18,\n      limit: 25,\n      unit: 'users'\n    }, {\n      name: 'Projects',\n      current: 12,\n      limit: 'unlimited',\n      unit: 'projects'\n    }, {\n      name: 'Storage',\n      current: 2.4,\n      limit: 100,\n      unit: 'GB'\n    }, {\n      name: 'API Calls',\n      current: 1250,\n      limit: 10000,\n      unit: 'calls'\n    }],\n    overageCharges: 0\n  });\n  const generateMockPlans = () => [{\n    id: 'starter',\n    name: 'Starter',\n    price: 9.99,\n    currency: 'USD',\n    billingCycle: 'monthly',\n    features: ['Up to 5 users', '10 projects', '5GB storage', 'Basic support', 'Standard integrations'],\n    current: false\n  }, {\n    id: 'professional',\n    name: 'Professional',\n    price: 29.99,\n    currency: 'USD',\n    billingCycle: 'monthly',\n    features: ['Up to 25 users', 'Unlimited projects', '100GB storage', 'Priority support', 'Advanced integrations', 'Analytics dashboard'],\n    current: true,\n    popular: true\n  }, {\n    id: 'enterprise',\n    name: 'Enterprise',\n    price: 99.99,\n    currency: 'USD',\n    billingCycle: 'monthly',\n    features: ['Unlimited users', 'Unlimited projects', '1TB storage', '24/7 dedicated support', 'Custom integrations', 'Advanced analytics', 'SSO & SAML', 'Custom branding'],\n    current: false\n  }];\n\n  // Check if user has access to billing\n  if (userRole !== 'owner') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser,\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"pt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Lock\",\n                size: 32,\n                className: \"mx-auto mb-4 text-text-secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-text-primary mb-2\",\n                children: \"Access Restricted\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary mb-4\",\n                children: \"Billing management is only available to Organization Owners.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => window.history.back(),\n                children: \"Go Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  }\n  const renderOverviewTab = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg border border-border p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-text-primary\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-3 py-1 rounded-full text-xs font-medium ${billingData.subscription.status === 'active' ? 'bg-success/10 text-success' : 'bg-destructive/10 text-destructive'}`,\n          children: billingData.subscription.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-secondary\",\n            children: \"Plan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl font-bold text-text-primary\",\n            children: billingData.subscription.plan\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-secondary\",\n            children: \"Price\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl font-bold text-text-primary\",\n            children: [\"$\", billingData.subscription.price, \"/\", billingData.subscription.billingCycle]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-secondary\",\n            children: \"Next Billing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl font-bold text-text-primary\",\n            children: new Date(billingData.subscription.nextBillingDate).toLocaleDateString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => setActiveTab('plans'),\n          children: \"Change Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          children: \"Update Payment Method\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg border border-border p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-text-primary mb-4\",\n        children: \"Usage Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n        children: billingData.usage.metrics.map((metric, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 bg-muted rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-secondary\",\n            children: metric.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-bold text-text-primary\",\n                children: [metric.current, \" \", metric.unit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-text-secondary\",\n                children: [\"/ \", metric.limit === 'unlimited' ? '∞' : `${metric.limit} ${metric.unit}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), metric.limit !== 'unlimited' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 w-full bg-background rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-primary h-2 rounded-full\",\n                style: {\n                  width: `${metric.current / metric.limit * 100}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg border border-border p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-text-primary\",\n          children: \"Recent Payments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => setActiveTab('history'),\n          children: \"View All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: billingData.paymentHistory.slice(0, 3).map(payment => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-3 bg-muted rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-text-primary\",\n              children: payment.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-text-secondary\",\n              children: new Date(payment.date).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-bold text-text-primary\",\n              children: [\"$\", payment.amount]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-xs px-2 py-1 rounded-full ${payment.status === 'paid' ? 'bg-success/10 text-success' : 'bg-warning/10 text-warning'}`,\n              children: payment.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)]\n        }, payment.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n  const renderPlansTab = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-2xl font-bold text-text-primary mb-2\",\n        children: \"Choose Your Plan\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-secondary\",\n        children: \"Select the plan that best fits your organization's needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n      children: billingData.plans.map(plan => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `relative bg-card rounded-lg border p-6 ${plan.current ? 'border-primary ring-2 ring-primary/20' : 'border-border'} ${plan.popular ? 'ring-2 ring-accent/20' : ''}`,\n        children: [plan.popular && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-accent text-accent-foreground px-3 py-1 rounded-full text-xs font-medium\",\n            children: \"Most Popular\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-xl font-bold text-text-primary\",\n            children: plan.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-3xl font-bold text-text-primary\",\n              children: [\"$\", plan.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-text-secondary\",\n              children: [\"/\", plan.billingCycle]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"space-y-3 mb-6\",\n          children: plan.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Check\",\n              size: 16,\n              className: \"text-success mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-text-secondary\",\n              children: feature\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: plan.current ? \"outline\" : \"default\",\n          className: \"w-full\",\n          disabled: plan.current,\n          children: plan.current ? 'Current Plan' : 'Upgrade to ' + plan.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this)]\n      }, plan.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 333,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName} ${currentUser.lastName}`,\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"pt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-text-primary\",\n              children: \"Billing & Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary mt-2\",\n              children: \"Manage your subscription, view usage, and payment history\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            iconName: \"Download\",\n            children: \"Download Invoice\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-b border-border mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"flex space-x-8\",\n            children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab.id),\n              className: `flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${activeTab === tab.id ? 'border-primary text-primary' : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border'}`,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: tab.icon,\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: tab.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this)]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary\",\n              children: \"Loading billing data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [activeTab === 'overview' && renderOverviewTab(), activeTab === 'plans' && renderPlansTab()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 383,\n    columnNumber: 5\n  }, this);\n};\n_s(Billing, \"cCAlnn7kFXRkRd3UGHJVSXH/RFw=\");\n_c = Billing;\nexport default Billing;\nvar _c;\n$RefreshReg$(_c, \"Billing\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "RoleBasedHeader", "Breadcrumb", "Icon", "<PERSON><PERSON>", "authService", "apiService", "jsxDEV", "_jsxDEV", "Billing", "_s", "currentUser", "setCurrentUser", "userRole", "setUserRole", "currentOrganization", "setCurrentOrganization", "loading", "setLoading", "billingData", "setBillingData", "subscription", "paymentHistory", "usage", "plans", "activeTab", "setActiveTab", "tabs", "id", "label", "icon", "loadUserData", "loadBillingData", "user", "getCurrentUser", "role", "getUserRole", "organization", "getCurrentOrganization", "error", "console", "Promise", "all", "getSubscriptionDetails", "getPaymentHistory", "getUsageBilling", "getAvailablePlans", "generateMockSubscription", "generateMockPaymentHistory", "generateMockUsage", "generateMockPlans", "plan", "status", "price", "currency", "billingCycle", "nextBillingDate", "seats", "usedSeats", "features", "date", "amount", "description", "downloadUrl", "currentPeriod", "start", "end", "metrics", "name", "current", "limit", "unit", "overageCharges", "popular", "className", "children", "toLowerCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "window", "history", "back", "renderOverviewTab", "Date", "toLocaleDateString", "variant", "map", "metric", "index", "style", "width", "slice", "payment", "renderPlansTab", "feature", "disabled", "firstName", "lastName", "email", "avatar", "iconName", "tab", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/billing/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\n\nconst Billing = () => {\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [billingData, setBillingData] = useState({\n    subscription: {},\n    paymentHistory: [],\n    usage: {},\n    plans: []\n  });\n\n  // Active tab state\n  const [activeTab, setActiveTab] = useState('overview');\n\n  const tabs = [\n    { id: 'overview', label: 'Overview', icon: 'CreditCard' },\n    { id: 'subscription', label: 'Subscription', icon: 'Package' },\n    { id: 'usage', label: 'Usage', icon: 'BarChart3' },\n    { id: 'history', label: 'Payment History', icon: 'Receipt' },\n    { id: 'plans', label: 'Plans & Pricing', icon: 'Layers' }\n  ];\n\n  useEffect(() => {\n    loadUserData();\n    loadBillingData();\n  }, []);\n\n  const loadUserData = async () => {\n    try {\n      const user = await authService.getCurrentUser();\n      const role = await authService.getUserRole();\n      const organization = await authService.getCurrentOrganization();\n      \n      setCurrentUser(user);\n      setUserRole(role);\n      setCurrentOrganization(organization);\n    } catch (error) {\n      console.error('Failed to load user data:', error);\n    }\n  };\n\n  const loadBillingData = async () => {\n    try {\n      setLoading(true);\n      \n      // Load billing data from API\n      const [subscription, paymentHistory, usage, plans] = await Promise.all([\n        apiService.getSubscriptionDetails(),\n        apiService.getPaymentHistory(),\n        apiService.getUsageBilling(),\n        apiService.getAvailablePlans()\n      ]);\n\n      setBillingData({\n        subscription: subscription || generateMockSubscription(),\n        paymentHistory: paymentHistory || generateMockPaymentHistory(),\n        usage: usage || generateMockUsage(),\n        plans: plans || generateMockPlans()\n      });\n    } catch (error) {\n      console.error('Failed to load billing data:', error);\n      // Use mock data as fallback\n      setBillingData({\n        subscription: generateMockSubscription(),\n        paymentHistory: generateMockPaymentHistory(),\n        usage: generateMockUsage(),\n        plans: generateMockPlans()\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mock data generators\n  const generateMockSubscription = () => ({\n    plan: 'Professional',\n    status: 'active',\n    price: 29.99,\n    currency: 'USD',\n    billingCycle: 'monthly',\n    nextBillingDate: '2024-02-15',\n    seats: 25,\n    usedSeats: 18,\n    features: [\n      'Unlimited Projects',\n      'Advanced Analytics',\n      'Priority Support',\n      'Custom Integrations',\n      'Advanced Security'\n    ]\n  });\n\n  const generateMockPaymentHistory = () => [\n    {\n      id: 'inv_001',\n      date: '2024-01-15',\n      amount: 29.99,\n      status: 'paid',\n      description: 'Professional Plan - Monthly',\n      downloadUrl: '#'\n    },\n    {\n      id: 'inv_002',\n      date: '2023-12-15',\n      amount: 29.99,\n      status: 'paid',\n      description: 'Professional Plan - Monthly',\n      downloadUrl: '#'\n    },\n    {\n      id: 'inv_003',\n      date: '2023-11-15',\n      amount: 29.99,\n      status: 'paid',\n      description: 'Professional Plan - Monthly',\n      downloadUrl: '#'\n    }\n  ];\n\n  const generateMockUsage = () => ({\n    currentPeriod: {\n      start: '2024-01-15',\n      end: '2024-02-15'\n    },\n    metrics: [\n      { name: 'Active Users', current: 18, limit: 25, unit: 'users' },\n      { name: 'Projects', current: 12, limit: 'unlimited', unit: 'projects' },\n      { name: 'Storage', current: 2.4, limit: 100, unit: 'GB' },\n      { name: 'API Calls', current: 1250, limit: 10000, unit: 'calls' }\n    ],\n    overageCharges: 0\n  });\n\n  const generateMockPlans = () => [\n    {\n      id: 'starter',\n      name: 'Starter',\n      price: 9.99,\n      currency: 'USD',\n      billingCycle: 'monthly',\n      features: [\n        'Up to 5 users',\n        '10 projects',\n        '5GB storage',\n        'Basic support',\n        'Standard integrations'\n      ],\n      current: false\n    },\n    {\n      id: 'professional',\n      name: 'Professional',\n      price: 29.99,\n      currency: 'USD',\n      billingCycle: 'monthly',\n      features: [\n        'Up to 25 users',\n        'Unlimited projects',\n        '100GB storage',\n        'Priority support',\n        'Advanced integrations',\n        'Analytics dashboard'\n      ],\n      current: true,\n      popular: true\n    },\n    {\n      id: 'enterprise',\n      name: 'Enterprise',\n      price: 99.99,\n      currency: 'USD',\n      billingCycle: 'monthly',\n      features: [\n        'Unlimited users',\n        'Unlimited projects',\n        '1TB storage',\n        '24/7 dedicated support',\n        'Custom integrations',\n        'Advanced analytics',\n        'SSO & SAML',\n        'Custom branding'\n      ],\n      current: false\n    }\n  ];\n\n  // Check if user has access to billing\n  if (userRole !== 'owner') {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser}\n          currentOrganization={currentOrganization}\n        />\n        <main className=\"pt-16\">\n          <div className=\"max-w-7xl mx-auto p-6\">\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <Icon name=\"Lock\" size={32} className=\"mx-auto mb-4 text-text-secondary\" />\n                <h3 className=\"text-lg font-medium text-text-primary mb-2\">Access Restricted</h3>\n                <p className=\"text-text-secondary mb-4\">Billing management is only available to Organization Owners.</p>\n                <Button onClick={() => window.history.back()}>\n                  Go Back\n                </Button>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    );\n  }\n\n  const renderOverviewTab = () => (\n    <div className=\"space-y-6\">\n      {/* Current Subscription */}\n      <div className=\"bg-card rounded-lg border border-border p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-text-primary\">Current Subscription</h3>\n          <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n            billingData.subscription.status === 'active' \n              ? 'bg-success/10 text-success' \n              : 'bg-destructive/10 text-destructive'\n          }`}>\n            {billingData.subscription.status}\n          </span>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <p className=\"text-sm text-text-secondary\">Plan</p>\n            <p className=\"text-xl font-bold text-text-primary\">{billingData.subscription.plan}</p>\n          </div>\n          <div>\n            <p className=\"text-sm text-text-secondary\">Price</p>\n            <p className=\"text-xl font-bold text-text-primary\">\n              ${billingData.subscription.price}/{billingData.subscription.billingCycle}\n            </p>\n          </div>\n          <div>\n            <p className=\"text-sm text-text-secondary\">Next Billing</p>\n            <p className=\"text-xl font-bold text-text-primary\">\n              {new Date(billingData.subscription.nextBillingDate).toLocaleDateString()}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 flex space-x-4\">\n          <Button variant=\"outline\" onClick={() => setActiveTab('plans')}>\n            Change Plan\n          </Button>\n          <Button variant=\"outline\">\n            Update Payment Method\n          </Button>\n        </div>\n      </div>\n\n      {/* Usage Overview */}\n      <div className=\"bg-card rounded-lg border border-border p-6\">\n        <h3 className=\"text-lg font-semibold text-text-primary mb-4\">Usage Overview</h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {billingData.usage.metrics.map((metric, index) => (\n            <div key={index} className=\"p-4 bg-muted rounded-lg\">\n              <p className=\"text-sm text-text-secondary\">{metric.name}</p>\n              <div className=\"mt-2\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-lg font-bold text-text-primary\">\n                    {metric.current} {metric.unit}\n                  </span>\n                  <span className=\"text-sm text-text-secondary\">\n                    / {metric.limit === 'unlimited' ? '∞' : `${metric.limit} ${metric.unit}`}\n                  </span>\n                </div>\n                {metric.limit !== 'unlimited' && (\n                  <div className=\"mt-2 w-full bg-background rounded-full h-2\">\n                    <div \n                      className=\"bg-primary h-2 rounded-full\" \n                      style={{ width: `${(metric.current / metric.limit) * 100}%` }}\n                    ></div>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Recent Payments */}\n      <div className=\"bg-card rounded-lg border border-border p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-text-primary\">Recent Payments</h3>\n          <Button variant=\"outline\" onClick={() => setActiveTab('history')}>\n            View All\n          </Button>\n        </div>\n        \n        <div className=\"space-y-3\">\n          {billingData.paymentHistory.slice(0, 3).map((payment) => (\n            <div key={payment.id} className=\"flex items-center justify-between p-3 bg-muted rounded-lg\">\n              <div>\n                <p className=\"font-medium text-text-primary\">{payment.description}</p>\n                <p className=\"text-sm text-text-secondary\">{new Date(payment.date).toLocaleDateString()}</p>\n              </div>\n              <div className=\"text-right\">\n                <p className=\"font-bold text-text-primary\">${payment.amount}</p>\n                <span className={`text-xs px-2 py-1 rounded-full ${\n                  payment.status === 'paid' \n                    ? 'bg-success/10 text-success' \n                    : 'bg-warning/10 text-warning'\n                }`}>\n                  {payment.status}\n                </span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPlansTab = () => (\n    <div className=\"space-y-6\">\n      <div className=\"text-center mb-8\">\n        <h3 className=\"text-2xl font-bold text-text-primary mb-2\">Choose Your Plan</h3>\n        <p className=\"text-text-secondary\">Select the plan that best fits your organization's needs</p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        {billingData.plans.map((plan) => (\n          <div key={plan.id} className={`relative bg-card rounded-lg border p-6 ${\n            plan.current ? 'border-primary ring-2 ring-primary/20' : 'border-border'\n          } ${plan.popular ? 'ring-2 ring-accent/20' : ''}`}>\n            {plan.popular && (\n              <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                <span className=\"bg-accent text-accent-foreground px-3 py-1 rounded-full text-xs font-medium\">\n                  Most Popular\n                </span>\n              </div>\n            )}\n            \n            <div className=\"text-center mb-6\">\n              <h4 className=\"text-xl font-bold text-text-primary\">{plan.name}</h4>\n              <div className=\"mt-2\">\n                <span className=\"text-3xl font-bold text-text-primary\">${plan.price}</span>\n                <span className=\"text-text-secondary\">/{plan.billingCycle}</span>\n              </div>\n            </div>\n\n            <ul className=\"space-y-3 mb-6\">\n              {plan.features.map((feature, index) => (\n                <li key={index} className=\"flex items-center\">\n                  <Icon name=\"Check\" size={16} className=\"text-success mr-2\" />\n                  <span className=\"text-sm text-text-secondary\">{feature}</span>\n                </li>\n              ))}\n            </ul>\n\n            <Button \n              variant={plan.current ? \"outline\" : \"default\"} \n              className=\"w-full\"\n              disabled={plan.current}\n            >\n              {plan.current ? 'Current Plan' : 'Upgrade to ' + plan.name}\n            </Button>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      <main className=\"pt-16\">\n        <div className=\"max-w-7xl mx-auto p-6\">\n          <Breadcrumb />\n          \n          {/* Page Header */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-text-primary\">Billing & Subscription</h1>\n              <p className=\"text-text-secondary mt-2\">\n                Manage your subscription, view usage, and payment history\n              </p>\n            </div>\n            \n            <Button variant=\"outline\" iconName=\"Download\">\n              Download Invoice\n            </Button>\n          </div>\n\n          {/* Tab Navigation */}\n          <div className=\"border-b border-border mb-8\">\n            <nav className=\"flex space-x-8\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-primary text-primary'\n                      : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border'\n                  }`}\n                >\n                  <Icon name={tab.icon} size={16} />\n                  <span>{tab.label}</span>\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Tab Content */}\n          {loading ? (\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n                <p className=\"text-text-secondary\">Loading billing data...</p>\n              </div>\n            </div>\n          ) : (\n            <div>\n              {activeTab === 'overview' && renderOverviewTab()}\n              {activeTab === 'plans' && renderPlansTab()}\n              {/* Other tabs would be implemented here */}\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Billing;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,UAAU,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACgB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC;IAC7CsB,YAAY,EAAE,CAAC,CAAC;IAChBC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE,CAAC,CAAC;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,UAAU,CAAC;EAEtD,MAAM4B,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAa,CAAC,EACzD;IAAEF,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAU,CAAC,EAC9D;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAY,CAAC,EAClD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAU,CAAC,EAC5D;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAS,CAAC,CAC1D;EAED9B,SAAS,CAAC,MAAM;IACd+B,YAAY,CAAC,CAAC;IACdC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAME,IAAI,GAAG,MAAM5B,WAAW,CAAC6B,cAAc,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAG,MAAM9B,WAAW,CAAC+B,WAAW,CAAC,CAAC;MAC5C,MAAMC,YAAY,GAAG,MAAMhC,WAAW,CAACiC,sBAAsB,CAAC,CAAC;MAE/D1B,cAAc,CAACqB,IAAI,CAAC;MACpBnB,WAAW,CAACqB,IAAI,CAAC;MACjBnB,sBAAsB,CAACqB,YAAY,CAAC;IACtC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMP,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACG,YAAY,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,CAAC,GAAG,MAAMiB,OAAO,CAACC,GAAG,CAAC,CACrEpC,UAAU,CAACqC,sBAAsB,CAAC,CAAC,EACnCrC,UAAU,CAACsC,iBAAiB,CAAC,CAAC,EAC9BtC,UAAU,CAACuC,eAAe,CAAC,CAAC,EAC5BvC,UAAU,CAACwC,iBAAiB,CAAC,CAAC,CAC/B,CAAC;MAEF1B,cAAc,CAAC;QACbC,YAAY,EAAEA,YAAY,IAAI0B,wBAAwB,CAAC,CAAC;QACxDzB,cAAc,EAAEA,cAAc,IAAI0B,0BAA0B,CAAC,CAAC;QAC9DzB,KAAK,EAAEA,KAAK,IAAI0B,iBAAiB,CAAC,CAAC;QACnCzB,KAAK,EAAEA,KAAK,IAAI0B,iBAAiB,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACAnB,cAAc,CAAC;QACbC,YAAY,EAAE0B,wBAAwB,CAAC,CAAC;QACxCzB,cAAc,EAAE0B,0BAA0B,CAAC,CAAC;QAC5CzB,KAAK,EAAE0B,iBAAiB,CAAC,CAAC;QAC1BzB,KAAK,EAAE0B,iBAAiB,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6B,wBAAwB,GAAGA,CAAA,MAAO;IACtCI,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,SAAS;IACvBC,eAAe,EAAE,YAAY;IAC7BC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,CACR,oBAAoB,EACpB,oBAAoB,EACpB,kBAAkB,EAClB,qBAAqB,EACrB,mBAAmB;EAEvB,CAAC,CAAC;EAEF,MAAMX,0BAA0B,GAAGA,CAAA,KAAM,CACvC;IACEpB,EAAE,EAAE,SAAS;IACbgC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,KAAK;IACbT,MAAM,EAAE,MAAM;IACdU,WAAW,EAAE,6BAA6B;IAC1CC,WAAW,EAAE;EACf,CAAC,EACD;IACEnC,EAAE,EAAE,SAAS;IACbgC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,KAAK;IACbT,MAAM,EAAE,MAAM;IACdU,WAAW,EAAE,6BAA6B;IAC1CC,WAAW,EAAE;EACf,CAAC,EACD;IACEnC,EAAE,EAAE,SAAS;IACbgC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,KAAK;IACbT,MAAM,EAAE,MAAM;IACdU,WAAW,EAAE,6BAA6B;IAC1CC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMd,iBAAiB,GAAGA,CAAA,MAAO;IAC/Be,aAAa,EAAE;MACbC,KAAK,EAAE,YAAY;MACnBC,GAAG,EAAE;IACP,CAAC;IACDC,OAAO,EAAE,CACP;MAAEC,IAAI,EAAE,cAAc;MAAEC,OAAO,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC/D;MAAEH,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAE,EAAE;MAAEC,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC,EACvE;MAAEH,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,GAAG;MAAEC,KAAK,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC,EACzD;MAAEH,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAQ,CAAC,CAClE;IACDC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMtB,iBAAiB,GAAGA,CAAA,KAAM,CAC9B;IACEtB,EAAE,EAAE,SAAS;IACbwC,IAAI,EAAE,SAAS;IACff,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,SAAS;IACvBI,QAAQ,EAAE,CACR,eAAe,EACf,aAAa,EACb,aAAa,EACb,eAAe,EACf,uBAAuB,CACxB;IACDU,OAAO,EAAE;EACX,CAAC,EACD;IACEzC,EAAE,EAAE,cAAc;IAClBwC,IAAI,EAAE,cAAc;IACpBf,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,SAAS;IACvBI,QAAQ,EAAE,CACR,gBAAgB,EAChB,oBAAoB,EACpB,eAAe,EACf,kBAAkB,EAClB,uBAAuB,EACvB,qBAAqB,CACtB;IACDU,OAAO,EAAE,IAAI;IACbI,OAAO,EAAE;EACX,CAAC,EACD;IACE7C,EAAE,EAAE,YAAY;IAChBwC,IAAI,EAAE,YAAY;IAClBf,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,SAAS;IACvBI,QAAQ,EAAE,CACR,iBAAiB,EACjB,oBAAoB,EACpB,aAAa,EACb,wBAAwB,EACxB,qBAAqB,EACrB,oBAAoB,EACpB,YAAY,EACZ,iBAAiB,CAClB;IACDU,OAAO,EAAE;EACX,CAAC,CACF;;EAED;EACA,IAAIxD,QAAQ,KAAK,OAAO,EAAE;IACxB,oBACEL,OAAA;MAAKkE,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCnE,OAAA,CAACP,eAAe;QACdY,QAAQ,EAAEA,QAAQ,CAAC+D,WAAW,CAAC,CAAE;QACjCjE,WAAW,EAAEA,WAAY;QACzBI,mBAAmB,EAAEA;MAAoB;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACFxE,OAAA;QAAMkE,SAAS,EAAC,OAAO;QAAAC,QAAA,eACrBnE,OAAA;UAAKkE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCnE,OAAA;YAAKkE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDnE,OAAA;cAAKkE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnE,OAAA,CAACL,IAAI;gBAACiE,IAAI,EAAC,MAAM;gBAACa,IAAI,EAAE,EAAG;gBAACP,SAAS,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3ExE,OAAA;gBAAIkE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFxE,OAAA;gBAAGkE,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAA4D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxGxE,OAAA,CAACJ,MAAM;gBAAC8E,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;gBAAAV,QAAA,EAAC;cAE9C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,MAAMM,iBAAiB,GAAGA,CAAA,kBACxB9E,OAAA;IAAKkE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnE,OAAA;MAAKkE,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DnE,OAAA;QAAKkE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDnE,OAAA;UAAIkE,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFxE,OAAA;UAAMkE,SAAS,EAAE,8CACfvD,WAAW,CAACE,YAAY,CAAC+B,MAAM,KAAK,QAAQ,GACxC,4BAA4B,GAC5B,oCAAoC,EACvC;UAAAuB,QAAA,EACAxD,WAAW,CAACE,YAAY,CAAC+B;QAAM;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxE,OAAA;QAAKkE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDnE,OAAA;UAAAmE,QAAA,gBACEnE,OAAA;YAAGkE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnDxE,OAAA;YAAGkE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAExD,WAAW,CAACE,YAAY,CAAC8B;UAAI;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC,eACNxE,OAAA;UAAAmE,QAAA,gBACEnE,OAAA;YAAGkE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpDxE,OAAA;YAAGkE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,GAChD,EAACxD,WAAW,CAACE,YAAY,CAACgC,KAAK,EAAC,GAAC,EAAClC,WAAW,CAACE,YAAY,CAACkC,YAAY;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNxE,OAAA;UAAAmE,QAAA,gBACEnE,OAAA;YAAGkE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3DxE,OAAA;YAAGkE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAC/C,IAAIY,IAAI,CAACpE,WAAW,CAACE,YAAY,CAACmC,eAAe,CAAC,CAACgC,kBAAkB,CAAC;UAAC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxE,OAAA;QAAKkE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCnE,OAAA,CAACJ,MAAM;UAACqF,OAAO,EAAC,SAAS;UAACP,OAAO,EAAEA,CAAA,KAAMxD,YAAY,CAAC,OAAO,CAAE;UAAAiD,QAAA,EAAC;QAEhE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxE,OAAA,CAACJ,MAAM;UAACqF,OAAO,EAAC,SAAS;UAAAd,QAAA,EAAC;QAE1B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA;MAAKkE,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DnE,OAAA;QAAIkE,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEhFxE,OAAA;QAAKkE,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClExD,WAAW,CAACI,KAAK,CAAC4C,OAAO,CAACuB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC3CpF,OAAA;UAAiBkE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAClDnE,OAAA;YAAGkE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAEgB,MAAM,CAACvB;UAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DxE,OAAA;YAAKkE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnE,OAAA;cAAKkE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnE,OAAA;gBAAMkE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAClDgB,MAAM,CAACtB,OAAO,EAAC,GAAC,EAACsB,MAAM,CAACpB,IAAI;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACPxE,OAAA;gBAAMkE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAC,IAC1C,EAACgB,MAAM,CAACrB,KAAK,KAAK,WAAW,GAAG,GAAG,GAAG,GAAGqB,MAAM,CAACrB,KAAK,IAAIqB,MAAM,CAACpB,IAAI,EAAE;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACLW,MAAM,CAACrB,KAAK,KAAK,WAAW,iBAC3B9D,OAAA;cAAKkE,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzDnE,OAAA;gBACEkE,SAAS,EAAC,6BAA6B;gBACvCmB,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAIH,MAAM,CAACtB,OAAO,GAAGsB,MAAM,CAACrB,KAAK,GAAI,GAAG;gBAAI;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAnBEY,KAAK;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA;MAAKkE,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DnE,OAAA;QAAKkE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDnE,OAAA;UAAIkE,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5ExE,OAAA,CAACJ,MAAM;UAACqF,OAAO,EAAC,SAAS;UAACP,OAAO,EAAEA,CAAA,KAAMxD,YAAY,CAAC,SAAS,CAAE;UAAAiD,QAAA,EAAC;QAElE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxE,OAAA;QAAKkE,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBxD,WAAW,CAACG,cAAc,CAACyE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACL,GAAG,CAAEM,OAAO,iBAClDxF,OAAA;UAAsBkE,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACzFnE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAGkE,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAEqB,OAAO,CAAClC;YAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtExE,OAAA;cAAGkE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAE,IAAIY,IAAI,CAACS,OAAO,CAACpC,IAAI,CAAC,CAAC4B,kBAAkB,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eACNxE,OAAA;YAAKkE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnE,OAAA;cAAGkE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GAAC,GAAC,EAACqB,OAAO,CAACnC,MAAM;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChExE,OAAA;cAAMkE,SAAS,EAAE,kCACfsB,OAAO,CAAC5C,MAAM,KAAK,MAAM,GACrB,4BAA4B,GAC5B,4BAA4B,EAC/B;cAAAuB,QAAA,EACAqB,OAAO,CAAC5C;YAAM;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAdEgB,OAAO,CAACpE,EAAE;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAef,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMiB,cAAc,GAAGA,CAAA,kBACrBzF,OAAA;IAAKkE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBnE,OAAA;MAAKkE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BnE,OAAA;QAAIkE,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/ExE,OAAA;QAAGkE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAwD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAENxE,OAAA;MAAKkE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EACnDxD,WAAW,CAACK,KAAK,CAACkE,GAAG,CAAEvC,IAAI,iBAC1B3C,OAAA;QAAmBkE,SAAS,EAAE,0CAC5BvB,IAAI,CAACkB,OAAO,GAAG,uCAAuC,GAAG,eAAe,IACtElB,IAAI,CAACsB,OAAO,GAAG,uBAAuB,GAAG,EAAE,EAAG;QAAAE,QAAA,GAC/CxB,IAAI,CAACsB,OAAO,iBACXjE,OAAA;UAAKkE,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAClEnE,OAAA;YAAMkE,SAAS,EAAC,6EAA6E;YAAAC,QAAA,EAAC;UAE9F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAEDxE,OAAA;UAAKkE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BnE,OAAA;YAAIkE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAExB,IAAI,CAACiB;UAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpExE,OAAA;YAAKkE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnE,OAAA;cAAMkE,SAAS,EAAC,sCAAsC;cAAAC,QAAA,GAAC,GAAC,EAACxB,IAAI,CAACE,KAAK;YAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3ExE,OAAA;cAAMkE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,GAAC,EAACxB,IAAI,CAACI,YAAY;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA;UAAIkE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC3BxB,IAAI,CAACQ,QAAQ,CAAC+B,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBAChCpF,OAAA;YAAgBkE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC3CnE,OAAA,CAACL,IAAI;cAACiE,IAAI,EAAC,OAAO;cAACa,IAAI,EAAE,EAAG;cAACP,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DxE,OAAA;cAAMkE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAEuB;YAAO;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAFvDY,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAELxE,OAAA,CAACJ,MAAM;UACLqF,OAAO,EAAEtC,IAAI,CAACkB,OAAO,GAAG,SAAS,GAAG,SAAU;UAC9CK,SAAS,EAAC,QAAQ;UAClByB,QAAQ,EAAEhD,IAAI,CAACkB,OAAQ;UAAAM,QAAA,EAEtBxB,IAAI,CAACkB,OAAO,GAAG,cAAc,GAAG,aAAa,GAAGlB,IAAI,CAACiB;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA,GAlCD7B,IAAI,CAACvB,EAAE;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmCZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACExE,OAAA;IAAKkE,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzCnE,OAAA,CAACP,eAAe;MACdY,QAAQ,EAAEA,QAAQ,CAAC+D,WAAW,CAAC,CAAE;MACjCjE,WAAW,EAAEA,WAAW,GAAG;QACzByD,IAAI,EAAE,GAAGzD,WAAW,CAACyF,SAAS,IAAIzF,WAAW,CAAC0F,QAAQ,EAAE;QACxDC,KAAK,EAAE3F,WAAW,CAAC2F,KAAK;QACxBC,MAAM,EAAE5F,WAAW,CAAC4F,MAAM,IAAI,2BAA2B;QACzDpE,IAAI,EAAEtB;MACR,CAAC,GAAG;QACFuD,IAAI,EAAE,YAAY;QAClBkC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,2BAA2B;QACnCpE,IAAI,EAAEtB;MACR,CAAE;MACFE,mBAAmB,EAAEA;IAAoB;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAEFxE,OAAA;MAAMkE,SAAS,EAAC,OAAO;MAAAC,QAAA,eACrBnE,OAAA;QAAKkE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCnE,OAAA,CAACN,UAAU;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGdxE,OAAA;UAAKkE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDnE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAIkE,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFxE,OAAA;cAAGkE,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAExC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENxE,OAAA,CAACJ,MAAM;YAACqF,OAAO,EAAC,SAAS;YAACe,QAAQ,EAAC,UAAU;YAAA7B,QAAA,EAAC;UAE9C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNxE,OAAA;UAAKkE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CnE,OAAA;YAAKkE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BhD,IAAI,CAAC+D,GAAG,CAAEe,GAAG,iBACZjG,OAAA;cAEE0E,OAAO,EAAEA,CAAA,KAAMxD,YAAY,CAAC+E,GAAG,CAAC7E,EAAE,CAAE;cACpC8C,SAAS,EAAE,0FACTjD,SAAS,KAAKgF,GAAG,CAAC7E,EAAE,GAChB,6BAA6B,GAC7B,oFAAoF,EACvF;cAAA+C,QAAA,gBAEHnE,OAAA,CAACL,IAAI;gBAACiE,IAAI,EAAEqC,GAAG,CAAC3E,IAAK;gBAACmD,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCxE,OAAA;gBAAAmE,QAAA,EAAO8B,GAAG,CAAC5E;cAAK;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GATnByB,GAAG,CAAC7E,EAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUL,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL/D,OAAO,gBACNT,OAAA;UAAKkE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDnE,OAAA;YAAKkE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnE,OAAA;cAAKkE,SAAS,EAAC;YAA0E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChGxE,OAAA;cAAGkE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENxE,OAAA;UAAAmE,QAAA,GACGlD,SAAS,KAAK,UAAU,IAAI6D,iBAAiB,CAAC,CAAC,EAC/C7D,SAAS,KAAK,OAAO,IAAIwE,cAAc,CAAC,CAAC;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEvC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtE,EAAA,CAhcID,OAAO;AAAAiG,EAAA,GAAPjG,OAAO;AAkcb,eAAeA,OAAO;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}