{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\card-details\\\\index.jsx\",\n  _s = $RefreshSig$();\n// ... imports\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport CardHeader from './components/CardHeader';\nimport CardDescription from './components/CardDescription';\nimport MemberAssignment from './components/MemberAssignment';\nimport DueDatePicker from './components/DueDatePicker';\nimport LabelManager from './components/LabelManager';\nimport ChecklistManager from './components/ChecklistManager';\nimport ActivityTimeline from './components/ActivityTimeline';\nimport authService from '../../utils/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CardDetails = () => {\n  _s();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const cardId = searchParams.get('id');\n\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());\n  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const [cardData, setCardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const handleTitleChange = newTitle => {\n    setCardData(prev => ({\n      ...prev,\n      title: newTitle,\n      updatedAt: new Date().toISOString()\n    }));\n  };\n  const handleDescriptionChange = newDescription => {\n    setCardData(prev => ({\n      ...prev,\n      description: newDescription,\n      updatedAt: new Date().toISOString()\n    }));\n  };\n  const handleMembersChange = newMembers => {\n    setCardData(prev => ({\n      ...prev,\n      assignedMembers: newMembers,\n      updatedAt: new Date().toISOString()\n    }));\n  };\n  const handleDueDateChange = newDueDate => {\n    setCardData(prev => ({\n      ...prev,\n      dueDate: newDueDate,\n      updatedAt: new Date().toISOString()\n    }));\n  };\n  const handleLabelsChange = newLabels => {\n    setCardData(prev => ({\n      ...prev,\n      labels: newLabels,\n      updatedAt: new Date().toISOString()\n    }));\n  };\n  const handleChecklistChange = newChecklist => {\n    setCardData(prev => ({\n      ...prev,\n      checklist: newChecklist,\n      updatedAt: new Date().toISOString()\n    }));\n  };\n  const handleAddComment = comment => {\n    console.log('New comment added:', comment);\n  };\n  const handleClose = () => navigate('/kanban-board');\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this card?')) {\n      console.log('Card deleted:', cardData.id);\n      navigate('/kanban-board');\n    }\n  };\n\n  // Load user authentication data\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResponse = await authService.getCurrentUser();\n        const orgResponse = await authService.getCurrentOrganization();\n        if (userResponse.data && userResponse.data.user) {\n          setCurrentUser(userResponse.data.user);\n          setUserRole(userResponse.data.user.role || 'member');\n        }\n        if (orgResponse.data && orgResponse.data.organization) {\n          setCurrentOrganization(orgResponse.data.organization);\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Set default values if auth fails\n        setUserRole('member');\n      }\n    };\n    loadUserData();\n  }, []);\n  useEffect(() => {\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n  if (!cardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        },\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-text-primary mb-2\",\n            children: \"Card not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary mb-4\",\n            children: \"The requested card could not be found.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"text-primary hover:underline\",\n            children: \"Return to Board\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName} ${currentUser.lastName}`,\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 z-1000 pt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-center min-h-full p-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full max-w-4xl bg-surface rounded-lg shadow-focused my-8 max-h-screen overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            card: cardData,\n            onTitleChange: handleTitleChange,\n            onClose: handleClose,\n            onDelete: handleDelete,\n            canEdit: canEdit,\n            canDelete: canDelete\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 lg:w-3/5 p-6 space-y-8\",\n              children: [/*#__PURE__*/_jsxDEV(CardDescription, {\n                card: cardData,\n                onDescriptionChange: handleDescriptionChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ChecklistManager, {\n                card: cardData,\n                onChecklistChange: handleChecklistChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ActivityTimeline, {\n                card: cardData,\n                onAddComment: handleAddComment,\n                canComment: canComment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:w-2/5 p-6 bg-muted/30 border-l border-border space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(MemberAssignment, {\n                card: cardData,\n                onMembersChange: handleMembersChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DueDatePicker, {\n                card: cardData,\n                onDueDateChange: handleDueDateChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(LabelManager, {\n                card: cardData,\n                onLabelsChange: handleLabelsChange,\n                canEdit: canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4 pt-4 border-t border-border\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-text-primary\",\n                  children: \"Card Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary\",\n                      children: \"Created:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary\",\n                      children: new Date(cardData.createdAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary\",\n                      children: \"Last updated:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary\",\n                      children: new Date(cardData.updatedAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary\",\n                      children: \"Card ID:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary font-mono text-xs\",\n                      children: [\"#\", cardData.id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), (canEdit || canDelete) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 pt-4 border-t border-border\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-text-primary text-sm\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\",\n                    children: \"Archive Card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 35\n                  }, this), canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\",\n                    children: \"Copy Card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 35\n                  }, this), canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\",\n                    children: \"Move Card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 35\n                  }, this), canDelete && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleDelete,\n                    className: \"w-full text-left px-3 py-2 text-sm text-destructive hover:bg-destructive/10 rounded-md\",\n                    children: \"Delete Card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(CardDetails, \"/Uovuxz0Usm9wE0aJzVEMByQRiw=\", false, function () {\n  return [useNavigate, useSearchParams];\n});\n_c = CardDetails;\nexport default CardDetails;\nvar _c;\n$RefreshReg$(_c, \"CardDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSearchParams", "RoleBasedHeader", "<PERSON><PERSON><PERSON><PERSON>", "CardDescription", "MemberAssignment", "DueDatePicker", "LabelManager", "ChecklistManager", "ActivityTimeline", "authService", "jsxDEV", "_jsxDEV", "CardDetails", "_s", "navigate", "searchParams", "cardId", "get", "currentUser", "setCurrentUser", "userRole", "setUserRole", "currentOrganization", "setCurrentOrganization", "canEdit", "includes", "toLowerCase", "canDelete", "canComment", "cardData", "setCardData", "isLoading", "setIsLoading", "handleTitleChange", "newTitle", "prev", "title", "updatedAt", "Date", "toISOString", "handleDescriptionChange", "newDescription", "description", "handleMembersChange", "newMembers", "assignedMembers", "handleDueDateChange", "newDueDate", "dueDate", "handleLabelsChange", "<PERSON><PERSON><PERSON><PERSON>", "labels", "handleChecklistChange", "newChecklist", "checklist", "handleAddComment", "comment", "console", "log", "handleClose", "handleDelete", "window", "confirm", "id", "loadUserData", "userResponse", "getCurrentUser", "orgResponse", "getCurrentOrganization", "data", "user", "role", "organization", "error", "document", "body", "style", "overflow", "className", "children", "name", "firstName", "lastName", "email", "avatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "card", "onTitleChange", "onClose", "onDelete", "onDescriptionChange", "onChecklistChange", "onAddComment", "onMembersChange", "onDueDateChange", "onLabelsChange", "createdAt", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/card-details/index.jsx"], "sourcesContent": ["// ... imports\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport CardHeader from './components/CardHeader';\nimport CardDescription from './components/CardDescription';\nimport MemberAssignment from './components/MemberAssignment';\nimport DueDatePicker from './components/DueDatePicker';\nimport LabelManager from './components/LabelManager';\nimport ChecklistManager from './components/ChecklistManager';\nimport ActivityTimeline from './components/ActivityTimeline';\nimport authService from '../../utils/authService';\n\nconst CardDetails = () => {\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const cardId = searchParams.get('id');\n\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n\n  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());\n  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n\n  const [cardData, setCardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const handleTitleChange = (newTitle) => {\n    setCardData(prev => ({ ...prev, title: newTitle, updatedAt: new Date().toISOString() }));\n  };\n\n  const handleDescriptionChange = (newDescription) => {\n    setCardData(prev => ({ ...prev, description: newDescription, updatedAt: new Date().toISOString() }));\n  };\n\n  const handleMembersChange = (newMembers) => {\n    setCardData(prev => ({ ...prev, assignedMembers: newMembers, updatedAt: new Date().toISOString() }));\n  };\n\n  const handleDueDateChange = (newDueDate) => {\n    setCardData(prev => ({ ...prev, dueDate: newDueDate, updatedAt: new Date().toISOString() }));\n  };\n\n  const handleLabelsChange = (newLabels) => {\n    setCardData(prev => ({ ...prev, labels: newLabels, updatedAt: new Date().toISOString() }));\n  };\n\n  const handleChecklistChange = (newChecklist) => {\n    setCardData(prev => ({ ...prev, checklist: newChecklist, updatedAt: new Date().toISOString() }));\n  };\n\n  const handleAddComment = (comment) => {\n    console.log('New comment added:', comment);\n  };\n\n  const handleClose = () => navigate('/kanban-board');\n\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this card?')) {\n      console.log('Card deleted:', cardData.id);\n      navigate('/kanban-board');\n    }\n  };\n\n  // Load user authentication data\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResponse = await authService.getCurrentUser();\n        const orgResponse = await authService.getCurrentOrganization();\n\n        if (userResponse.data && userResponse.data.user) {\n          setCurrentUser(userResponse.data.user);\n          setUserRole(userResponse.data.user.role || 'member');\n        }\n\n        if (orgResponse.data && orgResponse.data.organization) {\n          setCurrentOrganization(orgResponse.data.organization);\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Set default values if auth fails\n        setUserRole('member');\n      }\n    };\n\n    loadUserData();\n  }, []);\n\n  useEffect(() => {\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  if (!cardData) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n          currentOrganization={currentOrganization}\n        />\n        <div className=\"pt-16 flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"text-lg font-medium text-text-primary mb-2\">Card not found</div>\n            <div className=\"text-text-secondary mb-4\">The requested card could not be found.</div>\n            <button onClick={handleClose} className=\"text-primary hover:underline\">\n              Return to Board\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Modal Overlay */}\n      <div className=\"fixed inset-0 bg-black/50 z-1000 pt-16\">\n        <div className=\"flex items-start justify-center min-h-full p-4 overflow-y-auto\">\n          {/* Modal Content */}\n          <div className=\"w-full max-w-4xl bg-surface rounded-lg shadow-focused my-8 max-h-screen overflow-y-auto\">\n            {/* Card Header */}\n            <CardHeader\n              card={cardData}\n              onTitleChange={handleTitleChange}\n              onClose={handleClose}\n              onDelete={handleDelete}\n              canEdit={canEdit}\n              canDelete={canDelete}\n            />\n\n            {/* Main Content */}\n            <div className=\"flex flex-col lg:flex-row\">\n              <div className=\"flex-1 lg:w-3/5 p-6 space-y-8\">\n                <CardDescription card={cardData} onDescriptionChange={handleDescriptionChange} canEdit={canEdit} />\n                <ChecklistManager card={cardData} onChecklistChange={handleChecklistChange} canEdit={canEdit} />\n                <ActivityTimeline card={cardData} onAddComment={handleAddComment} canComment={canComment} />\n              </div>\n\n              <div className=\"lg:w-2/5 p-6 bg-muted/30 border-l border-border space-y-6\">\n                <MemberAssignment card={cardData} onMembersChange={handleMembersChange} canEdit={canEdit} />\n                <DueDatePicker card={cardData} onDueDateChange={handleDueDateChange} canEdit={canEdit} />\n                <LabelManager card={cardData} onLabelsChange={handleLabelsChange} canEdit={canEdit} />\n\n                <div className=\"space-y-4 pt-4 border-t border-border\">\n                  <h4 className=\"font-medium text-text-primary\">Card Information</h4>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Created:</span>\n                      <span className=\"text-text-primary\">{new Date(cardData.createdAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Last updated:</span>\n                      <span className=\"text-text-primary\">{new Date(cardData.updatedAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-text-secondary\">Card ID:</span>\n                      <span className=\"text-text-primary font-mono text-xs\">#{cardData.id}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {(canEdit || canDelete) && (\n                  <div className=\"space-y-2 pt-4 border-t border-border\">\n                    <h4 className=\"font-medium text-text-primary text-sm\">Actions</h4>\n                    <div className=\"space-y-2\">\n                      {canEdit && <button className=\"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\">Archive Card</button>}\n                      {canEdit && <button className=\"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\">Copy Card</button>}\n                      {canEdit && <button className=\"w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md\">Move Card</button>}\n                      {canDelete && <button onClick={handleDelete} className=\"w-full text-left px-3 py-2 text-sm text-destructive hover:bg-destructive/10 rounded-md\">Delete Card</button>}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CardDetails;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgB,YAAY,CAAC,GAAGf,eAAe,CAAC,CAAC;EACxC,MAAMgB,MAAM,GAAGD,YAAY,CAACE,GAAG,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACyB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAEpE,MAAM2B,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EAC7E,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAACF,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EACrE,MAAME,UAAU,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACH,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EAEhF,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMoC,iBAAiB,GAAIC,QAAQ,IAAK;IACtCJ,WAAW,CAACK,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEC,KAAK,EAAEF,QAAQ;MAAEG,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EAC1F,CAAC;EAED,MAAMC,uBAAuB,GAAIC,cAAc,IAAK;IAClDX,WAAW,CAACK,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEO,WAAW,EAAED,cAAc;MAAEJ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EACtG,CAAC;EAED,MAAMI,mBAAmB,GAAIC,UAAU,IAAK;IAC1Cd,WAAW,CAACK,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEU,eAAe,EAAED,UAAU;MAAEP,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EACtG,CAAC;EAED,MAAMO,mBAAmB,GAAIC,UAAU,IAAK;IAC1CjB,WAAW,CAACK,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEa,OAAO,EAAED,UAAU;MAAEV,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EAC9F,CAAC;EAED,MAAMU,kBAAkB,GAAIC,SAAS,IAAK;IACxCpB,WAAW,CAACK,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEgB,MAAM,EAAED,SAAS;MAAEb,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EAC5F,CAAC;EAED,MAAMa,qBAAqB,GAAIC,YAAY,IAAK;IAC9CvB,WAAW,CAACK,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEmB,SAAS,EAAED,YAAY;MAAEhB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EAClG,CAAC;EAED,MAAMgB,gBAAgB,GAAIC,OAAO,IAAK;IACpCC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,OAAO,CAAC;EAC5C,CAAC;EAED,MAAMG,WAAW,GAAGA,CAAA,KAAM7C,QAAQ,CAAC,eAAe,CAAC;EAEnD,MAAM8C,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChEL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE7B,QAAQ,CAACkC,EAAE,CAAC;MACzCjD,QAAQ,CAAC,eAAe,CAAC;IAC3B;EACF,CAAC;;EAED;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMkE,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,YAAY,GAAG,MAAMxD,WAAW,CAACyD,cAAc,CAAC,CAAC;QACvD,MAAMC,WAAW,GAAG,MAAM1D,WAAW,CAAC2D,sBAAsB,CAAC,CAAC;QAE9D,IAAIH,YAAY,CAACI,IAAI,IAAIJ,YAAY,CAACI,IAAI,CAACC,IAAI,EAAE;UAC/CnD,cAAc,CAAC8C,YAAY,CAACI,IAAI,CAACC,IAAI,CAAC;UACtCjD,WAAW,CAAC4C,YAAY,CAACI,IAAI,CAACC,IAAI,CAACC,IAAI,IAAI,QAAQ,CAAC;QACtD;QAEA,IAAIJ,WAAW,CAACE,IAAI,IAAIF,WAAW,CAACE,IAAI,CAACG,YAAY,EAAE;UACrDjD,sBAAsB,CAAC4C,WAAW,CAACE,IAAI,CAACG,YAAY,CAAC;QACvD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACApD,WAAW,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC;IAED2C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAENlE,SAAS,CAAC,MAAM;IACd4E,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvC,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAAChD,QAAQ,EAAE;IACb,oBACElB,OAAA;MAAKmE,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCpE,OAAA,CAACV,eAAe;QACdmB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;QACjCR,WAAW,EAAEA,WAAW,GAAG;UACzB8D,IAAI,EAAE,GAAG9D,WAAW,CAAC+D,SAAS,IAAI/D,WAAW,CAACgE,QAAQ,EAAE;UACxDC,KAAK,EAAEjE,WAAW,CAACiE,KAAK;UACxBC,MAAM,EAAElE,WAAW,CAACkE,MAAM,IAAI,2BAA2B;UACzDb,IAAI,EAAEnD;QACR,CAAC,GAAG;UACF4D,IAAI,EAAE,YAAY;UAClBG,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnCb,IAAI,EAAEnD;QACR,CAAE;QACFE,mBAAmB,EAAEA;MAAoB;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACF7E,OAAA;QAAKmE,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEpE,OAAA;UAAKmE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BpE,OAAA;YAAKmE,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChF7E,OAAA;YAAKmE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAsC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtF7E,OAAA;YAAQ8E,OAAO,EAAE9B,WAAY;YAACmB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAEvE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAKmE,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzCpE,OAAA,CAACV,eAAe;MACdmB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;MACjCR,WAAW,EAAEA,WAAW,GAAG;QACzB8D,IAAI,EAAE,GAAG9D,WAAW,CAAC+D,SAAS,IAAI/D,WAAW,CAACgE,QAAQ,EAAE;QACxDC,KAAK,EAAEjE,WAAW,CAACiE,KAAK;QACxBC,MAAM,EAAElE,WAAW,CAACkE,MAAM,IAAI,2BAA2B;QACzDb,IAAI,EAAEnD;MACR,CAAC,GAAG;QACF4D,IAAI,EAAE,YAAY;QAClBG,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,2BAA2B;QACnCb,IAAI,EAAEnD;MACR,CAAE;MACFE,mBAAmB,EAAEA;IAAoB;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGF7E,OAAA;MAAKmE,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDpE,OAAA;QAAKmE,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAE7EpE,OAAA;UAAKmE,SAAS,EAAC,yFAAyF;UAAAC,QAAA,gBAEtGpE,OAAA,CAACT,UAAU;YACTwF,IAAI,EAAE7D,QAAS;YACf8D,aAAa,EAAE1D,iBAAkB;YACjC2D,OAAO,EAAEjC,WAAY;YACrBkC,QAAQ,EAAEjC,YAAa;YACvBpC,OAAO,EAAEA,OAAQ;YACjBG,SAAS,EAAEA;UAAU;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAGF7E,OAAA;YAAKmE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCpE,OAAA;cAAKmE,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5CpE,OAAA,CAACR,eAAe;gBAACuF,IAAI,EAAE7D,QAAS;gBAACiE,mBAAmB,EAAEtD,uBAAwB;gBAAChB,OAAO,EAAEA;cAAQ;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnG7E,OAAA,CAACJ,gBAAgB;gBAACmF,IAAI,EAAE7D,QAAS;gBAACkE,iBAAiB,EAAE3C,qBAAsB;gBAAC5B,OAAO,EAAEA;cAAQ;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChG7E,OAAA,CAACH,gBAAgB;gBAACkF,IAAI,EAAE7D,QAAS;gBAACmE,YAAY,EAAEzC,gBAAiB;gBAAC3B,UAAU,EAAEA;cAAW;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eAEN7E,OAAA;cAAKmE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEpE,OAAA,CAACP,gBAAgB;gBAACsF,IAAI,EAAE7D,QAAS;gBAACoE,eAAe,EAAEtD,mBAAoB;gBAACnB,OAAO,EAAEA;cAAQ;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5F7E,OAAA,CAACN,aAAa;gBAACqF,IAAI,EAAE7D,QAAS;gBAACqE,eAAe,EAAEpD,mBAAoB;gBAACtB,OAAO,EAAEA;cAAQ;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzF7E,OAAA,CAACL,YAAY;gBAACoF,IAAI,EAAE7D,QAAS;gBAACsE,cAAc,EAAElD,kBAAmB;gBAACzB,OAAO,EAAEA;cAAQ;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEtF7E,OAAA;gBAAKmE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDpE,OAAA;kBAAImE,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnE7E,OAAA;kBAAKmE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCpE,OAAA;oBAAKmE,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCpE,OAAA;sBAAMmE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrD7E,OAAA;sBAAMmE,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAE,IAAIzC,IAAI,CAACT,QAAQ,CAACuE,SAAS,CAAC,CAACC,kBAAkB,CAAC;oBAAC;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC,eACN7E,OAAA;oBAAKmE,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCpE,OAAA;sBAAMmE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1D7E,OAAA;sBAAMmE,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAE,IAAIzC,IAAI,CAACT,QAAQ,CAACQ,SAAS,CAAC,CAACgE,kBAAkB,CAAC;oBAAC;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC,eACN7E,OAAA;oBAAKmE,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCpE,OAAA;sBAAMmE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrD7E,OAAA;sBAAMmE,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,GAAC,GAAC,EAAClD,QAAQ,CAACkC,EAAE;oBAAA;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL,CAAChE,OAAO,IAAIG,SAAS,kBACpBhB,OAAA;gBAAKmE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDpE,OAAA;kBAAImE,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClE7E,OAAA;kBAAKmE,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACvBvD,OAAO,iBAAIb,OAAA;oBAAQmE,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACjHhE,OAAO,iBAAIb,OAAA;oBAAQmE,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC9GhE,OAAO,iBAAIb,OAAA;oBAAQmE,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC9G7D,SAAS,iBAAIhB,OAAA;oBAAQ8E,OAAO,EAAE7B,YAAa;oBAACkB,SAAS,EAAC,wFAAwF;oBAAAC,QAAA,EAAC;kBAAW;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAvMID,WAAW;EAAA,QACEb,WAAW,EACLC,eAAe;AAAA;AAAAsG,EAAA,GAFlC1F,WAAW;AAyMjB,eAAeA,WAAW;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}